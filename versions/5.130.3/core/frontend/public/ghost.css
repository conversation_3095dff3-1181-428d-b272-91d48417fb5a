/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS and IE text size adjust after device orientation change,
 *    without disabling user zoom.
 */
html {
  font-family: sans-serif; /* 1 */
  -ms-text-size-adjust: 100%; /* 2 */
  -webkit-text-size-adjust: 100%; /* 2 */
}
/**
 * Remove default margin.
 */
body {
  margin: 0;
}
/* HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */
audio,
canvas,
progress,
video {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}
/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}
/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none;
}
/* Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background-color: transparent;
}
/**
 * Improve readability of focused elements when they are also in an
 * active/hover state.
 */
a:active,
a:hover {
  outline: 0;
}
/* Text-level semantics
   ========================================================================== */
/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}
/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */
b,
strong {
  font-weight: bold;
}
/**
 * Address styling not present in Safari and Chrome.
 */
dfn {
  font-style: italic;
}
/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000;
}
/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* Embedded content
   ========================================================================== */
/**
 * Remove border when inside `a` element in IE 8/9/10.
 */
img {
  border: 0;
}
/**
 * Correct overflow not hidden in IE 9/10/11.
 */
svg:not(:root) {
  overflow: hidden;
}
/* Grouping content
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari.
 */
figure {
  margin: 1em 40px;
}
/**
 * Address differences between Firefox and other browsers.
 */
hr {
  box-sizing: content-box;
  height: 0;
}
/**
 * Contain overflow in all browsers.
 */
pre {
  overflow: auto;
}
/**
 * Address odd `em`-unit font size rendering in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
/* Forms
   ========================================================================== */
/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */
/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit; /* 1 */
  font: inherit; /* 2 */
  margin: 0; /* 3 */
}
/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */
button {
  overflow: visible;
}
/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */
button,
select {
  text-transform: none;
}
/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */
button,
html input[type="button"], /* 1 */
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button; /* 2 */
  cursor: pointer; /* 3 */
}
/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}
/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
input {
  line-height: normal;
}
/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}
/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome.
 */
input[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  box-sizing: content-box; /* 2 */
}
/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */
legend {
  border: 0; /* 1 */
  padding: 0; /* 2 */
}
/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */
textarea {
  overflow: auto;
}
/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */
optgroup {
  font-weight: bold;
}
/* Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
/* Patterns: Groups of Styles
/* ---------------------------------------------------------- */
/* Global styles
/* ---------------------------------------------------------- */
/* Variables
/* ---------------------------------------------------------- */
:root {
    /* Colours */
    /* Style values */
}
/* Colour classes
/* ---------------------------------------------------------- */
.black {
    color: #15171A;
}

.darkgrey {
    color: #394047;
}

.midgrey {
    color: #7C8B9A;
}

.lightgrey {
    color: #CED4D9;
}

.blue {
    color: #14b8ff;
}

.red {
    color: #f50b23;
}

.orange {
    color: #ffb41f;
}

.green {
    color: #30cf43;
}

/* Colour classes (hover)
/* ---------------------------------------------------------- */
.darkgrey-hover:hover {
    color: #394047
}

.midgrey-hover:hover {
    color: #7C8B9A;
}

.lightgrey-hover:hover {
    color: #CED4D9;
}

.blue-hover:hover {
    color: #14b8ff;
}

.red-hover:hover {
    color: #f50b23;
}

.orange-hover:hover {
    color: #ffb41f;
}

.green-hover:hover {
    color: #30cf43;
}

/* Layout
/* ---------------------------------------------------------- */
*,
*:before,
*:after {
    box-sizing: border-box;
}
html {
    overflow: hidden;
    width: 100%;
    /* Prevent elastic scrolling on the whole page */
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    font-size: 62.5%;
    line-height: 1.65;
    letter-spacing: 0.2px;

    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
    overflow: auto;
    overflow-x: hidden;
    width: 100%;
    /* Prevent elastic scrolling on the whole page */
    height: 100%;
    color: #343f44;
    font-size: 1.4rem;
}

.gh-view {
    -ms-flex-positive: 1;
    flex-grow: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}

/* Text
/* ---------------------------------------------------------- */
h1 {
    margin: 0 0 0.3em 0;
    color: #343f44;
    line-height: 1.15em;
    text-rendering: optimizeLegibility;
    text-indent: -1px;
    font-size: 2.9rem;
}

h2 {
    margin: 0 0 0.3em 0;
    color: #343f44;
    line-height: 1.15em;
    text-rendering: optimizeLegibility;
    text-indent: -1px;
    font-size: 2.9rem;
}

@media (max-width: 500px) {
    h1 {
        font-size: 2.4rem;
    }
}
/* Inputs
/* ---------------------------------------------------------- */
.gh-input {
    display: block;
    padding: 10px 12px;
    width: 100%;
    height: 40px;
    border: rgb(214, 227, 235) 1px solid;
    border-radius: 4px;
    color: rgb(75, 91, 98);
    font-size: 1.6rem;
    line-height: 1em;
    font-weight: 300;
    -webkit-user-select: text;
       -moz-user-select: text;
        -ms-user-select: text;
            user-select: text;
    transition: border-color 0.15s linear;

    -webkit-appearance: none;
}

.gh-input:focus {
    outline: 0;
    border-color: rgb(180, 203, 218);
}

/* Buttons
/* ---------------------------------------------------------- */
/* Base button style */
/* Should only be applied to <a> tags */
.gh-btn {
    display: inline-block;
    outline: none;
    border: 1px solid rgb(214, 227, 235);
    color: rgb(130, 154, 168);
    text-decoration: none !important;
    -webkit-user-select: none;
       -moz-user-select: none;
        -ms-user-select: none;
            user-select: none;
    fill: rgb(130, 154, 168);
    border-radius: 5px;
    transition: all 0.2s ease;

    -webkit-font-smoothing: subpixel-antialiased;
}
/* ALL buttons must have a span for content */
.gh-btn span {
    display: block;
    padding: 0 12px;
    height: 33px;
    font-size: 1.3rem;
    line-height: 33px;
    font-weight: 400;
    text-align: center;
    letter-spacing: 0.2px;
    border-radius: 4px;
}
.gh-btn:hover {
    border-color: rgb(180, 203, 218);
}

/* Button highlights
/* ---------------------------------------------------------- */
.gh-btn-hover-blue:hover {
    border-color: #3eb0ef;
    color: #3eb0ef;
}

/* Blue button
/* ---------------------------------------------------------- */
/* The background of the button creates 1px gradient border */
.gh-btn-blue {
    padding: 1px;
    border: 0;
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0,0,0,0.1);
    fill: #fff;
    background: linear-gradient(
    rgb(61, 161, 214),
    rgb(34, 136, 191)
    );
    box-shadow: 0 1px 0 rgba(0,0,0,0.12);
    transition: none !important;
}
/* The background of the span is the main visual element */
.gh-btn-blue span {
    background: linear-gradient(
    rgb(74, 182, 240),
    rgb(47, 165, 228) 60%,
    rgb(47, 165, 228) 90%,
    rgb(56, 169, 229)
    );
    box-shadow: 0 1px 0 rgba(255,255,255,0.1) inset;
}
/* When clicked or focused with keyboard */
.gh-btn-blue:active,
.gh-btn-blue:focus {
    background: rgb(30, 120, 169);
}
.gh-btn-blue:active span,
.gh-btn-blue:focus span {
    background: rgb(41, 160, 224);
    box-shadow: none;
}
/* Special Buttons
/* ---------------------------------------------------------- */
.gh-btn-block {
    display: block;
    width: 100%;
}

/* Input Icons
/* ---------------------------------------------------------- */

.gh-input-icon {
    position: relative;
    display: block;
}

.gh-input-icon svg {
    position: absolute;
    top: 50%;
    left: 10px;
    z-index: 2;
    height: 14px;
    width: auto;
    fill: color(var(--midgrey) l(+15%));
    transform: translateY(-7px);
}

.gh-input-icon input {
    padding-left: 35px;
}

/* Layouts: Groups of Components
/* ---------------------------------------------------------- */
/* Global Layout
/* ---------------------------------------------------------- */

/* Main viewport, contains main content, and alerts */
.gh-app {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
        flex-direction: column;
    overflow: hidden;
    height: 100%;
}

/* Content viewport, contains everything else */
.gh-viewport {
    -ms-flex-positive: 1;
        flex-grow: 1;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    max-height: 100%;
}

.gh-main {
    position: relative;
    -ms-flex-positive: 1;
        flex-grow: 1;
    display: -ms-flexbox;
    display: flex;
    background: #fff;
    overflow-y: auto;
}

/* Full screen workflow
/* ---------------------------------------------------------- */
.gh-flow {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    min-height: 100%;
    background: linear-gradient(315deg,#efefef,#fff);
}

.gh-flow:has(.gh-flow-content.private) {
    background: #fff;
}

.gh-flow-content-wrap {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    margin: 0 24px;
    padding-bottom: 8vh;
}

.gh-flow-content-wrap .site-icon {
    width: 70px;
    height: 70px;
    border-radius: 3px;
}

.gh-flow-content {
    display: flex;
    flex-direction: column;
    max-width: 520px;
    width: 100%;
    margin: 4rem 0 6rem;
    padding: 40px;
    background: #fff;
    color: var(--darkgrey);
    font-size: 1.9rem;
    line-height: 1.5em;
    font-weight: 300;
    border-radius: 3px;
    box-shadow:
        0 2.8px 2.2px rgba(0,0,0,.02),
        0 6.7px 5.3px rgba(0,0,0,.02),
        0 12.5px 10px rgba(0,0,0,.02),
        0 22.3px 17.9px rgba(0,0,0,.03),
        0 41.8px 33.4px rgba(0,0,0,.03),
        0 100px 80px rgba(0,0,0,.05);
}

.gh-flow-content.unsubscribe {
        align-items: center;
        justify-content: center;
        max-width: 560px;
        min-height: 200px;
        margin: 4rem 0;
        text-align: center;
}

.gh-flow-content.private {
    box-shadow: none;
}

@media (max-width: 500px) {
    .gh-flow-content {
        padding: 0;
        background: transparent;
        box-shadow: none;
    }
}

.gh-flow-content header {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.gh-flow-content h1 {
    margin-bottom: 24px;
    color: #15171A;
    font-size: 4.1rem;
    font-weight: 700;
    line-height: 1.15em;
}

.gh-flow-content.unsubscribe h1 {
    font-size: 3.2rem;
}

@media (max-width: 600px) {
    .gh-flow-content h1,
    .gh-flow-content.unsubscribe h1 {
        font-size: 6vw;
    }
}

.gh-flow-content.unsubscribe p {
    margin: 0 0 .4em;
    color: #394047;
    font-size: 1.8rem;
}

@media (max-width: 500px) {
    .gh-flow-content.unsubscribe p {
        font-size: 1.6rem;
        line-height: 1.5;
    }
}

.gh-flow-content .gh-btn {
    display: block;
    margin: 20px auto 0;
    max-width: 400px;
}

.gh-flow-content .form-group {
    position: relative;
    margin-bottom: 2.5rem;
}

.gh-flow-content .form-group.error .gh-input {
    border-color: #f50b23;
    box-shadow: 0 0 0 3px rgba(239,24,24,.15);
}

.gh-flow-content .main-error {
    position: absolute;
    right: 0;
    margin: 0;
    color: #7C8B9A;
    font-size: 1.35rem;
    font-weight: 400;
    text-align: center;
    user-select: text;
}

.gh-flow-em {
    font-weight: 500;
}

.unsubscribe-footer {
    text-align: center;
    font-size: 1.5rem;
}

@media (max-width: 500px) {
    .unsubscribe-footer {
        padding: 0 24px;
        font-size: 1.4rem;
        line-height: 1.4em;
    }
}

.unsubscribe-footer p {
    color: #7C8B9A;
    margin: 0 0 .4rem;
}

.unsubscribe-footer a {
    color: #15171A;
    text-decoration: none;
}

.unsubscribe-footer a:hover {
    text-decoration: underline;
}

/* Sign in
/* ---------------------------------------------------------- */
.gh-signin {
    margin-bottom: 1.5rem;
}

.gh-signin .gh-input,
.gh-signin .gh-input:-webkit-autofill::first-line {
    height: 54px;
    padding: 12px 16px;
    font-size: 1.8rem;
    border-radius: 8px;
    border: 1px solid #f3f6f7;
    background: #f3f6f7;
    color: var(--black);
}

.gh-signin .gh-input::placeholder {
    color: #abb4be;
    font-weight: 400;
    opacity: 1;
}

.gh-signin .gh-input::-webkit-input-placeholder {
    color: #abb4be;
    font-weight: 400;
}

.gh-signin .gh-input:-ms-input-placeholder {
    color: #abb4be;
    font-weight: 400;
}

.gh-signin .gh-input::-moz-placeholder {
    color: #abb4be;
    font-weight: 400;
    opacity: 1;
}

.gh-signin .gh-input:focus {
    border-color: #30cf43;
    box-shadow: 0 0 0 3px rgba(48,207,67,.25);
}

.gh-signin .gh-btn {
    margin: 0;
}

.gh-signin .gh-btn {
    width: 100%;
    height: 54px;
    max-width: unset;
    margin-top: 32px;
    background: #15171A;
    font-weight: 300;
    line-height: 54px;
    border-radius: 8px;
    transition: all 0.4s ease;
    -webkit-font-smoothing: subpixel-antialiased;
}

.gh-signin .gh-btn span {
    color: #fff;
    font-size: 1.8rem;
}

/* Error /ghost/404/
/* ---------------------------------------------------------- */

.error-content {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: text;
    padding: 8vw;
}

.error-details {
    display: flex;
    align-items: center;
    margin-bottom: 4rem;
}

.error-ghost {
    margin: 15px;
    height: 115px;
}

@media (max-width: 630px) {
    .error-ghost {
        display: none;
    }
}

.error-code {
    margin: 0;
    color: #C5D2D9;
    font-size: 10vw;
    font-weight: 600;
    line-height: 0.9em;
    letter-spacing: -0.4vw;
}


.error-description {
    margin: 0;
    padding: 0;
    border: none;
    color: #54666D;
    font-size: 2.3rem;
    font-weight: 300;
    line-height: 1.3em;
}

.error-message {
    display: flex;
    flex-direction: column;
    margin: 15px;
    align-items: center;
}

.error-message a {
    font-size: 1.4rem;
    line-height: 1;
    margin: 8px 0;
}

.error-link {
    background-color: transparent;
    color: #5ba4e5;
    transition: background .3s,color .3s;
    text-decoration: none;
}

/* Stack trace
/* ---------------------------------------------------------- */

.error-stack {
    margin: 1rem auto;
    padding: 2rem;
    max-width: 800px;
    background-color: rgba(255, 255, 255, 0.3);
}

.error-stack-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.error-stack-list li {
    display: block;
}

.error-stack-list li:before {
    content: "\21AA";
    display: inline-block;
    margin-right: 0.5rem;
    color: #bbb;
    font-size: 1.2rem;
}

.error-stack-function {
    font-weight: bold;
}
