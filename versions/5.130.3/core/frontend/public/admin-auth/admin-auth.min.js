"use strict";const adminUrl=window.location.href.replace("auth-frame/","")+"api/admin",siteOrigin="{{SITE_ORIGIN}}";window.addEventListener("message",async function(a){if(a.origin!==siteOrigin){console.warn("Ignored message to admin auth iframe because of mismatch in origin","expected",siteOrigin,"got",a.origin,"with data",a.data);return}let n=null;try{n=JSON.parse(a.data)}catch(t){console.error(t)}function e(t,s){a.source.postMessage(JSON.stringify({uid:n.uid,error:t,result:s}),siteOrigin)}if(n.action==="browseComments")try{const{postId:t,params:s}=n,o=await(await fetch(adminUrl+`/comments/post/${t}/?${new URLSearchParams(s).toString()}`)).json();e(null,o)}catch(t){e(t,null)}if(n.action==="getReplies")try{const{commentId:t,params:s}=n,o=await(await fetch(adminUrl+`/comments/${t}/replies/?${new URLSearchParams(s).toString()}`)).json();e(null,o)}catch(t){e(t,null)}if(n.action==="readComment")try{const{commentId:t,params:s}=n,o=await(await fetch(adminUrl+"/comments/"+t+"/?"+new URLSearchParams(s).toString())).json();e(null,o)}catch(t){e(t,null)}if(n.action==="getUser")try{const s=await(await fetch(adminUrl+"/users/me/?include=roles")).json();e(null,s)}catch(t){e(t,null)}if(n.action==="hideComment")try{const s=await(await fetch(adminUrl+"/comments/"+n.id+"/",{method:"PUT",body:JSON.stringify({comments:[{id:n.id,status:"hidden"}]}),headers:{"Content-Type":"application/json"}})).json();e(null,s)}catch(t){e(t,null)}if(n.action==="showComment")try{const s=await(await fetch(adminUrl+"/comments/"+n.id+"/",{method:"PUT",body:JSON.stringify({comments:[{id:n.id,status:"published"}]}),headers:{"Content-Type":"application/json"}})).json();e(null,s)}catch(t){e(t,null)}});
