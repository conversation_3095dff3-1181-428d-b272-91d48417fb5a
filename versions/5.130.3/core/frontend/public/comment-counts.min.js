"use strict";(async function(){const a={},c=new Set,d=document.querySelector("[data-ghost-comments-counts-api]").dataset.ghostCommentsCountsApi,i=function(o,e=100){let n;return(...t)=>{clearTimeout(n),n=setTimeout(()=>o.apply(this,t),e)}},m=function(o){(o.querySelectorAll?.("[data-ghost-comment-count]")||[]).forEach(n=>{a[n.dataset.ghostCommentCount]||c.add(n.dataset.ghostCommentCount)})},l=function(){for(const[o,e]of Object.entries(a))document.querySelectorAll(`[data-ghost-comment-count="${o}"]`).forEach(t=>{let s=t.dataset.ghostCommentCountEmpty;if(e===1&&(t.dataset.ghostCommentCountSingular?s=`${e} ${t.dataset.ghostCommentCountSingular}`:s=e),e>1&&(t.dataset.ghostCommentCountPlural?s=`${e} ${t.dataset.ghostCommentCountPlural}`:s=e),s)if(t.dataset.ghostCommentCountAutowrap!=="false"){const u=document.createElement(t.dataset.ghostCommentCountTag);t.dataset.ghostCommentCountClassName&&u.classList.add(t.dataset.ghostCommentCountClassName),u.textContent=s,t.insertAdjacentElement("afterend",u)}else t.insertAdjacentText("afterend",s);t.remove()})},r=async function(){const o=Array.from(c);if(c.clear(),!o.length)return;const e=await fetch(`${d}?ids=${o.join(",")}`,{method:"GET",headers:{Accept:"application/json","Content-Type":"application/json"}});if(e.status!==200)return;const n=await e.json();for(const[t,s]of Object.entries(n))a[t]=s;l()};new MutationObserver(o=>{o.forEach(e=>{e.addedNodes.forEach(n=>{m(n),i(r)})})}).observe(document.body,{subtree:!0,childList:!0}),m(document.body),r()})();
