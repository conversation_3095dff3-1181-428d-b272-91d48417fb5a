.ember-basic-dropdown {
  position: relative;
}

.ember-basic-dropdown,
.ember-basic-dropdown-content,
.ember-basic-dropdown-content * {
  box-sizing: border-box;
}

.ember-basic-dropdown-content {
  position: absolute;
  width: auto;
  z-index: 1000;
  background-color: #fff;
}

.ember-basic-dropdown-content--left {
  left: 0;
}

.ember-basic-dropdown-content--right {
  right: 0;
}

.ember-basic-dropdown-overlay {
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  z-index: 10;
  top: 0;
  left: 0;
  pointer-events: none;
}

.ember-basic-dropdown-content-wormhole-origin {
  display: inline;
}
.ember-basic-dropdown {
  position: relative; }

.ember-basic-dropdown, .ember-basic-dropdown-content, .ember-basic-dropdown-content * {
  box-sizing: border-box; }

.ember-basic-dropdown-content {
  position: absolute;
  width: auto;
  z-index: 1000;
  background-color: #ffffff; }

.ember-basic-dropdown-content--left {
  left: 0; }

.ember-basic-dropdown-content--right {
  right: 0; }

.ember-basic-dropdown-overlay {
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  z-index: 10;
  top: 0;
  left: 0;
  pointer-events: none; }

.ember-basic-dropdown-content-wormhole-origin {
  display: inline; }

.ember-power-select-dropdown * {
  box-sizing: border-box; }

.ember-power-select-trigger {
  position: relative;
  border-top: 1px solid #aaaaaa;
  border-bottom: 1px solid #aaaaaa;
  border-right: 1px solid #aaaaaa;
  border-left: 1px solid #aaaaaa;
  border-radius: 4px;
  background-color: #ffffff;
  line-height: 1.75;
  overflow-x: hidden;
  text-overflow: ellipsis;
  min-height: 1.75em;
  user-select: none;
  -webkit-user-select: none;
  color: inherit;
  /* Minimum clearfix for modern browsers */ }
  .ember-power-select-trigger:after {
    content: "";
    display: table;
    clear: both; }

.ember-power-select-trigger:focus,
.ember-power-select-trigger--active {
  border-top: 1px solid #aaaaaa;
  border-bottom: 1px solid #aaaaaa;
  border-right: 1px solid #aaaaaa;
  border-left: 1px solid #aaaaaa;
  box-shadow: none; }

.ember-basic-dropdown-trigger--below.ember-power-select-trigger[aria-expanded="true"],
.ember-basic-dropdown-trigger--in-place.ember-power-select-trigger[aria-expanded="true"] {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.ember-basic-dropdown-trigger--above.ember-power-select-trigger[aria-expanded="true"] {
  border-top-left-radius: 0;
  border-top-right-radius: 0; }

.ember-power-select-placeholder {
  color: #999999;
  display: block;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; }

.ember-power-select-status-icon {
  position: absolute;
  display: inline-block;
  width: 0;
  height: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  border-style: solid;
  border-width: 7px 4px 0 4px;
  border-color: #aaaaaa transparent transparent transparent; }
  .ember-basic-dropdown-trigger[aria-expanded="true"] .ember-power-select-status-icon {
    transform: rotate(180deg); }

.ember-power-select-clear-btn {
  position: absolute;
  cursor: pointer; }

.ember-power-select-trigger-multiple-input {
  font-family: inherit;
  font-size: inherit;
  border: none;
  display: inline-block;
  line-height: inherit;
  -webkit-appearance: none;
  outline: none;
  padding: 0;
  float: left;
  background-color: transparent;
  text-indent: 2px;
  /* There's a browser bug where this selectos cannot be chained with commas */ }
  .ember-power-select-trigger-multiple-input:disabled {
    background-color: #eeeeee; }
  .ember-power-select-trigger-multiple-input::placeholder {
    opacity: 1;
    color: #999999; }
  .ember-power-select-trigger-multiple-input::-webkit-input-placeholder {
    opacity: 1;
    color: #999999; }
  .ember-power-select-trigger-multiple-input::-moz-placeholder {
    opacity: 1;
    color: #999999; }
  .ember-power-select-trigger-multiple-input::-ms-input-placeholder {
    opacity: 1;
    color: #999999; }

.ember-power-select-multiple-options {
  padding: 0;
  margin: 0; }

.ember-power-select-multiple-option {
  border: 1px solid gray;
  border-radius: 4px;
  color: #333333;
  background-color: #e4e4e4;
  padding: 0 4px;
  display: inline-block;
  line-height: 1.45;
  float: left;
  margin: 2px 0 2px 3px; }

.ember-power-select-multiple-remove-btn {
  cursor: pointer; }
  .ember-power-select-multiple-remove-btn:not(:hover) {
    opacity: 0.5; }

.ember-power-select-search {
  padding: 4px; }

.ember-power-select-search-input {
  border: 1px solid #aaaaaa;
  border-radius: 0;
  width: 100%;
  font-size: inherit;
  line-height: inherit;
  padding: 0 5px; }
  .ember-power-select-search-input:focus {
    border: 1px solid #aaaaaa;
    box-shadow: none; }

.ember-power-select-dropdown {
  border-left: 1px solid #aaaaaa;
  border-right: 1px solid #aaaaaa;
  line-height: 1.75;
  border-radius: 4px;
  box-shadow: none;
  overflow: hidden;
  color: inherit; }

.ember-power-select-dropdown.ember-basic-dropdown-content--above {
  border-top: 1px solid #aaaaaa;
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0; }

.ember-power-select-dropdown.ember-basic-dropdown-content--below, .ember-power-select-dropdown.ember-basic-dropdown-content--in-place {
  border-top: none;
  border-bottom: 1px solid #aaaaaa;
  border-top-left-radius: 0;
  border-top-right-radius: 0; }

.ember-power-select-dropdown.ember-basic-dropdown-content--in-place {
  width: 100%; }

.ember-power-select-options {
  list-style: none;
  margin: 0;
  padding: 0;
  user-select: none;
  -webkit-user-select: none; }
  .ember-power-select-options[role="listbox"] {
    overflow-y: auto;
    /* in firefox in windows this can cause a word-break issue. Try `overflow-y: scroll` if that happens */
    -webkit-overflow-scrolling: touch;
    max-height: 12.25em; }

.ember-power-select-option {
  cursor: pointer;
  padding: 0 8px; }

.ember-power-select-group[aria-disabled="true"] {
  color: #999999;
  cursor: not-allowed; }

.ember-power-select-group[aria-disabled="true"] .ember-power-select-option,
.ember-power-select-option[aria-disabled="true"] {
  color: #999999;
  pointer-events: none;
  cursor: not-allowed; }

.ember-power-select-option[aria-selected="true"] {
  background-color: #dddddd; }

.ember-power-select-option[aria-current="true"] {
  background-color: #5897fb;
  color: #ffffff; }

.ember-power-select-group-name {
  cursor: default;
  font-weight: bold; }

.ember-power-select-trigger[aria-disabled=true] {
  background-color: #eeeeee; }

.ember-power-select-trigger {
  padding: 0 16px 0 0; }

.ember-power-select-selected-item, .ember-power-select-placeholder {
  margin-left: 8px; }

.ember-power-select-status-icon {
  right: 5px; }

.ember-power-select-clear-btn {
  right: 25px; }

.ember-power-select-group .ember-power-select-group .ember-power-select-group-name {
  padding-left: 24px; }

.ember-power-select-group .ember-power-select-group .ember-power-select-option {
  padding-left: 40px; }

.ember-power-select-group .ember-power-select-option {
  padding-left: 24px; }

.ember-power-select-group .ember-power-select-group-name {
  padding-left: 8px; }

.ember-power-select-trigger[dir=rtl] {
  padding: 0 0 0 16px; }
  .ember-power-select-trigger[dir=rtl] .ember-power-select-selected-item, .ember-power-select-trigger[dir=rtl] .ember-power-select-placeholder {
    margin-right: 8px; }
  .ember-power-select-trigger[dir=rtl] .ember-power-select-multiple-option {
    float: right; }
  .ember-power-select-trigger[dir=rtl] .ember-power-select-trigger-multiple-input {
    float: right; }
  .ember-power-select-trigger[dir=rtl] .ember-power-select-status-icon {
    left: 5px;
    right: initial; }
  .ember-power-select-trigger[dir=rtl] .ember-power-select-clear-btn {
    left: 25px;
    right: initial; }

.ember-power-select-dropdown[dir=rtl] .ember-power-select-group .ember-power-select-group .ember-power-select-group-name {
  padding-right: 24px; }

.ember-power-select-dropdown[dir=rtl] .ember-power-select-group .ember-power-select-group .ember-power-select-option {
  padding-right: 40px; }

.ember-power-select-dropdown[dir=rtl] .ember-power-select-group .ember-power-select-option {
  padding-right: 24px; }

.ember-power-select-dropdown[dir=rtl] .ember-power-select-group .ember-power-select-group-name {
  padding-right: 8px; }

.liquid-container {
    position: relative;
    overflow: hidden;

    /* without this, overflow:hidden won't take effect because the things
       we're trying to hide are on a separate accelerated
       context. Also, this prevents a tiny vertical jump when the
       content switches to accelerated.  */
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    transform: translateY(0);
}

.liquid-child {
    overflow: hidden; /* Prevent margin collapse */
}

/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */

/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS and IE text size adjust after device orientation change,
 *    without disabling user zoom.
 */

html {
  font-family: sans-serif; /* 1 */
  -ms-text-size-adjust: 100%; /* 2 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/**
 * Remove default margin.
 */

body {
  margin: 0;
}

/* HTML5 display definitions
   ========================================================================== */

/**
 * Correct `block` display not defined for any HTML5 element in IE 8/9.
 * Correct `block` display not defined for `details` or `summary` in IE 10/11
 * and Firefox.
 * Correct `block` display not defined for `main` in IE 11.
 */

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

/**
 * 1. Correct `inline-block` display not defined in IE 8/9.
 * 2. Normalize vertical alignment of `progress` in Chrome, Firefox, and Opera.
 */

audio,
canvas,
progress,
video {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */

audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Address `[hidden]` styling not present in IE 8/9/10.
 * Hide the `template` element in IE 8/9/10/11, Safari, and Firefox < 22.
 */

[hidden],
template {
  display: none;
}

/* Links
   ========================================================================== */

/**
 * Remove the gray background color from active links in IE 10.
 */

a {
  background-color: transparent;
}

/**
 * Improve readability of focused elements when they are also in an
 * active/hover state.
 */

a:active,
a:hover {
  outline: 0;
}

/* Text-level semantics
   ========================================================================== */

/**
 * Address styling not present in IE 8/9/10/11, Safari, and Chrome.
 */

abbr[title] {
  border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari, and Chrome.
 */

b,
strong {
  font-weight: bold;
}

/**
 * Address styling not present in Safari and Chrome.
 */

dfn {
  font-style: italic;
}

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari, and Chrome.
 */

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/**
 * Address styling not present in IE 8/9.
 */

mark {
  background: #ff0;
  color: #000;
}

/**
 * Address inconsistent and variable font size in all browsers.
 */

small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* Embedded content
   ========================================================================== */

/**
 * Remove border when inside `a` element in IE 8/9/10.
 */

img {
  border: 0;
}

/**
 * Correct overflow not hidden in IE 9/10/11.
 */

svg:not(:root) {
  overflow: hidden;
}

/* Grouping content
   ========================================================================== */

/**
 * Address margin not present in IE 8/9 and Safari.
 */

figure {
  margin: 1em 40px;
}

/**
 * Address differences between Firefox and other browsers.
 */

hr {
  box-sizing: content-box;
  height: 0;
}

/**
 * Contain overflow in all browsers.
 */

pre {
  overflow: auto;
}

/**
 * Address odd `em`-unit font size rendering in all browsers.
 */

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

/* Forms
   ========================================================================== */

/**
 * Known limitation: by default, Chrome and Safari on OS X allow very limited
 * styling of `select`, unless a `border` property is set.
 */

/**
 * 1. Correct color not being inherited.
 *    Known issue: affects color of disabled elements.
 * 2. Correct font properties not being inherited.
 * 3. Address margins set differently in Firefox 4+, Safari, and Chrome.
 */

button,
input,
optgroup,
select,
textarea {
  color: inherit; /* 1 */
  font: inherit; /* 2 */
  margin: 0; /* 3 */
}

/**
 * Address `overflow` set to `hidden` in IE 8/9/10/11.
 */

button {
  overflow: visible;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Firefox, IE 8/9/10/11, and Opera.
 * Correct `select` style inheritance in Firefox.
 */

button,
select {
  text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */

button,
html input[type="button"], /* 1 */
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button; /* 2 */
  cursor: pointer; /* 3 */
}

/**
 * Re-set default cursor for disabled elements.
 */

button[disabled],
html input[disabled] {
  cursor: default;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */

input {
  line-height: normal;
}

/**
 * It's recommended that you don't attempt to style these elements.
 * Firefox's implementation doesn't respect box-sizing, padding, or width.
 *
 * 1. Address box sizing set to `content-box` in IE 8/9/10.
 * 2. Remove excess padding in IE 8/9/10.
 */

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Fix the cursor style for Chrome's increment/decrement buttons. For certain
 * `font-size` values of the `input`, it causes the cursor style of the
 * decrement button to change from `default` to `text`.
 */

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari and Chrome.
 */

input[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  box-sizing: content-box; /* 2 */
}

/**
 * Remove inner padding and search cancel button in Safari and Chrome on OS X.
 * Safari (but not Chrome) clips the cancel button when the search input has
 * padding (and `textfield` appearance).
 */

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * Define consistent border, margin, and padding.
 */

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9/10/11.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */

legend {
  border: 0; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Remove default vertical scrollbar in IE 8/9/10/11.
 */

textarea {
  overflow: auto;
}

/**
 * Don't inherit the `font-weight` (applied by a rule above).
 * NOTE: the default cannot safely be changed in Chrome and Safari on OS X.
 */

optgroup {
  font-weight: bold;
}

/* Tables
   ========================================================================== */

/**
 * Remove most spacing between table cells.
 */

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/* BASICS */

.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  font-family: monospace;
  height: 300px;
  color: black;
  direction: ltr;
}

/* PADDING */

.CodeMirror-lines {
  padding: 4px 0; /* Vertical padding around content */
}
.CodeMirror pre {
  padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}
.CodeMirror-linenumbers {}
.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker { color: black; }
.CodeMirror-guttermarker-subtle { color: #999; }

/* CURSOR */

.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}
/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}
.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}
.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}
.cm-fat-cursor-mark {
  background-color: rgba(20, 255, 20, 0.5);
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
}
.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}
@-moz-keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}
@-webkit-keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}
@keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}

/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror-overwrite .CodeMirror-cursor {}

.cm-tab { display: inline-block; text-decoration: inherit; }

.CodeMirror-rulers {
  position: absolute;
  left: 0; right: 0; top: -50px; bottom: -20px;
  overflow: hidden;
}
.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0; bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */

.cm-s-default .cm-header {color: blue;}
.cm-s-default .cm-quote {color: #090;}
.cm-negative {color: #d44;}
.cm-positive {color: #292;}
.cm-header, .cm-strong {font-weight: bold;}
.cm-em {font-style: italic;}
.cm-link {text-decoration: underline;}
.cm-strikethrough {text-decoration: line-through;}

.cm-s-default .cm-keyword {color: #708;}
.cm-s-default .cm-atom {color: #219;}
.cm-s-default .cm-number {color: #164;}
.cm-s-default .cm-def {color: #00f;}
.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {}
.cm-s-default .cm-variable-2 {color: #05a;}
.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #085;}
.cm-s-default .cm-comment {color: #a50;}
.cm-s-default .cm-string {color: #a11;}
.cm-s-default .cm-string-2 {color: #f50;}
.cm-s-default .cm-meta {color: #555;}
.cm-s-default .cm-qualifier {color: #555;}
.cm-s-default .cm-builtin {color: #30a;}
.cm-s-default .cm-bracket {color: #997;}
.cm-s-default .cm-tag {color: #170;}
.cm-s-default .cm-attribute {color: #00c;}
.cm-s-default .cm-hr {color: #999;}
.cm-s-default .cm-link {color: #00c;}

.cm-s-default .cm-error {color: #f00;}
.cm-invalidchar {color: #f00;}

.CodeMirror-composing { border-bottom: 2px solid; }

/* Default styles for common addons */

div.CodeMirror span.CodeMirror-matchingbracket {color: #0b0;}
div.CodeMirror span.CodeMirror-nonmatchingbracket {color: #a22;}
.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }
.CodeMirror-activeline-background {background: #e8f2ff;}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important; /* Things will break if this is overridden */
  /* 30px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -30px; margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: none; /* Prevent dragging from highlighting the element */
  position: relative;
}
.CodeMirror-sizer {
  position: relative;
  border-right: 30px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
}
.CodeMirror-vscrollbar {
  right: 0; top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}
.CodeMirror-hscrollbar {
  bottom: 0; left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}
.CodeMirror-scrollbar-filler {
  right: 0; bottom: 0;
}
.CodeMirror-gutter-filler {
  left: 0; bottom: 0;
}

.CodeMirror-gutters {
  position: absolute; left: 0; top: 0;
  min-height: 100%;
  z-index: 3;
}
.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -30px;
}
.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: none !important;
  border: none !important;
}
.CodeMirror-gutter-background {
  position: absolute;
  top: 0; bottom: 0;
  z-index: 4;
}
.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}
.CodeMirror-gutter-wrapper ::selection { background-color: transparent }
.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }

.CodeMirror-lines {
  cursor: text;
  min-height: 1px; /* prevents collapsing before first draw */
}
.CodeMirror pre {
  /* Reset some styles that the rest of the page might have set */
  -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: contextual;
  font-variant-ligatures: contextual;
}
.CodeMirror-wrap pre {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  padding: 0.1px; /* Force widget margins to stay inside of the container */
}

.CodeMirror-widget {}

.CodeMirror-rtl pre { direction: rtl; }

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}
.CodeMirror-measure pre { position: static; }

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}
div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected { background: #d9d9d9; }
.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }
.CodeMirror-crosshair { cursor: crosshair; }
.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }
.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }

.cm-searching {
  background-color: #ffa;
  background-color: rgba(255, 255, 0, .4);
}

/* Used to force a border model for a node */
.cm-force-border { padding-right: .1px; }

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}

/* See issue #2901 */
.cm-tab-wrap-hack:after { content: ''; }

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext { background: none; }

/*
Copyright (C) 2011 by MarkLogic Corporation
Author: Mike Brevoort <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/
.cm-s-xq-light span.cm-keyword { line-height: 1em; font-weight: bold; color: #5A5CAD; }
.cm-s-xq-light span.cm-atom { color: #6C8CD5; }
.cm-s-xq-light span.cm-number { color: #164; }
.cm-s-xq-light span.cm-def { text-decoration:underline; }
.cm-s-xq-light span.cm-variable { color: black; }
.cm-s-xq-light span.cm-variable-2 { color:black; }
.cm-s-xq-light span.cm-variable-3, .cm-s-xq-light span.cm-type { color: black; }
.cm-s-xq-light span.cm-property {}
.cm-s-xq-light span.cm-operator {}
.cm-s-xq-light span.cm-comment { color: #0080FF; font-style: italic; }
.cm-s-xq-light span.cm-string { color: red; }
.cm-s-xq-light span.cm-meta { color: yellow; }
.cm-s-xq-light span.cm-qualifier { color: grey; }
.cm-s-xq-light span.cm-builtin { color: #7EA656; }
.cm-s-xq-light span.cm-bracket { color: #cc7; }
.cm-s-xq-light span.cm-tag { color: #3F7F7F; }
.cm-s-xq-light span.cm-attribute { color: #7F007F; }
.cm-s-xq-light span.cm-error { color: #f00; }

.cm-s-xq-light .CodeMirror-activeline-background { background: #e8f2ff; }
.cm-s-xq-light .CodeMirror-matchingbracket { outline:1px solid grey;color:black !important;background:yellow; }

.occluded-content {
  display: block;
  position: relative;
  width: 100%;

  /* prevents margin overflow on item container */
  min-height: 0.01px;

  /* hides text visually while still being readable by screen readers */
  color: rgba(0,0,0,0);
}

table .occluded-content,
tbody .occluded-content,
thead .occluded-content,
tfoot .occluded-content {
  display: table-row;
  position: relative;
  width: 100%;
}

ul .occluded-content,
ol .occluded-content {
  display: list-item;
  position: relative;
  width: 100%;
  list-style-type: none;
  height: 0;
}

:root {
  /* The named -duration and -delay variables will be lowered to near zero when using the setupPromiseModals test helper  */
  --epm-animation-backdrop-in-duration: 0.3s;
  --epm-animation-backdrop-out-duration: 0.18s;
  --epm-animation-modal-in-duration: 0.3s;
  --epm-animation-modal-out-duration: 0.18s;
  --epm-animation-backdrop-in-delay: 0s;
  --epm-animation-backdrop-out-delay: 0s;
  --epm-animation-modal-in-delay: 0s;
  --epm-animation-modal-out-delay: 0s;
  --epm-animation-backdrop-in: var(--epm-animation-backdrop-in-duration) ease var(--epm-animation-backdrop-in-delay) forwards epm-backdrop-in;
  --epm-animation-backdrop-out: var(--epm-animation-backdrop-out-duration) ease var(--epm-animation-backdrop-out-delay) forwards epm-backdrop-out;
  --epm-animation-modal-in: var(--epm-animation-modal-in-duration) ease-out var(--epm-animation-modal-in-delay) forwards epm-modal-in;
  --epm-animation-modal-out: var(--epm-animation-modal-out-duration) ease-out var(--epm-animation-modal-out-delay) forwards epm-modal-out;
  --epm-backdrop-background: #2d3748CD;
}

@media (prefers-reduced-motion: reduce) {
  :root {
  --epm-animation-backdrop-in-duration: 0s;
  --epm-animation-backdrop-out-duration: 0s;
  --epm-animation-modal-in-duration: 0s;
  --epm-animation-modal-out-duration: 0s;
  --epm-animation-backdrop-in-delay: 0s;
  --epm-animation-backdrop-out-delay: 0s;
  --epm-animation-modal-in-delay: 0s;
  --epm-animation-modal-out-delay: 0s;
  }
}

.epm-scrolling-disabled {
  overflow: hidden;
}

.epm-backdrop,
.epm-modal-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.epm-backdrop {
  background-color: #2d3748CD;
  background-color: var(--epm-backdrop-background);
  opacity: 0;
  animation: 0.3s ease 0s forwards epm-backdrop-in;
  animation: var(--epm-animation-backdrop-in);
  animation-delay: 0s;
  animation-delay: var(--epm-animation-backdrop-in-delay);
  animation-duration: 0.3s;
  animation-duration: var(--epm-animation-backdrop-in-duration);
}

.epm-modal-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.epm-animating .epm-modal-container {
  overflow: visible;
  overflow: initial;
}

.epm-modal {
  margin: auto;
  transform: translate(0, -30vh) scale(1.1);
  opacity: 0;
  animation: 0.3s ease-out 0s forwards epm-modal-in;
  animation: var(--epm-animation-modal-in);
  animation-delay: 0s;
  animation-delay: var(--epm-animation-modal-in-delay);
  animation-duration: 0.3s;
  animation-duration: var(--epm-animation-modal-in-duration);
  -webkit-overflow-scrolling: touch; /* momentum-based scrolling for Safari on iOS */
}

.epm-backdrop.epm-out {
  opacity: 1;
  animation: 0.18s ease 0s forwards epm-backdrop-out;
  animation: var(--epm-animation-backdrop-out);
  animation-delay: 0s;
  animation-delay: var(--epm-animation-backdrop-out-delay);
  animation-duration: 0.18s;
  animation-duration: var(--epm-animation-backdrop-out-duration);
  pointer-events: none;
}

.epm-modal.epm-out {
  transform: translate(0, 0) scale(1);
  opacity: 1;
  animation: 0.18s ease-out 0s forwards epm-modal-out;
  animation: var(--epm-animation-modal-out);
  animation-delay: 0s;
  animation-delay: var(--epm-animation-modal-out-delay);
  animation-duration: 0.18s;
  animation-duration: var(--epm-animation-modal-out-duration);
  pointer-events: none;
}

@keyframes epm-backdrop-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes epm-backdrop-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes epm-modal-in {
  0% {
    transform: translate(0, -30vh) scale(1.1);
    opacity: 0;
  }
  72% {
    transform: translate(0, 0) scale(0.99);
    opacity: 1;
  }
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
}

@keyframes epm-modal-out {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(0, -10vh) scale(0.8);
    opacity: 0;
  }
}

.ember-tooltip-base {
  display: none;
  height: 0;
  width: 0;
  position: absolute;
}

.ember-tooltip,
.ember-popover {
  margin-bottom: 10px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  pointer-events: none;
  position: absolute;
  z-index: 2;

  font-size: 14px;
  font-family: inherit;
  border-radius: 3px;
}

.ember-tooltip {
  max-width: 200px;
  padding: 6px 10px;
  color: #fff;
  background: #3a3c47;
  text-shadow: -1px -1px 0 rgba(0,0,0,0.2);
  white-space: normal; /* IE doesnt support initial so fall back to normal */
  white-space: initial;
}

.ember-popover {
  color: #000;
  background-color: #fff;
  padding: 10px 20px;
  border: 1px solid #ccc;
}

.ember-popover[aria-hidden="false"] {
  pointer-events: auto;
  cursor: initial;
  -webkit-touch-callout: auto;
  -webkit-user-select: auto;
  user-select: auto;
}

.ember-tooltip-arrow,
.ember-popover-arrow {
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  margin: 5px;
  border: 5px solid transparent;
}

.ember-tooltip, .ember-popover {
  opacity: 0;
  -webkit-transition: opacity ease-out, margin ease-out;
  transition: opacity ease-out, margin ease-out;
  transition-duration: 200ms;
}

.ember-tooltip.ember-tooltip-effect-none,
.ember-popover.ember-tooltip-effect-none,
.ember-tooltip.ember-tooltip-show,
.ember-popover.ember-tooltip-show {
  opacity: 1;
}

.ember-tooltip[x-placement^="top"] .ember-tooltip-arrow,
.ember-popover[x-placement^="top"] .ember-popover-arrow {
  border-bottom-width: 0;
  bottom: -5px;
  left: calc(50% - 5px);
  margin-top: 0;
  margin-bottom: 0;
}

.ember-tooltip[x-placement^="top"] .ember-tooltip-arrow {
  border-top-color: #3a3c47;
}

.ember-popover[x-placement^="top"] .ember-popover-arrow {
  border-top-color: #ccc;
}

.ember-tooltip[x-placement^="right"] .ember-tooltip-arrow,
.ember-popover[x-placement^="right"] .ember-popover-arrow {
  border-left-width: 0;
  left: -5px;
  top: calc(50% - 5px);
  margin-right: 0;
  margin-left: 0;
}

.ember-tooltip[x-placement^="right"] .ember-tooltip-arrow {
  border-right-color: #3a3c47;
}

.ember-popover[x-placement^="right"] .ember-popover-arrow {
  border-right-color: #ccc;
}

.ember-tooltip[x-placement^="bottom"] .ember-tooltip-arrow,
.ember-popover[x-placement^="bottom"] .ember-popover-arrow {
  border-top-width: 0;
  top: -5px;
  left: calc(50% - 5px);
  margin-bottom: 0;
  margin-top: 0;
}

.ember-tooltip[x-placement^="bottom"] .ember-tooltip-arrow {
  border-bottom-color: #3a3c47;
}

.ember-popover[x-placement^="bottom"] .ember-popover-arrow {
  border-bottom-color: #ccc;
}

.ember-tooltip[x-placement^="left"] .ember-tooltip-arrow,
.ember-popover[x-placement^="left"] .ember-popover-arrow {
  border-right-width: 0;
  right: -5px;
  top: calc(50% - 5px);
  margin-left: 0;
  margin-right: 0;
}

.ember-tooltip[x-placement^="left"] .ember-tooltip-arrow {
  border-left-color: #3a3c47;
}

.ember-popover[x-placement^="left"] .ember-popover-arrow {
  border-left-color: #ccc;
}

.default-liquid-destination {
  position: absolute;
  top: 0;
  left: 0;
  height: 0;

  width: 100%;
  z-index: 9999;
}

.default-liquid-destination .liquid-destination-stack {
  position: absolute;

  width: 100%;
}

.default-liquid-destination > .liquid-destination-stack > .liquid-child {
  position: absolute;
  top: 0;
  left: 0;

  width: 100%;
  overflow: visible;
  visibility: hidden;
}

.default-liquid-destination > .liquid-destination-stack > .liquid-child > div {
  position: absolute;
  top: 0;
  left: 0;

  width: 100%;
  height: 100vh;
  visibility: hidden;
}

.default-liquid-destination > .liquid-destination-stack > .liquid-child > div > * {
  visibility: visible;
}

.liquid-wormhole-container {
  display: none;
}
