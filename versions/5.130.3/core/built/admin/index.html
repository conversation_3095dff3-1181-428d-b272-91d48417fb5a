<!DOCTYPE html>
<html class="no-js" lang="en">
<head>
    <meta charset="UTF-8" />

    <title>Ghost</title>

    
<meta name="ghost-admin/config/environment" content="%7B%22modulePrefix%22%3A%22ghost-admin%22%2C%22environment%22%3A%22production%22%2C%22cdnUrl%22%3A%22%22%2C%22editorUrl%22%3A%22%22%2C%22rootURL%22%3A%22%22%2C%22locationType%22%3A%22trailing-hash%22%2C%22EmberENV%22%3A%7B%22FEATURES%22%3A%7B%7D%2C%22EXTEND_PROTOTYPES%22%3A%7B%22Date%22%3Afalse%2C%22Array%22%3Atrue%2C%22String%22%3Atrue%2C%22Function%22%3Afalse%7D%2C%22_APPLICATION_TEMPLATE_WRAPPER%22%3Afalse%2C%22_JQUERY_INTEGRATION%22%3Atrue%2C%22_TEMPLATE_ONLY_GLIMMER_COMPONENTS%22%3Atrue%7D%2C%22APP%22%3A%7B%22version%22%3A%225.130%22%2C%22name%22%3A%22ghost-admin%22%7D%2C%22ember-simple-auth%22%3A%7B%7D%2C%22%40sentry%2Fember%22%3A%7B%22disablePerformance%22%3Atrue%2C%22sentry%22%3A%7B%7D%7D%2C%22ember-cli-mirage%22%3A%7B%22usingProxy%22%3Afalse%2C%22useDefaultPassthroughs%22%3Atrue%7D%2C%22exportApplicationGlobal%22%3Afalse%2C%22ember-load%22%3A%7B%22loadingIndicatorClass%22%3A%22ember-load-indicator%22%7D%2C%22editorFilename%22%3A%22koenig-lexical.umd.js%22%2C%22editorHash%22%3A%2237bd1e3e4d%22%2C%22adminXSettingsFilename%22%3A%22admin-x-settings.js%22%2C%22adminXSettingsHash%22%3A%22575f27df11%22%2C%22adminXActivitypubFilename%22%3A%22admin-x-activitypub.js%22%2C%22adminXActivitypubHash%22%3A%22b38d27bb5e%22%2C%22postsFilename%22%3A%22posts.js%22%2C%22postsHash%22%3A%229a7e1b885b%22%2C%22statsFilename%22%3A%22stats.js%22%2C%22statsHash%22%3A%225445ed6ca9%22%2C%22adminXActivitypubCustomUrl%22%3A%22https%3A%2F%2Fcdn.jsdelivr.net%2Fghost%2Fadmin-x-activitypub%400%2Fdist%2Fadmin-x-activitypub.js%22%7D" />

    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1, minimal-ui, viewport-fit=cover" />
    <meta name="pinterest" content="nopin" />

    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="Ghost" />
    <meta name="apple-mobile-web-app-title" content="Ghost" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />

    <link rel="shortcut icon" href="assets/img/favicon-a9c6dbdcdc3ae568f4e0dad92149a0e3.ico" />
    <link rel="apple-touch-icon" href="assets/img/apple-touch-icon-74680e326a7e87b159d366c7d4fb3d4b.png" />

    <!-- variables that we don't want postcss-custom-properties to remove -->
    <style>
        :root {
            --editor-sidebar-width: 0px;
        }
    </style>

    <link integrity="" rel="stylesheet" href="assets/vendor-0ede59da8efb5e28fa929557f7ff7154.css">
    <link integrity="" rel="stylesheet" href="assets/ghost-e474a058646b6df157de25ea1cd978d5.css" title="light">

    
</head>
<body>

    <div class="ember-load-indicator">
        <div class="gh-loading-content">
            <video width="100" height="100" loop autoplay muted playsinline preload="metadata" style="width: 100px; height: 100px;">
                <source src="assets/videos/logo-loader-d91f93ba1dbf0f52a22815b5a571bd31.mp4" type="video/mp4" />
                <div class="gh-loading-spinner"></div>
            </video>
        </div>
    </div>

    

    <div id="ember-basic-dropdown-wormhole"></div>

    <script src="assets/vendor-aed0068cf9b67d042dd23a6343545b7b.js"></script>
<script src="assets/chunk.728.985c45ad584b4b91ca60.js"></script>
<script src="assets/chunk.524.d4dce2126c430c48812b.js"></script>
    <script src="assets/ghost-bf886dc6db7f0d5d41ebe6a468fadc90.js"></script>
</body>
</html>
