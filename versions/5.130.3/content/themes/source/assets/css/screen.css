/* Table of contents
/* ------------------------------------------------------------

This is a development CSS file which is built to a minified
production stylesheet in assets/built/screen.css

1. Variables
2. Fonts
3. Resets
4. Globals
5. Layout
6. Navigation
  6.1. Navigation styles
  6.2. Navigation layouts
  6.3. Dropdown menu
  6.4. Mobile menu
7. Card
8. Header
  8.1. Magazine layout
  8.2. Highlight layout
  8.3. Classic layout
9. CTA
10. Featured posts
11. Container
  11.1. With sidebar
  11.2. Without sidebar
12. Post list
  12.1. List style
  12.2. Grid style
  12.3. No image list
13. Sidebar
14. Post/page
  14.1. Article
  14.2. Page template
  14.3. Page without header
15. Content
16. Cards
17. Comments
18. Recent posts
19. Archive
20. Design settings
21. Footer
  21.1. Footer styles
  21.2. Footer bar
  21.3. Footer signup
22. Lightbox

*/

/* 1. Variables
/* ---------------------------------------------------------- */

:root {
    --color-white: #fff;
    --color-lighter-gray: rgb(0 0 0 / 0.05);
    --color-light-gray: #e6e6e6;
    --color-mid-gray: #ccc;
    --color-dark-gray: #444;
    --color-darker-gray: #15171a;
    --color-black: #000;
    --color-primary-text: var(--color-darker-gray);
    --color-secondary-text: rgb(0 0 0 / 0.55);
    --color-border: rgb(0 0 0 / 0.08);
    --color-dark-border: rgb(0 0 0 / 0.55);
    --font-sans: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    --font-serif: "EB Garamond", Georgia, Times, serif;
    --font-serif-alt: Georgia, Times, serif;
    --font-mono: "JetBrains Mono", Menlo, Consolas, Monaco, "Liberation Mono", "Lucida Console", monospace;
    --container-width: 1320px;
    --container-gap: clamp(24px, 1.7032rem + 1.9355vw, 48px);
    --grid-gap: 42px;
}

:root.has-light-text,
:is(.gh-navigation, .gh-footer).has-accent-color {
    --color-lighter-gray: rgb(255 255 255 / 0.1);
    --color-darker-gray: #fff;
    --color-secondary-text: rgb(255 255 255 / 0.64);
    --color-border: rgb(255 255 255 / 0.15);
    --color-dark-border: rgb(255 255 255 / 0.5);
}

/* 2. Fonts
/* ---------------------------------------------------------- */

/* Fonts are preloaded and defined in default.hbs to avoid layout shift */

/* 3. Resets
/* ---------------------------------------------------------- */

*, *::before, *::after {
    box-sizing: border-box;
}

* {
    margin: 0;
}

html {
    font-size: 62.5%;
}

body {
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img, picture, video, canvas, svg {
    display: block;
    height: auto;
    max-width: 100%;
}

iframe {
    display: block;
}

input, button, textarea, select {
    font: inherit;
}

p, h1, h2, h3, h4, h5, h6 {
    overflow-wrap: break-word;
}

h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
}

/* 4. Globals
/* ---------------------------------------------------------- */

body {
    font-family: var(--gh-font-body, var(--font-sans));
    font-size: 1.6rem;
    background-color: var(--background-color);
    color: var(--color-primary-text);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--gh-font-heading, var(--font-sans));
}

a {
    color: var(--color-darker-gray);
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}

.gh-button {
    display: inline-flex;
    gap: 0.4em;
    align-items: center;
    justify-content: center;
    padding: 0.8em 1.4em;
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: -0.004em;
    line-height: 1;
    color: var(--color-white);
    cursor: pointer;
    background-color: var(--ghost-accent-color);
    border: 0;
    border-radius: 100px;
}

.gh-button:hover {
    opacity: 0.95;
}

.gh-icon-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    color: var(--color-darker-gray);
    cursor: pointer;
    background-color: transparent;
    border: 0;
    outline: none;
}

.gh-icon-button:hover :is(svg, span) {
    opacity: 0.8;
}

.gh-icon-button svg {
    width: 20px;
    height: 20px;
}

.gh-form {
    display: flex;
    align-items: center;
    position: relative;
    max-width: 560px;
    width: 100%;
    height: 56px;
    font-size: 1.7rem;
    font-weight: 450;
    letter-spacing: -0.008em;
    border-radius: 40px;
    background-color: var(--color-lighter-gray);
    transition: background-color 0.2s ease;
}

.gh-form.success {
    pointer-events: none;
}

.gh-form.error {
    box-shadow: 0 0 0 1px red;
}

.gh-form:hover {
    background-color: rgb(0 0 0 / 0.065);
}

.has-light-text .gh-form:hover,
.gh-footer.has-accent-color .gh-form:hover {
    background-color: rgb(255 255 255 / 0.15);
}

.gh-form-input {
    position: absolute;
    inset: 0;
    padding-inline: 26px;
    width: 100%;
    height: 100%;
    font-size: inherit;
    letter-spacing: inherit;
    line-height: 1.1;
    border: 0;
    border-radius: 40px;
    background-color: transparent;
    outline: none;
    transition: 0.3s ease-in-out;
}

.gh-form-input::placeholder,
button.gh-form-input {
    color: rgb(0 0 0 / 0.3);
}

:is(.has-serif-title, .has-mono-title):not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-form-input,
body[class*=" gh-font-heading"]:not(.gh-font-heading-fira-sans):not(.gh-font-heading-inter):not(.gh-font-heading-manrope):not(.gh-font-heading-noto-sans):not(.gh-font-heading-nunito):not(.gh-font-heading-poppins):not(.gh-font-heading-roboto):not(.gh-font-heading-space-grotesk) .gh-form-input {
    padding-inline: 20px;
}

.gh-form.gh-form.success .gh-form-input {
    opacity: 0.5;
}

.has-light-text .gh-form-input,
.gh-footer.has-accent-color .gh-form-input {
    color: #fff;
}

.has-light-text .gh-form-input::placeholder,
.has-light-text button.gh-form-input,
.gh-footer.has-accent-color .gh-form-input::placeholder {
    color: rgb(255 255 255 / 0.55);
}

.gh-header.is-classic.has-image .gh-form-input {
    color: #15171a;
}

.gh-header.is-classic.has-image .gh-form-input::placeholder,
.gh-header.is-classic.has-image button.gh-form-input,
.gh-header.is-classic.has-image .gh-form > svg {
    color: rgb(0 0 0 / 0.5);
}

button.gh-form-input {
    padding-inline-start: 56px;
    text-align: left;
    color: var(--color-secondary-text);
    cursor: pointer;
}

:is(.has-serif-title,.has-mono-title):not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) button.gh-form-input,
body[class*=" gh-font-heading"]:not(.gh-font-heading-fira-sans):not(.gh-font-heading-inter):not(.gh-font-heading-manrope):not(.gh-font-heading-noto-sans):not(.gh-font-heading-nunito):not(.gh-font-heading-poppins):not(.gh-font-heading-roboto):not(.gh-font-heading-space-grotesk) button.gh-form-input {
    padding-inline-start: 50px;
}

.gh-form .gh-button {
    position: absolute;
    right: 6px;
    padding-inline: 32px;
    height: 44px;
    font-size: inherit;
}

.gh-form > svg {
    position: relative;
    left: 22px;
    width: 20px;
    height: 20px;
    color: var(--color-secondary-text);
}

:is(.has-serif-title,.has-mono-title):not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-form > svg {
    left: 16px;
}

.gh-form .gh-button svg {
    display: none;
    position: absolute;
    margin-top: 1px;
}

.gh-form:is(.loading, .success) .gh-button span {
    visibility: hidden;
}

.gh-form.loading .gh-button svg:first-of-type {
    display: block;
}

.gh-form.success .gh-button svg:last-of-type {
    display: block;
}

.gh-form [data-members-error] {
    position: absolute;
    top: 100%;
    margin-top: 10px;
    width: 100%;
    font-size: 1.4rem;
    line-height: 1.4;
}

@media (max-width: 576px) {
    .gh-form {
        font-size: 1.6rem;
    }

    .gh-form .gh-button {
        padding-inline: 12px;
    }

    .gh-form .gh-button span span {
        display: none;
    }

    .gh-form .gh-button span svg {
        display: inline;
        position: static;
        margin-top: 2px;
        width: 20px;
        height: 20px;
    }
}

/* 5. Layout
/* ---------------------------------------------------------- */

.gh-viewport {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.gh-outer {
    padding: 0 max(4vmin, 20px);
}

.gh-outer .gh-outer {
    padding: 0;
}

.gh-inner {
    margin: 0 auto;
    max-width: var(--container-width);
    width: 100%;
}

.gh-main {
    flex-grow: 1;
}

.gh-canvas,
.kg-width-full.kg-content-wide {
    --main: min(var(--content-width, 720px), 100% - var(--container-gap) * 2);
    --wide: minmax(0, calc((var(--container-width, 1200px) - var(--content-width, 720px)) / 2));
    --full: minmax(var(--container-gap), 1fr);

    display: grid;
    grid-template-columns:
        [full-start] var(--full)
        [wide-start] var(--wide)
        [main-start] var(--main) [main-end]
        var(--wide) [wide-end]
        var(--full) [full-end];
}

.gh-canvas > * {
    grid-column: main;
}

.kg-width-wide,
.kg-content-wide > div {
    grid-column: wide;
}

.kg-width-full {
    grid-column: full;
}

/* 6. Navigation
/* ---------------------------------------------------------- */

.gh-navigation {
    height: 100px;
    font-size: 1.5rem;
    font-weight: 550;
    background-color: var(--background-color);
    color: var(--color-darker-gray);
}

.gh-navigation :is(.gh-navigation-logo, a:not(.gh-button), .gh-icon-button) {
    color: inherit;
}

.gh-navigation-inner {
    display: grid;
    grid-auto-flow: row dense;
    column-gap: 24px;
    align-items: center;
    height: 100%;
}

.gh-navigation-brand {
    line-height: 1;
}

.gh-navigation-logo {
    position: relative;
    font-family: var(--gh-font-heading, var(--font-sans));
    font-size: calc(2.4rem * var(--factor, 1));
    font-weight: 725;
    letter-spacing: -0.015em;
    white-space: nowrap;
}

.gh-navigation-logo img {
    max-height: 40px;
}

.gh-navigation-menu {
    display: flex;
    gap: 24px;
    align-items: center;
}

.gh-navigation .nav {
    display: inline-flex;
    gap: 28px;
    align-items: center;
    padding: 0;
    margin: 0;
    white-space: nowrap;
    list-style: none;
}

.gh-navigation .gh-more-toggle {
    position: relative;
    margin: 0 -6px;
    font-size: inherit;
    text-transform: inherit;
}

.gh-navigation .gh-more-toggle svg {
    width: 24px;
    height: 24px;
}

.gh-navigation-actions {
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: flex-end;
    background-color: var(--background-color);
}

.gh-navigation.has-accent-color .gh-navigation-actions {
    background-color: var(--ghost-accent-color);
}

.gh-navigation-members {
    display: flex;
    gap: 20px;
    align-items: center;
    white-space: nowrap;
}

.gh-navigation-members .gh-button {
    font-size: inherit;
    font-weight: 600;
}

.gh-search {
    margin-right: -2px;
    margin-left: -2px;
}

@media (max-width: 767px) {
    .gh-navigation-logo {
        white-space: normal;
    }

    .gh-navigation-members {
        flex-direction: column-reverse;
        gap: 16px;
        width: 100%;
    }

    .gh-navigation-actions .gh-search {
        display: none;
    }
}

@media (min-width: 768px) {
    .gh-navigation-brand .gh-search {
        display: none;
    }

    .gh-navigation:not(.is-dropdown-loaded) .gh-navigation-menu .nav > li {
        opacity: 0;
    }
}

/* 6.1. Navigation styles */

.gh-navigation.has-accent-color {
    background-color: var(--ghost-accent-color);
}

.gh-navigation.has-accent-color .gh-button {
    background-color: #fff;
    color: #15171a;
}

/* 6.2. Navigation layouts */

/*
======================================================================
LOGO   Home About Collection Author Portal             Login Subscribe
======================================================================
*/

.gh-navigation.is-left-logo .gh-navigation-inner {
    grid-template-columns: auto 1fr auto;
}

@media (min-width: 768px) {
    .gh-navigation.is-left-logo .gh-navigation-logo:not(:has(img)) {
        top: -2px;
    }
}

@media (min-width: 992px) {
    .gh-navigation.is-left-logo .gh-navigation-menu {
        margin-right: 100px;
        margin-left: 16px;
    }
}

/*
======================================================================
Home About Collection            LOGO                  Login Subscribe
======================================================================
*/

.gh-navigation.is-middle-logo .gh-navigation-inner {
    grid-template-columns: 1fr auto 1fr;
}

.gh-navigation.is-middle-logo .gh-navigation-brand {
    grid-column-start: 2;
}

.gh-navigation.is-middle-logo .gh-navigation-actions {
    gap: 28px;
}

@media (min-width: 992px) {
    .gh-navigation.is-middle-logo .gh-navigation-menu {
        margin-right: 64px;
    }
}

/*
======================================================================
Search                         LOGO                    Login Subscribe
                 Home About Collection Author Portal
======================================================================
*/

.gh-navigation.is-stacked {
    position: relative;
    height: auto;
}

.gh-navigation.is-stacked .gh-navigation-inner {
    grid-template-columns: 1fr auto 1fr;
}

.gh-navigation.is-stacked .gh-navigation-brand {
    display: flex;
    align-items: center;
    grid-row-start: 1;
    grid-column-start: 2;
    min-height: 80px;
}

@media (max-width: 767px) {
    .gh-navigation.is-stacked .gh-navigation-brand {
        min-height: unset;
    }
}

@media (min-width: 992px) {
    .gh-navigation.is-stacked .gh-navigation-inner {
        padding: 0;
    }

    .gh-navigation.is-stacked .gh-navigation-brand {
        display: flex;
        align-items: center;
        height: 80px;
    }

    .gh-navigation.is-stacked .gh-navigation-menu {
        grid-row-start: 2;
        grid-column: 1 / 4;
        justify-content: center;
        height: 60px;
        margin: 0 48px;
    }

    .gh-navigation.is-stacked .gh-navigation-menu::before,
    .gh-navigation.is-stacked .gh-navigation-menu::after {
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        height: 1px;
        content: "";
        background-color: var(--color-border);
    }

    .gh-navigation.is-stacked .gh-navigation-menu::after {
        top: 140px;
    }

    .gh-navigation.is-stacked .gh-navigation-actions {
        grid-row-start: 1;
        grid-column: 1 / 4;
        justify-content: space-between;
    }

    .gh-navigation.is-stacked .gh-navigation-actions .gh-search {
        display: flex;
        gap: 10px;
        width: auto;
    }
}

/* 6.3. Dropdown menu */

.gh-dropdown {
    position: absolute;
    top: 100%;
    right: -16px;
    z-index: 90;
    width: 200px;
    padding: 12px 0;
    margin-top: 24px;
    text-align: left;
    visibility: hidden;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 0 1px rgb(0 0 0 / 0.04), 0 7px 20px -5px rgb(0 0 0 / 0.15);
    opacity: 0;
    transition: opacity 0.3s, transform 0.2s;
    transform: translate3d(0, 6px, 0);
}

.gh-dropdown.is-left {
    right: auto;
    left: -16px;
}

.is-dropdown-mega .gh-dropdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-auto-flow: column;
    column-gap: 40px;
    width: auto;
    padding: 20px 32px;
}

.is-dropdown-open .gh-dropdown {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.gh-dropdown li a {
    display: block;
    padding: 7px 20px;
    line-height: 1.5;
    white-space: normal;
    color: #15171a !important;
}

.is-dropdown-mega .gh-dropdown li a {
    padding: 8px 0;
}

/* 6.4. Mobile menu */

.gh-burger {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    display: none;
    margin-right: -7px;
    margin-left: 4px;
}

.gh-burger svg {
    width: 24px;
    height: 24px;
}

.gh-burger svg:last-child {
    display: none;
}

.is-open .gh-burger svg:first-child {
    display: none;
}

.is-open .gh-burger svg:last-child {
    display: block;
}

@media (max-width: 767px) {
    .gh-burger {
        display: flex;
    }

    #gh-navigation {
        height: 64px;
    }

    #gh-navigation .gh-navigation-inner {
        grid-template-rows: auto 1fr auto;
        grid-template-columns: 1fr;
        gap: 48px;
    }

    #gh-navigation .gh-navigation-brand {
        display: grid;
        grid-template-columns: 1fr auto auto;
        grid-column-start: 1;
        align-items: center;
        height: 64px;
    }

    #gh-navigation .gh-navigation-logo {
        font-size: 2.2rem;
    }

    #gh-navigation .gh-navigation-menu,
    #gh-navigation .gh-navigation-actions {
        position: fixed;
        justify-content: center;
        visibility: hidden;
        opacity: 0;
    }

    #gh-navigation .gh-navigation-menu {
        transition: none;
        transform: translateY(0);
    }

    #gh-navigation .nav {
        gap: 20px;
        align-items: center;
        line-height: 1.4;
    }

    #gh-navigation .nav a {
        font-size: 1.75rem;
        font-weight: 600;
        text-transform: none;
    }

    #gh-navigation .nav li {
        opacity: 0;
        transform: translateY(-4px);
    }

    #gh-navigation .gh-navigation-actions {
        text-align: center;
    }

    #gh-navigation :is(.gh-button, a[data-portal="signin"]) {
        opacity: 0;
        transform: translateY(8px);
    }

    #gh-navigation .gh-button {
        width: 100%;
        font-size: 1.75rem;
        text-transform: none;
    }

    #gh-navigation a[data-portal="signin"] {
        font-size: 1.75rem;
    }

    #gh-main {
        transition: opacity 0.4s;
    }

    .is-open#gh-navigation {
        position: fixed;
        inset: 0;
        z-index: 3999999;
        height: 100%;
        overflow-y: scroll;
        -webkit-overflow-scrolling: touch;
    }

    .is-open#gh-navigation .gh-navigation-menu,
    .is-open#gh-navigation .gh-navigation-actions {
        position: static;
        visibility: visible;
        opacity: 1;
    }

    .is-open#gh-navigation .nav {
        display: flex;
        flex-direction: column;
    }

    .is-open#gh-navigation .nav li {
        opacity: 1;
        transition: transform 0.2s, opacity 0.2s;
        transform: translateY(0);
    }

    .is-open#gh-navigation .gh-navigation-actions {
        position: sticky;
        right: 0;
        bottom: 0;
        left: 0;
        display: inline-flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        padding: var(--container-gap) 0;
    }

    .is-open#gh-navigation :is(.gh-button, a[data-portal="signin"]) {
        opacity: 1;
        transition: transform 0.4s, opacity 0.4s;
        transition-delay: 0.2s;
        transform: translateY(0);
    }

    .is-open#gh-navigation a[data-portal="signin"] {
        transition-delay: 0.4s;
    }

    .is-open#gh-main {
        opacity: 0;
    }
}

/* 7. Card
/* ---------------------------------------------------------- */

.gh-card {
    position: relative;
}

.gh-card-link {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.gh-card-link:hover {
    opacity: 1;
}

.gh-card-image {
    position: relative;
    flex-shrink: 0;
    aspect-ratio: 16 / 9;
}

.gh-card-image img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gh-card-wrapper {
    flex-grow: 1;
}

.gh-card-tag {
    display: none;
    margin-bottom: 4px;
    font-size: 1.2rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    text-transform: uppercase;
}

.gh-card-title {
    font-size: calc(1.9rem * var(--factor, 1));
    font-weight: 725;
    letter-spacing: -0.014em;
    line-height: 1.3;
}

.gh-card-link:hover .gh-card-title {
    opacity: 0.8;
}

.gh-card-excerpt {
    display: -webkit-box;
    overflow-y: hidden;
    margin-top: 8px;
    font-size: 1.45rem;
    line-height: 1.4;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.gh-card-meta {
    align-items: center;
    padding-bottom: 1px;
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.4;
    letter-spacing: -0.004em;
    color: var(--color-secondary-text);
}

.gh-card-meta svg {
    position: relative;
    top: 1px;
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 2px;
}

.gh-card-meta:not(:empty) {
    margin-top: 8px;
}

.gh-card-author + .gh-card-date::before {
    content: "—";
    margin-right: 4px;
}

/* 8. Header
/* ---------------------------------------------------------- */

.gh-header {
    position: relative;
    margin-top: 40px;
}

.gh-header-inner {
    position: relative;
    overflow: hidden;
}

/* 8.1. Magazine layout */

.gh-header.is-magazine .gh-header-inner {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    gap: var(--grid-gap);
}

.gh-header.is-magazine .gh-header-inner > div {
    display: flex;
    flex-direction: column;
    gap: var(--grid-gap);
    grid-row: 1;
}

.gh-header.is-magazine .gh-header-left {
    grid-column: 1 / span 4;
}

.gh-header.is-magazine .gh-header-inner > .gh-card {
    position: relative;
    grid-column: 5 / span 8;
}

.gh-header.is-magazine .gh-header-right {
    grid-column: 13 / -1;
}

.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-link {
    gap: 28px;
}

.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-image {
    aspect-ratio: 1.618033;
}

.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-tag {
    display: block;
    margin-bottom: 12px;
}

.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-title {
    font-size: clamp(2.8rem, 1.36vw + 2.25rem, 4rem);
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.022em;
}

.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-excerpt {
    margin-top: clamp(12px, 0.45vw + 10.18px, 16px);
    font-size: 1.8rem;
    letter-spacing: -0.02em;
    max-width: 90%;
}

.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-meta:not(:empty) {
    margin-top: 14px;
}

.gh-header.is-magazine :is(.gh-header-left, .gh-header-right) .gh-card:last-child .gh-card-image {
    display: none;
}

.gh-header.is-magazine .gh-header-inner > div .gh-card-excerpt {
    display: none;
}

.gh-header.is-magazine .gh-header-inner > .gh-card::before,
.gh-header.is-magazine .gh-header-inner > .gh-card::after {
    position: absolute;
    top: 0;
    left: calc(var(--grid-gap) / -2);
    content: "";
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
}

.gh-header.is-magazine .gh-header-inner > .gh-card::after {
    right: calc(var(--grid-gap) / -2);
    left: auto;
}

.gh-header.is-magazine .gh-header-inner > div .gh-card + .gh-card::before {
    position: absolute;
    top: calc(var(--grid-gap) / -2);
    left: 0;
    content: "";
    width: 100%;
    height: 1px;
    background-color: var(--color-border);
}

@media (max-width: 1199px) {
    .gh-header.is-magazine .gh-header-inner {
        grid-template-columns: repeat(12, 1fr);
    }

    .gh-header.is-magazine .gh-header-inner > .gh-card {
        grid-column: 1 / span 8;
    }

    .gh-header.is-magazine .gh-header-left {
        grid-column: 9 / -1;
    }

    .gh-header.is-magazine .gh-header-inner > div.gh-header-right {
        grid-column: 1 / -1;
        grid-row: 2;
        flex-direction: row;
    }

    .gh-header.is-magazine .gh-header-right .gh-card {
        flex: 1;
    }

    .gh-header.is-magazine .gh-header-right .gh-card:last-child .gh-card-image {
        display: block;
    }

    .gh-header.is-magazine .gh-header-right {
        position: relative;
    }

    .gh-header.is-magazine .gh-header-right::before {
        position: absolute;
        top: calc(var(--grid-gap) / -2);
        left: 0;
        content: "";
        width: 100%;
        height: 1px;
        background-color: var(--color-border);
    }

    .gh-header.is-magazine .gh-header-right .gh-card::before {
        display: none;
    }

    .gh-header.is-magazine .gh-header-right .gh-card::after {
        position: absolute;
        top: 0;
        right: calc(var(--grid-gap) / -2);
        content: "";
        width: 1px;
        height: 100%;
        background-color: var(--color-border);
    }
}

@media (max-width: 991px) {
    .gh-header.is-magazine .gh-header-left .gh-card:nth-child(2) .gh-card-image {
        display: none;
    }
}

@media (max-width: 767px) {
    .gh-header.is-magazine .gh-header-inner {
        display: flex;
        flex-direction: column;
        gap: var(--grid-gap);
    }

    .gh-header.is-magazine .gh-header-inner > div.gh-header-right {
        flex-direction: column;
    }

    .gh-header.is-magazine .gh-card-image {
        display: block !important;
    }

    .gh-header.is-magazine .gh-card::before {
        display: block !important;
        position: absolute;
        top: calc(var(--grid-gap) / -2);
        left: 0;
        content: "";
        width: 100%;
        height: 1px;
        background-color: var(--color-border);
    }
}

/* 8.2. Highlight layout */

.gh-header.is-highlight .gh-header-inner {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    gap: var(--grid-gap);
}

.gh-header.is-highlight .gh-header-left {
    position: relative;
    grid-column: span 8;
}

.gh-header.is-highlight .gh-header-left .gh-card {
    grid-column: span 8;
    grid-row: span 3;
}

.gh-header.is-highlight .gh-header-left .gh-card .gh-card-link {
    gap: 28px;
}

.gh-header.is-highlight .gh-header-left .gh-card .gh-card-image {
    aspect-ratio: 3 / 2;
}

.gh-header.is-highlight .gh-header-left .gh-card .gh-card-tag {
    display: block;
    margin-bottom: 12px;
}

.gh-header.is-highlight .gh-header-left .gh-card .gh-card-title {
    font-size: clamp(2.8rem, 1.36vw + 2.25rem, 4rem);
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.022em;
}

.gh-header.is-highlight .gh-header-left .gh-card .gh-card-excerpt {
    margin-top: clamp(12px, 0.45vw + 10.18px, 16px);
    font-size: 1.8rem;
}

.gh-header.is-highlight .gh-header-left .gh-card .gh-card-meta:not(:empty) {
    margin-top: 12px;
}

.gh-header.is-highlight .gh-header-middle {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--grid-gap);
    grid-column: 9 / span 4;
}

.gh-header.is-highlight .gh-header-middle .gh-card:last-child .gh-card-image {
    display: none;
}

.gh-header.is-highlight .gh-header-middle .gh-card .gh-card-excerpt {
    display: none;
}

.gh-header.is-highlight .gh-header-right {
    grid-column: 13 / -1;
}

.gh-header.is-highlight .gh-featured {
    margin-top: 0;
    padding: 0;
}

.gh-header.is-highlight .gh-featured-feed {
    display: flex;
    flex-direction: column;
}

.gh-header.is-highlight .gh-featured-feed .gh-card-title {
    font-size: clamp(1.4rem, 0.23vw + 1.31rem, 1.6rem);
}

.gh-header.is-highlight .gh-header-left::after,
.gh-header.is-highlight .gh-header-middle::after {
    position: absolute;
    top: 0;
    right: calc(var(--grid-gap) / -2);
    content: "";
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
}

.gh-header.is-highlight .gh-card::before {
    position: absolute;
    top: calc(var(--grid-gap) / -2);
    left: 0;
    content: "";
    width: 100%;
    height: 1px;
    background-color: var(--color-border);
}

.gh-header.is-highlight .gh-featured .gh-card:first-child::before {
    display: none;
}

@media (max-width: 1199px) {
    .gh-header.is-highlight .gh-header-inner {
        grid-template-columns: repeat(9, 1fr);
    }

    .gh-header.is-highlight .gh-header-left {
        grid-column: span 6;
    }

    .gh-header.is-highlight .gh-header-middle {
        grid-column: 7 / -1;
    }

    .gh-header.is-highlight .gh-header-right {
        grid-column: 1 / -1;
    }

    .gh-header.is-highlight .gh-featured-feed {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }

    .gh-header.is-highlight .gh-featured-feed .gh-card:before {
        width: calc(100% + var(--grid-gap));
    }

    .gh-header.is-highlight .gh-featured-feed .gh-card::after {
        position: absolute;
        top: 0;
        left: calc(var(--grid-gap) / -2);
        content: "";
        width: 1px;
        height: 100%;
        background-color: var(--color-border);
    }
}

@media (max-width: 991px) {
    .gh-header.is-highlight .gh-header-middle .gh-card:nth-child(2) .gh-card-image {
        display: none;
    }
}

@media (max-width: 767px) {
    .gh-header.is-highlight .gh-header-inner {
        display: flex;
        flex-direction: column;
    }

    .gh-header.is-highlight .gh-featured-feed {
        display: flex;
        /* gap: var(--grid-gap); */
    }

    .gh-header.is-highlight .gh-card-image {
        display: block !important;
    }

    .gh-header.is-highlight .gh-header-middle .gh-card .gh-card-excerpt {
        display: -webkit-box;
    }
}

/* 8.3. Classic layout */

.gh-header.is-classic {
    display: flex;
    margin-top: 0;
    padding-block: 160px;
}

.gh-header.is-classic .gh-header-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 48px;
    overflow: unset;
    margin: auto;
    max-width: 1020px;
    text-align: center;
}

.gh-header.is-classic .gh-header-title {
    font-size: calc(clamp(3rem, 1.82vw + 2.27rem, 4.6rem) * var(--factor, 1));
    line-height: 1.1;
    letter-spacing: -0.028em;
}

.gh-header.is-classic.has-image {
    margin-top: 0;
}

.gh-header.is-classic.has-image::before {
    position: absolute;
    inset: 0;
    content: "";
    opacity: 0.3;
    background-color: var(--color-black);
    transition: opacity 1.5s ease;
}

.gh-header.is-classic.has-image .gh-header-inner {
    color: #fff;
}

.gh-header.is-classic.has-image .gh-header-image {
    position: absolute;
    inset: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gh-header.is-classic.has-image .gh-form {
    background-color: #fff;
}

/* 9. CTA
/* ---------------------------------------------------------- */

.gh-cta {
    display: none;
    margin-top: max(4vw, 40px);
}

.gh-header:is(.is-highlight, .is-magazine) + .gh-cta {
    display: block;
}

.gh-cta-inner {
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 36px;
    padding: clamp(48px, 3.64vw + 33.45px, 80px) clamp(40px, 2.73vw + 29.09px, 64px);
    text-align: center;
    background-color: var(--color-lighter-gray);
}

.gh-cta-content {
    max-width: 640px;
}

.gh-cta-title {
    font-size: clamp(2.8rem,1.36vw + 2.25rem,4rem);
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.021em;
}

.gh-cta-description {
    margin-top: 12px;
    font-size: 1.8rem;
    line-height: 1.4;
    letter-spacing: -0.015em;
}

/* 10. Featured posts
/* ---------------------------------------------------------- */

.gh-featured {
    margin-top: 100px;
}

.gh-navigation + .gh-featured {
    margin-top: 64px;
}

.gh-header.is-classic:not(.has-image) + .gh-featured {
    margin-top: 0;
}

.gh-featured-inner {
    overflow: hidden;
}

.gh-featured-title {
    margin-bottom: 20px;
    padding-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 550;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    border-bottom: 1px solid var(--color-border);
}

.gh-featured-feed {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--grid-gap);
}

.gh-featured-feed .gh-card {
    container-type: inline-size;
    container-name: featured-card;
}

.gh-featured-feed .gh-card-link {
    display: flex;
    flex-direction: row-reverse;
    align-items: flex-start;
}

.gh-featured-feed .gh-card-image {
    width: 72px;
    aspect-ratio: 1;
}

@container featured-card (width < 240px) {
    .gh-featured-feed .gh-card-image {
        display: none;
    }
}

@container featured-card (240px <= width <= 270px) {
    .gh-featured-feed .gh-card-image {
        width: 64px;
    }
}

.gh-featured-feed .gh-card-wrapper {
    container-type: inline-size;
    container-name: featured-card-wrapper;
}

.gh-featured-feed .gh-card-title {
    font-size: 1.6rem;
    font-weight: 650;
    letter-spacing: -0.011em;
}

@container featured-card-wrapper (width < 170px) {
    .gh-featured-feed .gh-card-title {
        font-size: 1.6rem;
    }
}

.gh-featured-feed .gh-card-excerpt {
    display: none;
}

.gh-featured-feed .gh-card-meta:not(:empty) {
    margin-top: 8px;
}

.gh-featured-feed .gh-card::before {
    position: absolute;
    top: 0;
    left: calc(var(--grid-gap) / -2);
    content: "";
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
}

@media (max-width: 1199px) {
    .gh-viewport > .gh-featured .gh-featured-feed {
        grid-template-columns: repeat(3, 1fr);
    }

    .gh-viewport > .gh-featured .gh-featured-feed .gh-card:nth-child(4) {
        display: none;
    }
}

@media (max-width: 767px) {
    .gh-viewport > .gh-featured .gh-featured-feed {
        display: flex;
        flex-direction: column;
    }
}

/* 11. Container
/* ---------------------------------------------------------- */

.gh-container {
    flex-grow: 1;
    margin-top: 64px;
}

.gh-container-inner {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    column-gap: var(--grid-gap);
}

:is(.gh-featured, .gh-cta) + .gh-container {
    margin-top: max(4vw, 40px);
}

.gh-header.is-classic:not(.has-image) + .gh-container {
    margin-top: 0;
}

.gh-navigation + .gh-container .gh-container-title,
:is(.paged, .tag-template, .author-template) .gh-container:not(.has-sidebar) .gh-container-title {
    display: none;
}

.gh-more {
    display: none;
    grid-column: 1 / -1;
    margin-top: 48px;
    font-size: calc(1.9rem * var(--factor, 1));
    font-weight: 725;
    letter-spacing: -0.014em;
}

.gh-container.has-sidebar .gh-more {
    grid-column: span 12;
}

.home-template .gh-feed:has(> :nth-child(12):last-child) ~ .gh-more {
    display: block;
}

.gh-more a {
    display: flex;
    align-items: center;
    gap: 4px;
}

.gh-more svg {
    margin-top: -1px;
    width: 18px;
    height: 18px;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-more svg {
    margin-top: 0;
}

/* 11.1. With sidebar */

.gh-container.has-sidebar .gh-main {
    grid-column: 1 / span 12;
    position: relative;
}

.gh-container.has-sidebar .gh-sidebar {
    grid-column: 13 / -1;
}

.gh-container.has-sidebar .gh-main::after {
    position: absolute;
    top: 0;
    right: calc(var(--grid-gap) / -2);
    content: "";
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
}

/* 11.2. Without sidebar */

.gh-container:not(.has-sidebar) :is(.gh-container-title, .gh-main, .gh-more) {
    grid-column: 3 / span 12;
}

.gh-container.is-list.no-image:not(.has-sidebar) :is(.gh-container-title, .gh-main, .gh-more) {
    grid-column: 4 / span 10;
}

.gh-header:is(.is-highlight, .is-magazine) ~ .gh-container.is-grid:not(.has-sidebar) :is(.gh-container-title, .gh-main, .gh-more) {
    grid-column: 1 / -1;
}

@media (max-width: 1199px) {
    .gh-container-inner {
        display: block;
        overflow: hidden;
    }

    .gh-container.has-sidebar .gh-sidebar {
        display: none;
    }
}

/* 12. Post list
/* ---------------------------------------------------------- */

.gh-container-title {
    grid-column: 1 / -1;
    margin-bottom: calc(var(--grid-gap) / 2);
    padding-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 550;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    border-bottom: 1px solid var(--color-border);
}

.gh-container:not(:has(.gh-card)) .gh-container-title {
    display: none;
}

.gh-container .gh-feed {
    gap: var(--grid-gap);
}

.gh-container .gh-card-meta:not(:empty) {
    margin-top: 12px;
}

/* 12.1. List style */

.gh-container.is-list .gh-feed {
    display: flex;
    flex-direction: column;
    container-type: inline-size;
    container-name: list-feed;
}

.gh-container.is-list .gh-card-link {
    flex-direction: row;
    align-items: center;
    gap: 24px;
}

.gh-container.is-list .no-image .gh-card-link {
    padding-block: 20px;
}

.gh-container.is-list .gh-card-image {
    flex-shrink: 0;
    width: 220px;
    aspect-ratio: 1.618033;
}

@container list-feed (width < 600px) {
    .gh-container.is-list .gh-card-image {
        width: 160px;
    }
}

.gh-container.is-list .gh-card:not(.no-image) .gh-card-wrapper {
    max-width: 600px;
}

.gh-container.is-list .gh-card-title {
    --factor: 1.05;
}

.gh-container.is-list .no-image .gh-card-title {
    --factor: 1.2;
}

.gh-container.is-list .gh-card-excerpt {
    margin-top: 6px;
}

.gh-container.is-list .gh-card + .gh-card::before {
    position: absolute;
    top: calc(var(--grid-gap) / -2);
    left: 0;
    content: "";
    width: 100%;
    height: 1px;
    background-color: var(--color-border);
}

.home-template .gh-container.is-list .gh-card:first-child:before {
    display: none;
}

@media (max-width: 767px) {
    .gh-container.is-list .gh-card-link {
        flex-direction: column;
        align-items: flex-start;
    }

    .gh-container.is-list .gh-card-image {
        width: 100%;
    }
}

/* 12.2. Grid style */

.gh-container.is-grid .gh-feed {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(248px, 1fr));
    row-gap: calc(var(--grid-gap) * 1.5);
    overflow: hidden;
}

.gh-container.is-grid .gh-card::before {
    position: absolute;
    top: calc(var(--grid-gap) / -2);
    right: calc(var(--grid-gap) / -2);
    left: calc(var(--grid-gap) / -2);
    content: "";
    height: 1px;
    background-color: var(--color-border);
}

.gh-container.is-grid .gh-card::after {
    position: absolute;
    top: 0;
    left: calc(var(--grid-gap) / -2);
    content: "";
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
}

@media (max-width: 767px) {
    .gh-container.is-grid .gh-feed {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .gh-container.is-grid .gh-feed {
        display: flex;
        flex-direction: column;
    }
}

/* 12.3. No image list */

.gh-container.is-list.no-image .gh-card-image {
    display: none;
}

/* 13. Sidebar
/* ---------------------------------------------------------- */

.gh-sidebar-inner {
    position: sticky;
    top: calc(var(--grid-gap) / 2);
}

.gh-sidebar-title {
    grid-column: 1 / -1;
    margin-bottom: calc(var(--grid-gap) / 2);
    padding-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 550;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    border-bottom: 1px solid var(--color-border);
}

.gh-about {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 48px 24px;
    min-height: 380px;
    text-align: center;
    background-color: var(--color-lighter-gray);
}

.gh-about-icon {
    margin-bottom: 24px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.gh-about-title {
    font-size: calc(2.4rem * var(--factor, 1));
    font-weight: 700;
    letter-spacing: -0.019em;
}

.gh-about-description {
    margin-top: 12px;
    font-size: 1.45rem;
    line-height: 1.4;
}

.gh-about .gh-button {
    margin-top: 32px;
}

.gh-recommendations {
    margin-top: 48px;
}

.gh-recommendations .recommendations {
    display: flex;
    flex-direction: column;
    gap: 26px;
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.gh-recommendations .recommendation:first-child {
    margin-top: 4px;
}

.gh-recommendations .recommendation a {
    display: grid;
    grid-template-columns: 24px auto;
    gap: 4px 12px;
}

.gh-recommendations .recommendation a:hover {
    opacity: 1;
}

.gh-recommendations .recommendation-favicon {
    grid-row: span 2;
    width: 100%;
    border-radius: 4px;
}

.gh-recommendations .recommendation-title {
    margin-top: -2px;
    font-size: 1.5rem;
    font-weight: 650;
    letter-spacing: -0.009em;
}

.gh-recommendations .recommendation a:hover .recommendation-title {
    opacity: 0.8;
}

.gh-recommendations .recommendation-url {
    order: 1;
    overflow: hidden;
    font-size: 1.4rem;
    line-height: 1.25;
    color: var(--color-secondary-text);
    text-overflow: ellipsis;
}

.gh-recommendations .recommendation-description {
    display: -webkit-box;
    display: none;
    overflow-y: hidden;
    grid-column: 2;
    font-size: 1.4rem;
    line-height: 1.4;
    color: var(--color-secondary-text);
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.gh-recommendations button {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    margin-top: 24px;
    padding: 0;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: 550;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    background-color: transparent;
    border: 0;
    color: var(--color-darker-gray);
}

.gh-recommendations button svg {
    margin-top: -1px;
    width: 12px;
}

.gh-recommendations button:hover {
    opacity: 0.8;
}

/* 14. Post/page
/* ---------------------------------------------------------- */

/* 14.1. Article */

.gh-article {
    --container-width: 1120px;

    word-break: break-word;
}

.gh-article-header {
    margin: clamp(40px, 3.64vw + 25.45px, 72px) 0 40px;
}

.gh-article-tag {
    margin-bottom: 12px;
    font-size: 1.3rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    text-transform: uppercase;
    color: var(--ghost-accent-color);
}

.gh-article-title {
    font-size: calc(clamp(3.4rem, 1.36vw + 2.85rem, 4.6rem) * var(--factor, 1));
    line-height: 1.1;
    letter-spacing: -0.022em;
}

.gh-article-excerpt {
    margin-top: clamp(12px, 0.45vw + 10.18px, 16px);
    max-width: 720px;
    font-size: clamp(1.7rem, 0.23vw + 1.61rem, 1.9rem);
    line-height: 1.45;
    letter-spacing: -0.018em;
}

.gh-article-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
    margin-left: 6px;
}

.gh-article-author-image {
    display: flex;
    margin-right: 8px;
}

.gh-article-author-image a {
    position: relative;
    margin: 0 -8px;
    width: 56px;
    height: 56px;
    overflow: hidden;
    background-color: var(--color-light-gray);
    border-radius: 50%;
    border: 3px solid var(--background-color);
}

.gh-article-author-image a:first-child {
    z-index: 10;
}

.gh-article-author-image a:nth-child(2) {
    z-index: 9;
}

.gh-article-author-image a:nth-child(3) {
    z-index: 8;
}

.gh-article-author-image :is(img, svg) {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gh-article-meta-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.gh-article-author-name {
    font-size: 1.6rem;
    font-weight: 650;
    letter-spacing: -0.013em;
}

.gh-article-meta-content {
    font-size: 1.35rem;
    font-weight: 500;
    line-height: 1.2;
    letter-spacing: -0.006em;
    color: var(--color-secondary-text);
}

.gh-article-image {
    grid-column: wide;
    margin-top: 40px;
}

.gh-article-image img {
    width: 100%;
}

/* 14.2. Page template */

.page-template .gh-article-header {
    margin-block: 72px 32px;
}

/* 14.3. Page without header */

.page-template .gh-content:only-child > *:first-child:not(.kg-width-full) {
    margin-top: 64px;
}

.page-template .gh-content > *:last-child:not(.kg-width-full) {
    margin-bottom: 6vw;
}

.page-template .gh-footer {
    margin-top: 0;
}

/* 15. Content
/* ---------------------------------------------------------- */

/* Content refers to styling all page and post content that is
created within the Ghost editor. The main content handles
headings, text, images and lists. We deal with cards lower down. */

.gh-content {
    font-size: var(--content-font-size, 1.7rem);
    letter-spacing: -0.01em;
}

/* Default vertical spacing */
.gh-content > * + * {
    margin-top: calc(28px * var(--content-spacing-factor, 1));
    margin-bottom: 0;
}

/* Remove space between full-width cards */
.gh-content > .kg-width-full + .kg-width-full:not(.kg-width-full.kg-card-hascaption + .kg-width-full) {
    margin-top: 0;
}

/* Add back a top margin to all headings,
unless a heading is the very first element in the post content */
.gh-content > [id]:not(:first-child) {
    margin-top: calc(56px * var(--content-spacing-factor, 1));
}

/* Add drop cap setting */
.post-template .gh-content.drop-cap > p:first-of-type:first-letter {
    margin :0 0.2em 0em 0;
    font-size: 3.1em;
    float:left;
    line-height: 1;
    margin-left: -1px;
    font-weight: 700;
}

.has-serif-body.post-template:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content.drop-cap > p:first-of-type:first-letter {
    font-size: 3.2em;
}

/* Add a small margin between a heading and paragraph after it */
.gh-content > [id] + p {
    margin-top: calc(12px * var(--content-spacing-factor, 1));
}

/* A larger margin before/after dividers, blockquotes and embeds */
.gh-content > :is(hr, blockquote, iframe) {
    position: relative;
    margin-top: calc(48px * var(--content-spacing-factor, 1)) !important;
}

.gh-content > :is(hr, blockquote, iframe) + * {
    margin-top: calc(48px * var(--content-spacing-factor, 1)) !important;
}

/* Now the content typography styles */
.gh-content h1 {
    font-size: calc(2.2em * var(--factor, 1));
    letter-spacing: -0.02em;
}

.gh-content h2 {
    font-size: calc(1.6em * var(--factor, 1));
    letter-spacing: -0.02em;
}

.gh-content h3 {
    font-size: calc(1.3em * var(--factor, 1));
    letter-spacing: -0.017em;
}

.gh-content a {
    color: var(--ghost-accent-color);
    text-decoration: underline;
}

.gh-content .kg-callout-card .kg-callout-text,
.gh-content .kg-toggle-card .kg-toggle-content > :is(ul, ol, p) {
    font-size: 0.95em;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content > blockquote,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content > ol,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content > ul,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content > dl,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content > p,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content .kg-callout-text,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content .kg-toggle-content > ol,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content .kg-toggle-content > ul,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-content .kg-toggle-content > p {
    font-family: var(--font-serif-alt);
}

.gh-content :is(ul, ol) {
    padding-left: 28px;
}

.gh-content :is(li + li, li :is(ul, ol)) {
    margin-top: 8px;
}

.gh-content ol ol li {
    list-style-type: lower-alpha;
}

.gh-content ol ol ol li {
    list-style-type: lower-roman;
}

.gh-content hr {
    width: 100%;
    height: 1px;
    background-color: var(--color-border);
    border: 0;
}

.gh-content .gh-table {
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
}

.gh-content .gh-table table {
    width: 100%;
    font-family: var(--font-sans);
    font-size: 1.5rem;
    white-space: nowrap;
    vertical-align: top;
    border-spacing: 0;
    border-collapse: collapse;
}

.gh-content .gh-table table th {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--color-darkgrey);
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 0.2px;
}

.gh-content .gh-table table :is(th, td),
.gh-content .gh-table table td {
    padding: 6px 12px;
    border-bottom: 1px solid var(--color-border);
}

.gh-content .gh-table table :is(th, td):first-child {
    padding-left: 0;
}

.gh-content .gh-table table :is(th, td):last-child {
    padding-right: 0;
}

.gh-content pre {
    overflow: auto;
    padding: 16px;
    font-size: 1.5rem;
    line-height: 1.5em;
    background: var(--color-lighter-gray);
    border-radius: 6px;
    font-family: var(--font-mono);
}

.gh-content :not(pre) > code {
    vertical-align: baseline;
    padding: 0.15em 0.4em;
    font-weight: 400;
    font-size: 0.95em;
    line-height: 1em;
    background: var(--color-lighter-gray);
    border-radius: 0.25em;
    font-family: var(--font-mono);
}

/* 16. Cards
/* ---------------------------------------------------------- */

/* Add extra margin before/after any cards, except for when immediately preceeded by a heading */

.gh-content :not(.kg-card):not(table):not([id]) + :is(.kg-card, table) {
    margin-top: calc(48px * var(--content-spacing-factor, 1));
}

.gh-content :is(.kg-card, table) + :not(.kg-card):not(table):not([id]) {
    margin-top: calc(48px * var(--content-spacing-factor, 1));
}

.gh-content :not(.kg-card):not([id]) + .kg-card.kg-width-full {
    margin-top: calc(68px * var(--content-spacing-factor, 1));
}

.gh-content .kg-card.kg-width-full + :not(.kg-card):not([id]) {
    margin-top: calc(68px * var(--content-spacing-factor, 1));
}

/* Image */

.kg-image {
    margin-right: auto;
    margin-left: auto;
}

/* Embed */

.kg-embed-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

/* Gallery */

.kg-image[width][height],
.kg-gallery-image {
    cursor: pointer;
}

.kg-image-card a:hover,
.kg-gallery-image a:hover {
    opacity: 1 !important;
}

/* Toggle */

.kg-card.kg-toggle-card .kg-toggle-heading-text {
    font-size: 2rem;
    font-weight: 700;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .kg-toggle-card .kg-toggle-heading-text {
    font-family: var(--font-serif);
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .kg-toggle-card .kg-toggle-heading-text {
    font-family: var(--font-mono);
}

/* Callout */

.kg-callout-card.kg-card {
    border-radius: 0.25em;
}

.kg-callout-card-accent a {
    text-decoration: underline;
}

/* Blockquote */

blockquote:not([class]) {
    padding-left: 2rem;
    border-left: 4px solid var(--ghost-accent-color);
}

blockquote.kg-blockquote-alt {
    font-style: normal;
    font-weight: 400;
    color: var(--color-secondary-text);
}

/* Button */

.kg-card.kg-button-card .kg-btn {
    height: unset;
    padding: .6em 1.2em;
    text-align: center;
    font-size: 1em;
    line-height: 1.2em;
}

/* Header */

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .kg-card.kg-header-card h2.kg-header-card-header {
    font-family: var(--font-serif);
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .kg-card.kg-header-card h2.kg-header-card-header {
    font-family: var(--font-mono);
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .kg-header-card h3.kg-header-card-subheader {
    font-family: var(--font-serif);
}

/* Bookmark */

.kg-bookmark-card .kg-bookmark-container {
    border-radius: 0.25em !important;
}

.kg-bookmark-card .kg-bookmark-container:hover {
    opacity: 1;
}

.kg-bookmark-card a.kg-bookmark-container,
.kg-bookmark-card a.kg-bookmark-container:hover {
    background: var(--background-color) !important;
    color: var(--color-darker-gray) !important;
}

.kg-bookmark-card .kg-bookmark-content {
    padding: 1.15em;
}

.kg-bookmark-card .kg-bookmark-title {
    font-size: 0.9em;
}

.kg-bookmark-card .kg-bookmark-description {
    max-height: none;
    margin-top: 0.3em;
    font-size: 0.8em;
}

.kg-bookmark-card .kg-bookmark-metadata {
    font-size: 0.8em;
}

.kg-bookmark-card .kg-bookmark-thumbnail img {
    border-radius: 0 0.2em 0.2em 0;
}

/* Product */

.kg-product-card.kg-card .kg-product-card-image {
    margin-bottom: 12px;
}

.kg-product-card.kg-card a.kg-product-card-button {
    height: 2.8em;
    margin-top: 12px;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .kg-product-card.kg-card .kg-product-card-title {
    font-family: var(--font-serif);
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .kg-product-card.kg-card .kg-product-card-title {
    font-family: var(--font-mono);
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .kg-product-card-description :is(p, ul, ol) {
    font-family: var(--font-serif-alt);
}

/* File */

.kg-file-card.kg-card .kg-file-card-container {
    padding: 0.6em;
}

.kg-file-card.kg-card .kg-file-card-contents {
    margin: 4px 8px 6px;
}

.kg-file-card.kg-card .kg-file-card-metadata {
    font-size: 0.9em;
}

.kg-file-card.kg-card .kg-file-card-filesize::before {
    margin-right: 6px;
    margin-left: 6px;
}

/* Caption */

figcaption {
    margin-top: 12px;
    font-size: 1.4rem;
    text-align: center;
}

.kg-card.kg-width-full figcaption {
    padding: 0 16px;
}

figcaption a {
    color: var(--ghost-accent-color);
    text-decoration: underline;
}

/* 17. Comments
/* ---------------------------------------------------------- */

.gh-comments {
    margin-top: 48px;
}

/* 18. Recent posts
/* ---------------------------------------------------------- */

.post-template .gh-container {
    margin-top: 120px;
}

.post-template .gh-container-inner {
    display: block;
}

.post-template .gh-container.is-grid .gh-feed {
    grid-template-columns: repeat(4, 1fr);
}

.post-template .gh-container .gh-container-title {
    display: block;
}

/* 19. Archive
/* ---------------------------------------------------------- */

.gh-archive {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    gap: var(--grid-gap);
    margin-block: 80px 24px;
}

.gh-archive.has-image {
    margin-top: 48px;
}

.gh-archive-inner {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--grid-gap);
    grid-column: 1 / -1;
    padding-bottom: clamp(40px, 2.73vw + 29.09px, 64px);
    border-bottom: 1px solid var(--color-border);
}

.gh-archive.has-image .gh-archive-inner {
    align-items: center;
    grid-column: 1 / -1;
}

.gh-archive:not(.has-sidebar):not(.has-image) .gh-archive-inner {
    grid-column: 3 / span 12;
}

.gh-archive .gh-article-image {
    grid-column: auto;
    margin-top: 0;
}

:is(.tag-template, .author-template) .gh-container {
    margin-top: 0;
}

.author-template .gh-archive-inner {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 24px;
}

.author-template .gh-article-image {
    margin-top: 0;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
}

.author-template .gh-article-title {
    font-size: 3.6rem;
}

.gh-author-meta {
    display: flex;
    gap: 10px;
    margin-top: 14px;
    font-size: 1.5rem;
    font-weight: 550;
    color: var(--color-secondary-text);
}

.gh-author-meta a {
    color: inherit;
}

.gh-author-social {
    display: flex;
    gap: 16px;
    align-items: center;
    padding-left: 3px;
}

.gh-author-social svg {
    width: 20px;
    height: 20px;
}

@media (max-width: 1199px) {
    .gh-archive {
        display: block;
    }
}

@media (max-width: 767px) {
    .gh-archive-inner {
        display: flex;
        flex-direction: column-reverse;
        align-items: flex-start;
    }

    .author-template .gh-archive-inner {
        flex-direction: column-reverse;
    }
}

/* 20. Design settings
/* ---------------------------------------------------------- */

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) {
    --factor: 1.15;
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) {
    --factor: 1.1;
}

.has-sans-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) :is(.is-title, .gh-content :is(h2, h3)) {
    font-family: var(--gh-font-heading, var(--font-sans));
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) :is(.is-title, .gh-content :is(h2, h3)) {
    font-family: var(--gh-font-heading, var(--font-serif));
    font-weight: 550;
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) :is(.is-title, .gh-content :is(h2, h3)) {
    font-family: var(--gh-font-heading, var(--font-mono));
}

.has-sans-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .is-body {
    font-family: var(--gh-font-body, var(--font-sans));
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .is-body {
    font-family: var(--gh-font-body, var(--font-serif-alt));
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-header.is-classic .gh-header-title {
    font-weight: 550;
    letter-spacing: -0.015em;
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-header.is-classic .gh-header-title {
    letter-spacing: -0.01em;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-form {
    border-radius: 0;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-card-title {
    line-height: 1.15;
    letter-spacing: -0.006em;
    font-size: calc(2.0rem*var(--factor, 1))
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-featured-feed .gh-card-title {
    font-size: calc(1.6rem*var(--factor, 1))
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-featured-feed .gh-card-title {
    font-size: calc(1.5rem*var(--factor, 1));
    letter-spacing: 0;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-header.is-highlight .gh-featured-feed .gh-card-title {
    font-size: clamp(1.6rem, 0.23vw + 1.51rem, 1.8rem);
}

.has-mono-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-card-title {
    font-size: calc(1.8rem*var(--factor, 1));
    line-height: 1.2;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-about-title {
    letter-spacing: -0.009em;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-footer-signup-header {
    letter-spacing: -0.019em;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-article-title {
    letter-spacing: -0.019em;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) {
    --content-font-size: 1.9rem;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-card-excerpt {
    font-size: 1.65rem;
    line-height: 1.4;
    letter-spacing: 0.0005em;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-excerpt,
.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-header.is-highlight .gh-card:first-child .gh-card-excerpt {
    font-size: 1.8rem;
    letter-spacing: -0.001em;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-header.is-magazine .gh-header-inner>.gh-card .gh-card-title,
.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-header.is-highlight .gh-header-left .gh-card-title {
    font-weight: 550;
    font-size: clamp(3.2rem,1.82vw + 2.47rem,4.9rem)
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-about-description {
    font-size: 1.6rem;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-article-excerpt {
    letter-spacing: 0;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .gh-footer-signup-subhead {
    letter-spacing: 0;
}

:is(.has-serif-title,.has-mono-title):not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) :is(.gh-button, .gh-form, .gh-form-input),
body[class*=" gh-font-heading"]:not(.gh-font-heading-fira-sans):not(.gh-font-heading-inter):not(.gh-font-heading-manrope):not(.gh-font-heading-noto-sans):not(.gh-font-heading-nunito):not(.gh-font-heading-poppins):not(.gh-font-heading-roboto):not(.gh-font-heading-space-grotesk) :is(.gh-button, .gh-form, .gh-form-input) {
    border-radius: 0;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .gh-cta-title {
    font-size: 4.8rem;
}

/* 21. Footer
/* ---------------------------------------------------------- */

.gh-footer {
    margin-top: 12vw;
    font-size: 1.5rem;
    color: var(--color-darker-gray);
}

.gh-footer a:not(.gh-button) {
    color: inherit;
}

/* 21.1 Footer styles */

.gh-footer.has-accent-color {
    background-color: var(--ghost-accent-color);
}

.gh-footer.has-accent-color .gh-footer-bar {
    border-top: 0;
}

.gh-footer.has-accent-color .gh-button {
    background-color: #fff;
    color: #15171a;
}

/* 21.2. Footer bar */

.gh-footer-bar {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    column-gap: 32px;
    margin-bottom: 100px;
    padding-block: 28px;
    font-weight: 550;
    border-block: 1px solid var(--color-border);
}

.gh-footer-logo {
    position: relative;
    font-family: var(--gh-font-heading, var(--font-sans));
    font-size: calc(2.4rem * var(--factor, 1));
    font-weight: 725;
    letter-spacing: -0.015em;
    white-space: nowrap;
}

.gh-footer-logo img {
    max-height: 40px;
}

.gh-footer-menu .nav {
    display: flex;
    justify-content: center;
    gap: 8px 28px;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.gh-footer-copyright {
    text-align: right;
    white-space: nowrap;
}

.gh-footer-copyright a {
    text-decoration: underline;
}

/* 21.3. Footer signup */

.gh-footer-signup {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 160px;
    text-align: center;
}

.gh-footer-signup-header {
    font-size: calc(clamp(2.8rem,1.36vw + 2.25rem,4rem) * var(--factor, 1));
    font-weight: 700;
    letter-spacing: -0.03em;
}

.gh-footer-signup-subhead {
    margin-top: 12px;
    max-width: 640px;
    font-size: 1.8rem;
    font-weight: 450;
    line-height: 1.4;
    letter-spacing: -0.014em;
    opacity: 0.75;
}

.gh-footer-signup .gh-form {
    margin-top: 40px;
}

@media (max-width: 991px) {
    .gh-footer-bar {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
}

/* 22. Lightbox
/* ---------------------------------------------------------- */

.pswp {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3999999;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    -ms-touch-action: none;
    touch-action: none;
    outline: none;
    backface-visibility: hidden;
    -webkit-text-size-adjust: 100%;
}

.pswp img {
    max-width: none;
}

.pswp--animate_opacity {
    opacity: 0.001;
    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
    will-change: opacity;
}

.pswp--open {
    display: block;
}

.pswp--zoom-allowed .pswp__img {
    cursor: zoom-in;
}

.pswp--zoomed-in .pswp__img {
    cursor: grab;
}

.pswp--dragging .pswp__img {
    cursor: grabbing;
}

.pswp__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    opacity: 0;
    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: opacity;
}

.pswp__scroll-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.pswp__container,
.pswp__zoom-wrap {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    touch-action: none;
    backface-visibility: hidden;
}

.pswp__container,
.pswp__img {
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

.pswp__zoom-wrap {
    position: absolute;
    width: 100%;
    transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
    transform-origin: left top;
}

.pswp--animated-in .pswp__bg,
.pswp--animated-in .pswp__zoom-wrap {
    transition: none;
}

.pswp__item {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
}

.pswp__img {
    position: absolute;
    top: 0;
    left: 0;
    width: auto;
    height: auto;
}

.pswp__img--placeholder {
    backface-visibility: hidden;
}

.pswp__img--placeholder--blank {
    background: var(--color-black);
}

.pswp--ie .pswp__img {
    top: 0;
    left: 0;
    width: 100% !important;
    height: auto !important;
}

.pswp__error-msg {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    margin-top: -8px;
    font-size: 14px;
    line-height: 16px;
    color: var(--color-secondary-text);
    text-align: center;
}

.pswp__error-msg a {
    color: var(--color-secondary-text);
    text-decoration: underline;
}

.pswp__button {
    position: relative;
    display: block;
    float: right;
    width: 44px;
    height: 44px;
    padding: 0;
    margin: 0;
    overflow: visible;
    appearance: none;
    cursor: pointer;
    background: none;
    border: 0;
    box-shadow: none;
    transition: opacity 0.2s;
}

.pswp__button:focus,
.pswp__button:hover {
    opacity: 1;
}

.pswp__button:active {
    outline: none;
    opacity: 0.9;
}

.pswp__button::-moz-focus-inner {
    padding: 0;
    border: 0;
}

.pswp__ui--over-close .pswp__button--close {
    opacity: 1;
}

.pswp__button,
.pswp__button--arrow--left::before,
.pswp__button--arrow--right::before {
    width: 44px;
    height: 44px;
    background: url("../images/default-skin.png") 0 0 no-repeat;
    background-size: 264px 88px;
}

@media (-webkit-min-device-pixel-ratio: 1.1), (-webkit-min-device-pixel-ratio: 1.09375), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {
    .pswp--svg .pswp__button,
    .pswp--svg .pswp__button--arrow--left::before,
    .pswp--svg .pswp__button--arrow--right::before {
        background-image: url("../images/default-skin.svg");
    }

    .pswp--svg .pswp__button--arrow--left,
    .pswp--svg .pswp__button--arrow--right {
        background: none;
    }
}

.pswp__button--close {
    background-position: 0 -44px;
}

.pswp__button--share {
    background-position: -44px -44px;
}

.pswp__button--fs {
    display: none;
}

.pswp--supports-fs .pswp__button--fs {
    display: block;
}

.pswp--fs .pswp__button--fs {
    background-position: -44px 0;
}

.pswp__button--zoom {
    display: none;
    background-position: -88px 0;
}

.pswp--zoom-allowed .pswp__button--zoom {
    display: block;
}

.pswp--zoomed-in .pswp__button--zoom {
    background-position: -132px 0;
}

.pswp--touch .pswp__button--arrow--left,
.pswp--touch .pswp__button--arrow--right {
    visibility: hidden;
}

.pswp__button--arrow--left,
.pswp__button--arrow--right {
    position: absolute;
    top: 50%;
    width: 70px;
    height: 100px;
    margin-top: -50px;
    background: none;
}

.pswp__button--arrow--left {
    left: 0;
}

.pswp__button--arrow--right {
    right: 0;
}

.pswp__button--arrow--left::before,
.pswp__button--arrow--right::before {
    position: absolute;
    top: 35px;
    width: 32px;
    height: 30px;
    content: "";
}

.pswp__button--arrow--left::before {
    left: 6px;
    background-position: -138px -44px;
}

.pswp__button--arrow--right::before {
    right: 6px;
    background-position: -94px -44px;
}

.pswp__counter {
    position: absolute;
    top: 0;
    left: 0;
    height: 44px;
    padding: 0 15px;
    font-size: 11px;
    font-weight: 700;
    line-height: 44px;
    color: var(--color-white);
    user-select: none;
}

.pswp__caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    min-height: 44px;
}

.pswp__caption__center {
    max-width: 420px;
    padding: 25px 15px 30px;
    margin: 0 auto;
    font-size: 11px;
    line-height: 1.6;
    color: var(--color-white);
    text-align: center;
}

.pswp__caption__center .post-caption-title {
    margin-bottom: 7px;
    font-size: 15px;
    font-weight: 500;
    text-transform: uppercase;
}

.pswp__caption__center .post-caption-meta-item + .post-caption-meta-item::before {
    padding: 0 4px;
    content: "\02022";
}

.pswp__caption--empty {
    display: none;
}

.pswp__caption--fake {
    visibility: hidden;
}

.pswp__preloader {
    position: absolute;
    top: 0;
    left: 50%;
    width: 44px;
    height: 44px;
    margin-left: -22px;
    opacity: 0;
    transition: opacity 0.25s ease-out;
    direction: ltr;
    will-change: opacity;
}

.pswp__preloader__icn {
    width: 20px;
    height: 20px;
    margin: 12px;
}

.pswp__preloader--active {
    opacity: 1;
}

.pswp__preloader--active .pswp__preloader__icn {
    background: url("../images/preloader.gif") 0 0 no-repeat;
}

.pswp--css_animation .pswp__preloader--active {
    opacity: 1;
}

.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {
    animation: clockwise 500ms linear infinite;
}

.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {
    animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;
}

.pswp--css_animation .pswp__preloader__icn {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 14px;
    height: 14px;
    margin: 0;
    background: none;
    opacity: 0.75;
}

.pswp--css_animation .pswp__preloader__cut {
    position: relative;
    width: 7px;
    height: 14px;
    overflow: hidden;
}

.pswp--css_animation .pswp__preloader__donut {
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    width: 14px;
    height: 14px;
    margin: 0;
    background: none;
    border: 2px solid var(--color-white);
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-radius: 50%;
}

@media screen and (max-width: 1024px) {
    .pswp__preloader {
        position: relative;
        top: auto;
        left: auto;
        float: right;
        margin: 0;
    }
}

@keyframes clockwise {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes donut-rotate {
    0% {
        transform: rotate(0);
    }

    50% {
        transform: rotate(-140deg);
    }

    100% {
        transform: rotate(0);
    }
}

.pswp__ui {
    z-index: 1550;
    visibility: visible;
    opacity: 1;
    -webkit-font-smoothing: auto;
}

.pswp__top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 44px;
}

.pswp__caption,
.pswp__top-bar,
.pswp--has_mouse .pswp__button--arrow--left,
.pswp--has_mouse .pswp__button--arrow--right {
    backface-visibility: hidden;
    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
    will-change: opacity;
}

.pswp--has_mouse .pswp__button--arrow--left,
.pswp--has_mouse .pswp__button--arrow--right {
    visibility: visible;
}

.pswp__ui--idle .pswp__top-bar {
    opacity: 0;
}

.pswp__ui--idle .pswp__button--arrow--left,
.pswp__ui--idle .pswp__button--arrow--right {
    opacity: 0;
}

.pswp__ui--hidden .pswp__top-bar,
.pswp__ui--hidden .pswp__caption,
.pswp__ui--hidden .pswp__button--arrow--left,
.pswp__ui--hidden .pswp__button--arrow--right {
    opacity: 0.001;
}

.pswp__ui--one-slide .pswp__button--arrow--left,
.pswp__ui--one-slide .pswp__button--arrow--right,
.pswp__ui--one-slide .pswp__counter {
    display: none;
}

.pswp__element--disabled {
    display: none !important;
}

.pswp--minimal--dark .pswp__top-bar {
    background: none;
}
