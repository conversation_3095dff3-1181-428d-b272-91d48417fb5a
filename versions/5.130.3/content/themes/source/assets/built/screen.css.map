{"version": 3, "sources": ["screen.css"], "names": [], "mappings": "AAoDA,MACI,kBAAmB,CACnB,oCAAuC,CACvC,0BAA2B,CAC3B,qBAAsB,CACtB,sBAAuB,CACvB,2BAA4B,CAC5B,kBAAmB,CACnB,6CAA8C,CAC9C,sCAAyC,CACzC,8BAAiC,CACjC,mCAAsC,CACtC,yJAAqK,CACrK,8CAAkD,CAClD,oCAAuC,CACvC,+FAAsG,CACtG,wBAAyB,CACzB,qDAAwD,CACxD,eACJ,CAEA,qEAEI,uCAA4C,CAC5C,wBAAyB,CACzB,0CAA+C,CAC/C,kCAAuC,CACvC,sCACJ,CAUA,iBACI,qBACJ,CAEA,EACI,QACJ,CAEA,KACI,eACJ,CAEA,KAEI,kCAAmC,CACnC,iCAAkC,CAFlC,eAGJ,CAEA,6BACI,aAAc,CACd,WAAY,CACZ,cACJ,CAEA,OACI,aACJ,CAEA,6BACI,YACJ,CAEA,oBACI,wBACJ,CAEA,kBACI,eACJ,CAKA,KAGI,wCAAyC,CACzC,+BAAgC,CAHhC,gDAAkD,CAClD,gBAGJ,CAEA,kBACI,mDACJ,CAEA,EACI,8BAA+B,CAC/B,oBACJ,CAEA,QACI,UACJ,CAEA,WAGI,kBAAmB,CASnB,0CAA2C,CAC3C,QAAS,CACT,mBAAoB,CAJpB,wBAAyB,CACzB,cAAe,CAVf,mBAAoB,CAKpB,gBAAiB,CACjB,eAAgB,CALhB,QAAU,CAEV,sBAAuB,CAIvB,sBAAwB,CACxB,aAAc,CAJd,kBAUJ,CAEA,iBACI,WACJ,CAEA,gBAEI,kBAAmB,CAOnB,4BAA6B,CAC7B,QAAS,CAHT,8BAA+B,CAC/B,cAAe,CAPf,mBAAoB,CAIpB,WAAY,CAFZ,sBAAuB,CAQvB,YAAa,CALb,SAAU,CAFV,UAQJ,CAEA,oCACI,UACJ,CAEA,oBAEI,WAAY,CADZ,UAEJ,CAEA,SAEI,kBAAmB,CASnB,0CAA2C,CAD3C,kBAAmB,CATnB,YAAa,CAMb,gBAAiB,CACjB,eAAgB,CAFhB,WAAY,CAGZ,sBAAwB,CALxB,eAAgB,CADhB,iBAAkB,CASlB,oCAAsC,CAPtC,UAQJ,CAEA,iBACI,mBACJ,CAEA,eACI,wBACJ,CAEA,eACI,iCACJ,CAEA,0EAEI,oCACJ,CAEA,eAWI,4BAA6B,CAF7B,QAAS,CACT,kBAAmB,CAJnB,iBAAkB,CADlB,WAAY,CAHZ,OAAQ,CAKR,sBAAuB,CACvB,eAAgB,CAIhB,YAAa,CATb,mBAAoB,CAFpB,iBAAkB,CAYlB,0BAA4B,CAT5B,UAUJ,CAEA,iCAEI,oBACJ,CAHA,iDAEI,oBACJ,CAEA,yZAEI,mBACJ,CAEA,wCACI,UACJ,CAEA,0EAEI,UACJ,CAEA,8GAGI,yBACJ,CAJA,yIAGI,yBACJ,CAEA,+CACI,aACJ,CAEA,iEAGI,oBACJ,CAJA,8JAGI,oBACJ,CAEA,qBACI,0BAA0B,CAE1B,iCAAkC,CAClC,cAAe,CAHf,yBAA0B,CAC1B,eAGJ,CAEA,qaAEI,0BAA0B,CAA1B,yBACJ,CAEA,oBAKI,iBAAkB,CADlB,WAAY,CADZ,mBAAoB,CAFpB,iBAAkB,CAClB,SAIJ,CAEA,aAKI,iCAAkC,CADlC,WAAY,CAFZ,SAAU,CADV,iBAAkB,CAElB,UAGJ,CAEA,kHACI,SACJ,CAEA,wBACI,YAAa,CAEb,cAAe,CADf,iBAEJ,CAEA,+CACI,iBACJ,CAMA,2FACI,aACJ,CAEA,8BAKI,gBAAiB,CACjB,eAAgB,CAHhB,eAAgB,CAFhB,iBAAkB,CAClB,QAAS,CAET,UAGJ,CAEA,yBACI,SACI,gBACJ,CAEA,oBACI,mBACJ,CAEA,8BACI,YACJ,CAEA,6BACI,cAAe,CAIf,WAAY,CAFZ,cAAe,CADf,eAAgB,CAEhB,UAEJ,CACJ,CAKA,aAEI,YAAa,CACb,qBAAsB,CACtB,gBAAiB,CAHjB,iBAIJ,CAEA,UACI,yBACJ,CAEA,oBACI,SACJ,CAEA,UACI,aAAc,CACd,gCAAiC,CACjC,UACJ,CAEA,SACI,WACJ,CAEA,0CAEI,sEAAyE,CACzE,uFAA2F,CAC3F,uCAAyC,CAEzC,YAAa,CACb,yJAMJ,CAEA,aACI,gBACJ,CAEA,oCAEI,gBACJ,CAEA,eACI,gBACJ,CAKA,eAII,wCAAyC,CACzC,8BAA+B,CAH/B,gBAAiB,CACjB,eAAgB,CAFhB,YAKJ,CAEA,0EACI,aACJ,CAEA,qBAII,kBAAmB,CADnB,oBAAgB,CAAhB,eAAgB,CAFhB,YAAa,CACb,wBAAyB,CAGzB,WACJ,CAEA,qBACI,aACJ,CAEA,oBAEI,mDAAqD,CACrD,uCAA0C,CAC1C,eAAgB,CAChB,sBAAwB,CAJxB,iBAAkB,CAKlB,kBACJ,CAEA,wBACI,eACJ,CAEA,oBAGI,kBAAmB,CAFnB,YAAa,CACb,QAEJ,CAEA,oBAGI,kBAAmB,CAFnB,mBAAoB,CACpB,QAAS,CAKT,eAAgB,CAFhB,QAAS,CADT,SAAU,CAEV,kBAEJ,CAEA,+BAGI,iBAAkB,CADlB,aAAc,CADd,iBAAkB,CAGlB,sBACJ,CAEA,mCAEI,WAAY,CADZ,UAEJ,CAEA,uBAGI,kBAAmB,CAEnB,wCAAyC,CAJzC,YAAa,CACb,QAAS,CAET,wBAEJ,CAEA,uDACI,0CACJ,CAEA,uBAGI,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,kBACJ,CAEA,kCACI,iBAAkB,CAClB,eACJ,CAEA,WAEI,gBAAiB,CADjB,iBAEJ,CAEA,yBACI,oBACI,kBACJ,CAEA,uBACI,6BAA8B,CAC9B,QAAS,CACT,UACJ,CAEA,kCACI,YACJ,CACJ,CAEA,yBACI,gCACI,YACJ,CAEA,oEACI,SACJ,CACJ,CAIA,gCACI,0CACJ,CAEA,2CACI,qBAAsB,CACtB,aACJ,CAUA,iDACI,mCACJ,CAEA,yBACI,+DACI,QACJ,CACJ,CAEA,yBACI,gDAEI,gBAAiB,CADjB,kBAEJ,CACJ,CAQA,mDACI,kCACJ,CAEA,mDACI,mBACJ,CAEA,qDACI,QACJ,CAEA,yBACI,kDACI,iBACJ,CACJ,CASA,0BAEI,WAAY,CADZ,iBAEJ,CAEA,+CACI,kCACJ,CAEA,+CAEI,kBAAmB,CADnB,YAAa,CAGb,mBAAoB,CADpB,gBAAiB,CAEjB,eACJ,CAEA,yBACI,+CACI,gBACJ,CACJ,CAEA,yBACI,+CACI,SACJ,CAEA,+CAEI,kBAAmB,CADnB,YAAa,CAEb,WACJ,CAEA,8CAEI,eAAkB,CADlB,gBAAiB,CAGjB,WAAY,CADZ,sBAAuB,CAEvB,aACJ,CAEA,yGAQI,oCAAqC,CADrC,UAAW,CADX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAClB,QAAS,CAET,UAIJ,CAEA,oDACI,SACJ,CAEA,iDAEI,eAAkB,CADlB,gBAAiB,CAEjB,6BACJ,CAEA,4DACI,YAAa,CACb,QAAS,CACT,UACJ,CACJ,CAIA,aAUI,qBAAsB,CACtB,iBAAkB,CAClB,oEAA0E,CAL1E,eAAgB,CAMhB,SAAU,CAPV,cAAe,CALf,iBAAkB,CAElB,WAAY,CAKZ,eAAgB,CANhB,QAAS,CAaT,8BAAiC,CADjC,oCAAwC,CALxC,iBAAkB,CAJlB,WAAY,CADZ,UAYJ,CAEA,qBAEI,UAAW,CADX,UAEJ,CAEA,+BAII,oBAAgB,CAAhB,eAAgB,CAHhB,YAAa,CAEb,qBAAsB,CADtB,6BAA8B,CAI9B,iBAAkB,CADlB,UAEJ,CAEA,+BAEI,SAAU,CACV,uBAAwB,CAFxB,kBAGJ,CAEA,kBAKI,uBAAyB,CAJzB,aAAc,CAEd,eAAgB,CADhB,gBAAiB,CAEjB,kBAEJ,CAEA,oCACI,aACJ,CAIA,WAGI,kBAAmB,CAFnB,YAAa,CAIb,YAAa,CAHb,sBAAuB,CAKvB,eAAgB,CADhB,iBAAkB,CAFlB,iBAIJ,CAEA,eAEI,WAAY,CADZ,UAEJ,CAMA,8DACI,YACJ,CAEA,mCACI,aACJ,CAEA,yBACI,WACI,YACJ,CAEA,eACI,WACJ,CAEA,oCAGI,QAAS,CADT,yBAA0B,CAD1B,gCAGJ,CAEA,oCAII,kBAAmB,CAHnB,YAAa,CAEb,mBAAoB,CADpB,mCAAoC,CAGpC,WACJ,CAEA,mCACI,gBACJ,CAEA,yEAGI,sBAAuB,CAEvB,SAAU,CAHV,cAAe,CAEf,iBAEJ,CAEA,mCAEI,uBAAwB,CADxB,eAEJ,CAEA,oBAEI,kBAAmB,CADnB,QAAS,CAET,eACJ,CAEA,sBACI,iBAAkB,CAClB,eAAgB,CAChB,mBACJ,CAEA,uBACI,SAAU,CACV,0BACJ,CAEA,sCACI,iBACJ,CAEA,qDACI,SAAU,CACV,yBACJ,CAEA,0BAEI,iBAAkB,CAClB,mBAAoB,CAFpB,UAGJ,CAEA,qCACI,iBACJ,CAEA,SACI,sBACJ,CAEA,uBAMI,gCAAiC,CAFjC,WAAY,CAFZ,OAAQ,CAGR,iBAAkB,CAJlB,cAAe,CAEf,eAIJ,CAEA,yFAII,SAAU,CAFV,eAAgB,CAChB,kBAEJ,CAEA,4BACI,YAAa,CACb,qBACJ,CAEA,+BACI,SAAU,CAEV,uBAAwB,CADxB,oCAEJ,CAEA,8CAQI,kBAAmB,CALnB,QAAS,CAET,mBAAoB,CACpB,qBAAsB,CACtB,QAAS,CAHT,MAAO,CAKP,8BAA+B,CAR/B,eAAgB,CAChB,OAQJ,CAEA,6DACI,SAAU,CAGV,uBAAwB,CAFxB,oCAAwC,CACxC,oBAEJ,CAEA,6CACI,oBACJ,CAEA,iBACI,SACJ,CACJ,CAKA,SACI,iBACJ,CAEA,cACI,YAAa,CACb,qBAAsB,CACtB,QACJ,CAEA,oBACI,SACJ,CAEA,eAGI,iBAAoB,CADpB,aAAc,CADd,iBAGJ,CAEA,mBAII,WAAY,CAFZ,OAAQ,CAGR,mBAAiB,CAAjB,gBAAiB,CAJjB,iBAAkB,CAElB,UAGJ,CAEA,iBACI,WACJ,CAEA,aACI,YAAa,CAEb,gBAAiB,CACjB,eAAgB,CAChB,oBAAsB,CAHtB,iBAAkB,CAIlB,wBACJ,CAEA,eACI,uCAA0C,CAC1C,eAAgB,CAChB,sBAAwB,CACxB,eACJ,CAEA,mCACI,UACJ,CAEA,iBAMI,oBAAqB,CACrB,2BAA4B,CAN5B,mBAAoB,CAGpB,iBAAkB,CAClB,eAAgB,CAFhB,cAAe,CADf,iBAMJ,CAEA,cACI,kBAAmB,CAMnB,iCAAkC,CAJlC,iBAAkB,CAClB,eAAgB,CAEhB,sBAAwB,CADxB,eAAgB,CAHhB,kBAMJ,CAEA,kBAGI,oBAAqB,CAErB,WAAY,CACZ,gBAAiB,CALjB,iBAAkB,CAClB,OAAQ,CAER,UAGJ,CAEA,0BACI,cACJ,CAEA,qCACI,WAAY,CACZ,gBACJ,CAKA,WAEI,eAAgB,CADhB,iBAEJ,CAEA,iBAEI,eAAgB,CADhB,iBAEJ,CAIA,wCACI,YAAa,CAEb,mBAAoB,CADpB,oCAEJ,CAEA,4CACI,YAAa,CACb,qBAAsB,CACtB,mBAAoB,CACpB,UACJ,CAEA,uCACI,oBACJ,CAEA,iDAEI,oBAAuB,CADvB,iBAEJ,CAEA,wCACI,iBACJ,CAEA,+DACI,QACJ,CAEA,gEACI,qBACJ,CAEA,8DACI,aAAc,CACd,kBACJ,CAEA,gEACI,6CAAgD,CAChD,eAAgB,CAEhB,sBAAwB,CADxB,eAEJ,CAEA,kEAEI,gBAAiB,CACjB,qBAAuB,CAFvB,2CAA+C,CAG/C,aACJ,CAEA,2EACI,eACJ,CAEA,gGACI,YACJ,CAEA,6DACI,YACJ,CAEA,+GAQI,oCAAqC,CAHrC,UAAW,CAEX,WAAY,CAHZ,6BAAgC,CAFhC,iBAAkB,CAClB,KAAM,CAGN,SAGJ,CAEA,uDAEI,SAAU,CADV,8BAEJ,CAEA,qEAOI,oCAAqC,CAHrC,UAAW,CAEX,UAAW,CAHX,MAAO,CAFP,iBAAkB,CAClB,4BAA+B,CAG/B,UAGJ,CAEA,0BACI,wCACI,oCACJ,CAEA,iDACI,oBACJ,CAEA,uCACI,gBACJ,CAEA,4DAGI,kBAAmB,CAFnB,gBAAmB,CACnB,UAEJ,CAEA,iDACI,MACJ,CAEA,2EACI,aACJ,CAEA,wCACI,iBACJ,CAEA,+CAOI,oCAAqC,CAHrC,UAAW,CAEX,UAAW,CAHX,MAAO,CAFP,iBAAkB,CAClB,4BAA+B,CAG/B,UAGJ,CAEA,wDACI,YACJ,CAEA,uDAOI,oCAAqC,CAHrC,UAAW,CAEX,WAAY,CALZ,iBAAkB,CAElB,8BAAiC,CADjC,KAAM,CAGN,SAGJ,CACJ,CAEA,yBACI,4EACI,YACJ,CACJ,CAEA,yBACI,wCACI,YAAa,CACb,qBAAsB,CACtB,mBACJ,CAEA,4DACI,qBACJ,CAEA,sCACI,uBACJ,CAEA,uCAQI,oCAAqC,CAHrC,UAAW,CAJX,uBAAyB,CAMzB,UAAW,CAHX,MAAO,CAFP,iBAAkB,CAClB,4BAA+B,CAG/B,UAGJ,CACJ,CAIA,yCACI,YAAa,CAEb,mBAAoB,CADpB,oCAEJ,CAEA,wCAEI,kBAAmB,CADnB,iBAEJ,CAEA,iDACI,kBAAmB,CACnB,eACJ,CAEA,+DACI,QACJ,CAEA,gEACI,gBACJ,CAEA,8DACI,aAAc,CACd,kBACJ,CAEA,gEACI,6CAAgD,CAChD,eAAgB,CAEhB,sBAAwB,CADxB,eAEJ,CAEA,kEAEI,gBAAiB,CADjB,2CAEJ,CAEA,2EACI,eACJ,CAEA,0CAEI,YAAa,CACb,qBAAsB,CACtB,mBAAoB,CACpB,oBAAuB,CAJvB,iBAKJ,CAMA,iJACI,YACJ,CAEA,yCACI,iBACJ,CAEA,qCACI,YAAa,CACb,SACJ,CAEA,0CACI,YAAa,CACb,qBACJ,CAEA,yDACI,8CACJ,CAEA,8FAQI,oCAAqC,CAHrC,UAAW,CAEX,WAAY,CALZ,iBAAkB,CAElB,8BAAiC,CADjC,KAAM,CAGN,SAGJ,CAEA,wCAOI,oCAAqC,CAHrC,UAAW,CAEX,UAAW,CAHX,MAAO,CAFP,iBAAkB,CAClB,4BAA+B,CAG/B,UAGJ,CAEA,iEACI,YACJ,CAEA,0BACI,yCACI,mCACJ,CAEA,wCACI,kBACJ,CAEA,0CACI,gBACJ,CAEA,yCACI,gBACJ,CAEA,0CACI,YAAa,CACb,mCACJ,CAEA,0DACI,kCACJ,CAEA,yDAOI,oCAAqC,CAHrC,UAAW,CAEX,WAAY,CAHZ,6BAAgC,CAFhC,iBAAkB,CAClB,KAAM,CAGN,SAGJ,CACJ,CAEA,yBACI,+EACI,YACJ,CACJ,CAEA,yBACI,yCACI,YAAa,CACb,qBACJ,CAEA,0CACI,YAEJ,CAEA,uCACI,uBACJ,CAEA,oEACI,mBACJ,CACJ,CAIA,sBACI,YAAa,CACb,YAAa,CACb,mBACJ,CAEA,uCAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,QAAS,CAET,WAAY,CACZ,gBAAiB,CAFjB,cAAe,CAGf,iBACJ,CAEA,uCACI,sEAAyE,CAEzE,sBAAwB,CADxB,eAEJ,CAEA,gCACI,YACJ,CAEA,uCAKI,mCAAoC,CAFpC,UAAW,CADX,OAAQ,CAER,UAAY,CAHZ,iBAAkB,CAKlB,4BACJ,CAEA,iDACI,UACJ,CAEA,iDAKI,WAAY,CAHZ,OAAQ,CAIR,mBAAiB,CAAjB,gBAAiB,CALjB,iBAAkB,CAGlB,UAAW,CADX,UAIJ,CAEA,yCACI,qBACJ,CAKA,QACI,YAAa,CACb,wBACJ,CAEA,kDACI,aACJ,CAEA,cAKI,kBAAmB,CAInB,0CAA2C,CAN3C,YAAa,CACb,qBAAsB,CAEtB,QAAS,CAJT,eAAgB,CAKhB,2EAAgF,CANhF,iBAAkB,CAOlB,iBAEJ,CAEA,gBACI,eACJ,CAEA,cACI,6CAA8C,CAC9C,eAAgB,CAEhB,sBAAwB,CADxB,eAEJ,CAEA,oBAEI,gBAAiB,CAEjB,sBAAwB,CADxB,eAAgB,CAFhB,eAIJ,CAKA,aACI,gBACJ,CAEA,4BACI,eACJ,CAEA,mDACI,YACJ,CAEA,mBACI,eACJ,CAEA,mBAOI,2CAA4C,CAJ5C,gBAAiB,CACjB,eAAgB,CAChB,qBAAuB,CAJvB,kBAAmB,CACnB,mBAAoB,CAIpB,wBAEJ,CAEA,kBACI,YAAa,CAEb,mBAAoB,CADpB,mCAEJ,CAEA,2BAEI,4BAA6B,CAD7B,0BAEJ,CAEA,gCAGI,sBAAuB,CAFvB,YAAa,CACb,0BAEJ,CAEA,iCAEI,cAAe,CADf,UAEJ,CAEA,yCACI,iCACI,YACJ,CACJ,CAEA,mDACI,iCACI,UACJ,CACJ,CAEA,mCAEI,oCAAqC,CADrC,0BAEJ,CAEA,iCACI,gBAAiB,CACjB,eAAgB,CAChB,sBACJ,CAEA,iDACI,iCACI,gBACJ,CACJ,CAEA,mCACI,YACJ,CAEA,4CACI,cACJ,CAEA,kCAOI,oCAAqC,CAHrC,UAAW,CAEX,WAAY,CAHZ,6BAAgC,CAFhC,iBAAkB,CAClB,KAAM,CAGN,SAGJ,CAEA,0BACI,4CACI,mCACJ,CAEA,kEACI,YACJ,CACJ,CAEA,yBACI,4CACI,YAAa,CACb,qBACJ,CACJ,CAKA,cACI,WAAY,CACZ,eACJ,CAEA,oBAGI,+BAA2B,CAA3B,0BAA2B,CAF3B,YAAa,CACb,oCAEJ,CAEA,wCACI,wBACJ,CAEA,oDACI,YACJ,CAEA,gJAEI,YACJ,CAEA,SACI,YAAa,CAGb,uCAA0C,CAC1C,eAAgB,CAHhB,gBAAmB,CAInB,sBAAwB,CAHxB,eAIJ,CAEA,mCACI,mBACJ,CAEA,iEACI,aACJ,CAEA,WAEI,kBAAmB,CADnB,YAAa,CAEb,OACJ,CAEA,aAGI,WAAY,CAFZ,eAAgB,CAChB,UAEJ,CAEA,6FACI,YACJ,CAIA,mCACI,qBAAwB,CACxB,iBACJ,CAEA,sCACI,iBACJ,CAEA,yCAOI,oCAAqC,CAHrC,UAAW,CAEX,WAAY,CALZ,iBAAkB,CAElB,8BAAiC,CADjC,KAAM,CAGN,SAGJ,CAIA,2EACI,qBACJ,CAEA,4FACI,qBACJ,CAEA,6HACI,gBACJ,CAEA,0BACI,oBACI,aAAc,CACd,eACJ,CAEA,sCACI,YACJ,CACJ,CAKA,oBAQI,2CAA4C,CAJ5C,gBAAiB,CACjB,eAAgB,CAJhB,gBAAmB,CAKnB,qBAAuB,CAJvB,qCAAwC,CACxC,mBAAoB,CAIpB,wBAEJ,CAEA,sDACI,YACJ,CAEA,uBACI,mBACJ,CAEA,wCACI,eACJ,CAIA,+BAII,wBAAyB,CADzB,0BAA2B,CAF3B,YAAa,CACb,qBAGJ,CAEA,oCAEI,kBAAmB,CADnB,kBAAmB,CAEnB,QACJ,CAEA,8CACI,kBACJ,CAEA,qCAGI,qBAAsB,CAFtB,aAAc,CACd,WAEJ,CAEA,qCACI,qCACI,WACJ,CACJ,CAEA,+DACI,eACJ,CAEA,qCACI,aACJ,CAEA,+CACI,YACJ,CAEA,uCACI,cACJ,CAEA,+CAOI,oCAAqC,CAHrC,UAAW,CAEX,UAAW,CAHX,MAAO,CAFP,iBAAkB,CAClB,4BAA+B,CAG/B,UAGJ,CAEA,iEACI,YACJ,CAEA,yBACI,oCAEI,sBAAuB,CADvB,qBAEJ,CAEA,qCACI,UACJ,CACJ,CAIA,+BACI,YAAa,CACb,wDAA2D,CAE3D,eAAgB,CADhB,iCAEJ,CAEA,sCAMI,UAAW,CAHX,8BAAiC,CADjC,4BAMJ,CAEA,2EAHI,oCAAqC,CAFrC,UAAW,CADX,6BAAgC,CAHhC,iBAiBJ,CARA,qCAMI,WAAY,CAJZ,KAAM,CAGN,SAGJ,CAEA,yBACI,+BACI,mCACJ,CACJ,CAEA,yBACI,+BACI,YAAa,CACb,qBACJ,CACJ,CAIA,8CACI,YACJ,CAKA,kBACI,eAAgB,CAChB,2BACJ,CAEA,kBAQI,2CAA4C,CAJ5C,gBAAiB,CACjB,eAAgB,CAJhB,gBAAmB,CAKnB,qBAAuB,CAJvB,qCAAwC,CACxC,mBAAoB,CAIpB,wBAEJ,CAEA,UAII,kBAAmB,CAInB,0CAA2C,CAP3C,YAAa,CACb,qBAAsB,CACtB,sBAAuB,CAGvB,gBAAiB,CADjB,iBAAkB,CAElB,iBAEJ,CAEA,eAII,iBAAkB,CADlB,WAAY,CAFZ,kBAAmB,CACnB,UAGJ,CAEA,gBACI,uCAA0C,CAC1C,eAAgB,CAChB,sBACJ,CAEA,sBAEI,iBAAkB,CAClB,eAAgB,CAFhB,eAGJ,CAEA,qBACI,eACJ,CAEA,oBACI,eACJ,CAEA,qCACI,YAAa,CACb,qBAAsB,CACtB,QAAS,CAGT,oBAAqB,CAFrB,QAAS,CACT,SAEJ,CAEA,gDACI,cACJ,CAEA,sCACI,YAAa,CAEb,YAAa,CADb,+BAEJ,CAEA,4CACI,SACJ,CAEA,4CAGI,iBAAkB,CAFlB,eAAgB,CAChB,UAEJ,CAEA,0CAEI,gBAAiB,CACjB,eAAgB,CAChB,sBAAwB,CAHxB,eAIJ,CAEA,kEACI,UACJ,CAEA,wCAKI,iCAAkC,CAFlC,gBAAiB,CACjB,gBAAiB,CAHjB,OAAQ,CACR,eAAgB,CAIhB,sBACJ,CAEA,gDAQI,oBAAqB,CACrB,2BAA4B,CAF5B,iCAAkC,CANlC,mBAAoB,CACpB,YAAa,CAGb,gBAAiB,CADjB,aAAc,CAEd,eAAgB,CAHhB,iBAOJ,CAEA,2BAEI,kBAAmB,CASnB,4BAA6B,CAC7B,QAAS,CACT,8BAA+B,CAP/B,cAAe,CALf,mBAAoB,CAMpB,gBAAiB,CACjB,eAAgB,CALhB,OAAQ,CAMR,qBAAuB,CALvB,eAAgB,CAChB,SAAU,CAKV,wBAIJ,CAEA,+BACI,eAAgB,CAChB,UACJ,CAEA,iCACI,UACJ,CAOA,YACI,wBAAyB,CAEzB,qBACJ,CAEA,mBACI,+CACJ,CAEA,gBAMI,+BAAgC,CAJhC,gBAAiB,CACjB,eAAgB,CAChB,oBAAsB,CAHtB,kBAAmB,CAInB,wBAEJ,CAEA,kBACI,wEAA2E,CAE3E,sBAAwB,CADxB,eAEJ,CAEA,oBAGI,8CAAkD,CAElD,sBAAwB,CADxB,gBAAiB,CAHjB,2CAA+C,CAC/C,eAIJ,CAEA,iBAEI,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CAER,eAAgB,CADhB,eAEJ,CAEA,yBACI,YAAa,CACb,gBACJ,CAEA,2BAMI,wCAAyC,CAEzC,wCAAyC,CADzC,iBAAkB,CAHlB,WAAY,CAFZ,aAAc,CAGd,eAAgB,CAJhB,iBAAkB,CAElB,UAMJ,CAEA,uCACI,UACJ,CAEA,wCACI,SACJ,CAEA,wCACI,SACJ,CAEA,sCAII,WAAY,CAFZ,OAAQ,CAGR,mBAAiB,CAAjB,gBAAiB,CAJjB,iBAAkB,CAElB,UAGJ,CAEA,yBACI,YAAa,CACb,qBAAsB,CACtB,OACJ,CAEA,wBACI,gBAAiB,CACjB,eAAgB,CAChB,sBACJ,CAEA,yBAKI,iCAAkC,CAJlC,iBAAkB,CAClB,eAAgB,CAEhB,sBAAwB,CADxB,eAGJ,CAEA,kBACI,gBAAiB,CACjB,eACJ,CAEA,sBACI,UACJ,CAIA,kCACI,sBACJ,CAIA,uEACI,eACJ,CAEA,2DACI,iBACJ,CAEA,0BACI,YACJ,CASA,YACI,yCAA2C,CAC3C,qBACJ,CAGA,gBAEI,eAAgB,CADhB,sDAEJ,CAGA,gGACI,YACJ,CAIA,mCACI,sDACJ,CAGA,iEAGI,UAAU,CADV,eAAgB,CAIhB,eAAgB,CAFhB,aAAc,CACd,oBAEJ,CAEA,yIACI,eACJ,CAGA,mBACI,sDACJ,CAGA,sCAEI,gEAAoE,CADpE,iBAEJ,CAEA,wCACI,gEACJ,CAGA,eACI,sCAAyC,CACzC,qBACJ,CAEA,eACI,sCAAyC,CACzC,qBACJ,CAEA,eACI,sCAAyC,CACzC,sBACJ,CAEA,cACI,+BAAgC,CAChC,yBACJ,CAEA,0GAEI,eACJ,CAEA,q2BASI,iCACJ,CAEA,uBACI,iBACJ,CAEA,qCACI,cACJ,CAEA,qBACI,2BACJ,CAEA,wBACI,2BACJ,CAEA,eAGI,oCAAqC,CACrC,QAAS,CAFT,UAAW,CADX,UAIJ,CAEA,sBAEI,gCAAiC,CADjC,iBAEJ,CAEA,4BAOI,wBAAyB,CADzB,gBAAiB,CAJjB,4BAA6B,CAC7B,gBAAiB,CAEjB,kBAAmB,CADnB,kBAAmB,CAHnB,UAOJ,CAEA,+BAGI,2BAA4B,CAF5B,gBAAiB,CACjB,eAAgB,CAIhB,mBAAqB,CAFrB,eAAgB,CAChB,wBAEJ,CAEA,sEAGI,2CAA4C,CAD5C,gBAEJ,CAEA,mDACI,cACJ,CAEA,kDACI,eACJ,CAEA,gBAMI,iBAAkB,CAHlB,gBAAiB,CACjB,iBAAkB,CAHlB,aAAc,CACd,YAMJ,CAEA,2CALI,oCAAqC,CAErC,4BAYJ,CATA,2BAOI,mBAAqB,CAHrB,eAAiB,CADjB,eAAgB,CAEhB,eAAgB,CAHhB,kBAAqB,CADrB,uBAQJ,CAOA,oEACI,sDACJ,CAEA,oEACI,sDACJ,CAMA,wHACI,sDACJ,CAIA,UAEI,gBAAiB,CADjB,iBAEJ,CAIA,eAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,UACJ,CAIA,2CAEI,cACJ,CAEA,iDAEI,mBACJ,CAIA,gDACI,cAAe,CACf,eACJ,CAEA,wHACI,6BACJ,CAEA,uHACI,4BACJ,CAIA,yBACI,mBACJ,CAEA,0BACI,yBACJ,CAIA,wBAEI,+CAAgD,CADhD,iBAEJ,CAEA,6BAGI,iCAAkC,CAFlC,iBAAkB,CAClB,eAEJ,CAIA,gCAII,aAAc,CAHd,YAAa,CAIb,iBAAkB,CAHlB,kBAAmB,CACnB,iBAGJ,CAIA,iIACI,6BACJ,CAEA,gIACI,4BACJ,CAEA,qHACI,6BACJ,CAIA,yCACI,6BACJ,CAEA,+CACI,SACJ,CAEA,0FAEI,4CAA8C,CAC9C,wCACJ,CAEA,uCACI,cACJ,CAEA,qCACI,cACJ,CAEA,2CAGI,cAAgB,CADhB,eAAiB,CADjB,eAGJ,CAEA,wCACI,cACJ,CAEA,6CACI,2BACJ,CAIA,gDACI,kBACJ,CAEA,kDACI,YAAa,CACb,eACJ,CAEA,gIACI,6BACJ,CAEA,+HACI,4BACJ,CAEA,mHACI,iCACJ,CAIA,8CACI,YACJ,CAEA,6CACI,kBACJ,CAEA,6CACI,cACJ,CAEA,oDAEI,eAAgB,CADhB,gBAEJ,CAIA,WAEI,gBAAiB,CADjB,eAAgB,CAEhB,iBACJ,CAEA,kCACI,cACJ,CAEA,aACI,+BAAgC,CAChC,yBACJ,CAKA,aACI,eACJ,CAKA,6BACI,gBACJ,CAEA,mCACI,aACJ,CAEA,8CACI,mCACJ,CAEA,iDACI,aACJ,CAKA,YACI,YAAa,CAEb,mBAAoB,CADpB,oCAAsC,CAEtC,sBACJ,CAEA,sBACI,eACJ,CAEA,kBAMI,2CAA4C,CAL5C,YAAa,CAEb,mBAAoB,CACpB,gBAAmB,CAFnB,6BAA8B,CAG9B,gDAEJ,CAEA,wCACI,kBAAmB,CACnB,gBACJ,CAEA,gEACI,qBACJ,CAEA,8BACI,gBAAiB,CACjB,YACJ,CAEA,kDACI,YACJ,CAEA,mCACI,YAAa,CACb,0BAA2B,CAE3B,QAAS,CADT,wBAEJ,CAEA,mCAII,iBAAkB,CADlB,YAAa,CAFb,YAAa,CAIb,mBAAiB,CAAjB,gBAAiB,CAHjB,WAIJ,CAEA,mCACI,gBACJ,CAEA,gBAMI,iCAAkC,CALlC,YAAa,CAGb,gBAAiB,CACjB,eAAgB,CAHhB,QAAS,CACT,eAIJ,CAEA,kBACI,aACJ,CAEA,kBAGI,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,gBACJ,CAEA,sBAEI,WAAY,CADZ,UAEJ,CAEA,0BACI,YACI,aACJ,CACJ,CAEA,yBACI,kBAGI,sBAAuB,CAFvB,YAGJ,CAEA,qDAJI,6BAMJ,CACJ,CAKA,gFACI,aACJ,CAEA,+EACI,YACJ,CAEA,qHACI,mDACJ,CAEA,sHACI,oDAAsD,CACtD,eACJ,CAEA,qHACI,mDACJ,CAEA,iFACI,gDACJ,CAEA,kFACI,qDACJ,CAEA,uHACI,eAAgB,CAChB,sBACJ,CAEA,sHACI,qBACJ,CAEA,yFACI,eACJ,CAEA,+FAGI,qCAAuC,CADvC,sBAAwB,CADxB,gBAGJ,CAEA,iHACI,uCACJ,CAEA,gHACI,uCAAwC,CACxC,gBACJ,CAEA,yIACI,8CACJ,CAEA,8FACI,uCAAwC,CACxC,eACJ,CAEA,gGACI,sBACJ,CAMA,2MACI,sBACJ,CAEA,yEACI,0BACJ,CAEA,0FACI,iBAAkB,CAElB,sBAAwB,CADxB,eAEJ,CAEA,kRAEI,gBAAiB,CACjB,sBACJ,CAEA,uRAGI,+CAA+C,CAD/C,eAEJ,CAEA,+FACI,gBACJ,CAMA,gMACI,gBACJ,CAEA,2cAEI,eACJ,CAEA,8FACI,gBACJ,CAKA,WAGI,8BAA+B,CAD/B,gBAAiB,CADjB,eAGJ,CAEA,6BACI,aACJ,CAIA,4BACI,0CACJ,CAEA,2CACI,YACJ,CAEA,uCACI,qBAAsB,CACtB,aACJ,CAIA,eAGI,kBAAmB,CAKnB,0CAA2C,CAJ3C,oBAAgB,CAAhB,eAAgB,CAHhB,YAAa,CAMb,eAAgB,CALhB,kCAAmC,CAGnC,mBAAoB,CACpB,kBAGJ,CAEA,gBAEI,mDAAqD,CACrD,uCAA0C,CAC1C,eAAgB,CAChB,sBAAwB,CAJxB,iBAAkB,CAKlB,kBACJ,CAEA,oBACI,eACJ,CAEA,qBACI,YAAa,CAGb,cAAe,CADf,YAAa,CADb,sBAAuB,CAKvB,oBAAqB,CAFrB,QAAS,CACT,SAEJ,CAEA,qBACI,gBAAiB,CACjB,kBACJ,CAEA,uBACI,yBACJ,CAIA,kBAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,oBAAqB,CACrB,iBACJ,CAEA,yBACI,oEAAuE,CACvE,eAAgB,CAChB,qBACJ,CAEA,0BAGI,gBAAiB,CACjB,eAAgB,CAEhB,sBAAwB,CADxB,eAAgB,CAJhB,eAAgB,CAChB,eAAgB,CAKhB,WACJ,CAEA,2BACI,eACJ,CAEA,yBACI,eACI,YAAa,CACb,qBAAsB,CACtB,QACJ,CACJ,CAKA,MAaI,6BAA8B,CAD9B,0BAA2B,CAP3B,YAAa,CAEb,WAAY,CAJZ,MAAO,CAQP,YAAa,CAHb,eAAgB,CAPhB,iBAAkB,CAClB,KAAM,CAQN,iBAAkB,CAJlB,UAAW,CAFX,eAUJ,CAEA,UACI,cACJ,CAEA,uBACI,YAAc,CACd,iDAAuD,CACvD,mBACJ,CAEA,YACI,aACJ,CAEA,+BACI,cACJ,CAEA,4BACI,WACJ,CAEA,2BACI,eACJ,CAEA,UAUI,0BAA2B,CAJ3B,gCAAqC,CACrC,SAAU,CAEV,uBAAwB,CADxB,iDAAuD,CAGvD,mBACJ,CAEA,6BATI,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAiBJ,CAPA,mBAMI,eACJ,CAEA,kCAQI,0BAA2B,CAH3B,QAAS,CACT,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAIN,iBAEJ,CAEA,4BAGI,uCAAwC,CACxC,0BAA2B,CAF3B,wBAAiB,CAAjB,qBAAiB,CAAjB,gBAGJ,CAEA,iBACI,iBAAkB,CAGlB,yBAA0B,CAD1B,mDAAyD,CADzD,UAGJ,CAEA,iEAEI,eACJ,CAEA,YAII,QAAS,CAET,eAAgB,CAHhB,OAIJ,CAEA,uBAJI,MAAO,CAJP,iBAAkB,CAClB,KAaJ,CANA,WAKI,WAAY,CADZ,UAEJ,CAEA,wBACI,0BACJ,CAEA,+BACI,6BACJ,CAEA,qBAII,qBAAuB,CAFvB,MAAO,CADP,KAAM,CAEN,oBAEJ,CAEA,iBAQI,iCAAkC,CAFlC,cAAe,CAHf,MAAO,CAIP,gBAAiB,CAFjB,eAAgB,CAJhB,iBAAkB,CAQlB,iBAAkB,CAPlB,OAAQ,CAER,UAMJ,CAEA,mBACI,iCAAkC,CAClC,yBACJ,CAEA,cASI,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAEhB,eAAgB,CAChB,QAAS,CACT,eAAgB,CAHhB,cAAe,CARf,aAAc,CACd,WAAY,CAEZ,WAAY,CAEZ,QAAS,CACT,gBAAiB,CAFjB,SAAU,CALV,iBAAkB,CAalB,sBAAwB,CAVxB,UAWJ,CAEA,wCAEI,SACJ,CAEA,qBAEI,UAAY,CADZ,YAEJ,CAEA,gCAEI,QAAS,CADT,SAEJ,CAEA,2CACI,SACJ,CAEA,mFAKI,wDAA2D,CAC3D,0BAA2B,CAF3B,WAAY,CADZ,UAIJ,CAEA,sIACI,oHAGI,gDACJ,CAEA,6EAEI,eACJ,CACJ,CAEA,qBACI,2BACJ,CAEA,qBACI,+BACJ,CAEA,kBACI,YACJ,CAEA,qCACI,aACJ,CAEA,4BACI,2BACJ,CAEA,oBAEI,2BAA4B,CAD5B,YAEJ,CAEA,wCACI,aACJ,CAEA,qCACI,4BACJ,CAEA,iFAEI,iBACJ,CAEA,uDAOI,eAAgB,CAFhB,YAAa,CACb,gBAAiB,CAJjB,iBAAkB,CAClB,OAAQ,CACR,UAIJ,CAEA,2BACI,MACJ,CAEA,4BACI,OACJ,CAEA,qEAMI,UAAW,CADX,WAAY,CAHZ,iBAAkB,CAClB,QAAS,CACT,UAGJ,CAEA,kCAEI,gCAAiC,CADjC,QAEJ,CAEA,mCAEI,+BAAgC,CADhC,SAEJ,CAEA,eASI,wBAAyB,CAHzB,cAAe,CACf,eAAgB,CAHhB,WAAY,CADZ,MAAO,CAKP,gBAAiB,CAHjB,cAAe,CAJf,iBAAkB,CAClB,KAAM,CAQN,wBAAiB,CAAjB,qBAAiB,CAAjB,gBACJ,CAEA,eAEI,QAAS,CACT,MAAO,CAEP,eAAgB,CAJhB,iBAAkB,CAGlB,UAEJ,CAEA,uBAMI,wBAAyB,CAFzB,cAAe,CACf,eAAgB,CAFhB,aAAc,CAFd,eAAgB,CAChB,sBAAuB,CAKvB,iBACJ,CAEA,2CAEI,cAAe,CACf,eAAgB,CAFhB,iBAAkB,CAGlB,wBACJ,CAEA,8EAEI,gBAAiB,CADjB,aAEJ,CAEA,sBACI,YACJ,CAEA,qBACI,iBACJ,CAEA,iBASI,aAAc,CAJd,WAAY,CAFZ,QAAS,CAGT,iBAAkB,CAClB,SAAU,CANV,iBAAkB,CAClB,KAAM,CAMN,gCAAkC,CAJlC,UAAW,CAMX,mBACJ,CAEA,sBAEI,WAAY,CACZ,WAAY,CAFZ,UAGJ,CAEA,yBACI,SACJ,CAEA,+CACI,qDACJ,CAEA,8CACI,SACJ,CAEA,oEACI,uCACJ,CAEA,sEACI,2DACJ,CAEA,2CAOI,eAAgB,CAFhB,WAAY,CAFZ,SAAU,CAGV,QAAS,CAET,WAAa,CAPb,iBAAkB,CAClB,QAAS,CAET,UAKJ,CAEA,2CAGI,WAAY,CACZ,eAAgB,CAHhB,iBAAkB,CAClB,SAGJ,CAEA,6CAQI,eAAgB,CAEhB,mCAAgC,CAChC,iCAA8B,CAC9B,iBAAkB,CAHlB,yCAAoC,CAApC,uCAAoC,CALpC,qBAAsB,CAEtB,WAAY,CAHZ,MAAO,CAIP,QAAS,CANT,iBAAkB,CAClB,KAAM,CAGN,UAQJ,CAEA,qCACI,iBAII,WAAY,CADZ,SAAU,CAEV,QAAS,CAJT,iBAAkB,CAClB,QAIJ,CACJ,CAEA,qBACI,GACI,sBACJ,CAEA,GACI,uBACJ,CACJ,CAEA,wBACI,GACI,mBACJ,CAEA,IACI,yBACJ,CAEA,GACI,mBACJ,CACJ,CAEA,UAII,2BAA4B,CAD5B,SAAU,CADV,kBAAmB,CADnB,YAIJ,CAEA,eAKI,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAEJ,CAEA,uHAII,0BAA2B,CAC3B,iDAAuD,CACvD,mBACJ,CAEA,yFAEI,kBACJ,CAMA,sHAEI,SACJ,CAEA,6JAII,YACJ,CAEA,qIAGI,YACJ,CAEA,yBACI,sBACJ,CAEA,oCACI,eACJ", "file": "screen.css", "sourcesContent": ["/* Table of contents\n/* ------------------------------------------------------------\n\nThis is a development CSS file which is built to a minified\nproduction stylesheet in assets/built/screen.css\n\n1. Variables\n2. Fonts\n3. Resets\n4. Globals\n5. Layout\n6. Navigation\n  6.1. Navigation styles\n  6.2. Navigation layouts\n  6.3. Dropdown menu\n  6.4. Mobile menu\n7. Card\n8. Header\n  8.1. Magazine layout\n  8.2. Highlight layout\n  8.3. Classic layout\n9. CTA\n10. Featured posts\n11. Container\n  11.1. With sidebar\n  11.2. Without sidebar\n12. Post list\n  12.1. List style\n  12.2. Grid style\n  12.3. No image list\n13. Sidebar\n14. Post/page\n  14.1. Article\n  14.2. Page template\n  14.3. Page without header\n15. Content\n16. Cards\n17. Comments\n18. Recent posts\n19. Archive\n20. Design settings\n21. Footer\n  21.1. Footer styles\n  21.2. Footer bar\n  21.3. Footer signup\n22. Lightbox\n\n*/\n\n/* 1. Variables\n/* ---------------------------------------------------------- */\n\n:root {\n    --color-white: #fff;\n    --color-lighter-gray: rgb(0 0 0 / 0.05);\n    --color-light-gray: #e6e6e6;\n    --color-mid-gray: #ccc;\n    --color-dark-gray: #444;\n    --color-darker-gray: #15171a;\n    --color-black: #000;\n    --color-primary-text: var(--color-darker-gray);\n    --color-secondary-text: rgb(0 0 0 / 0.55);\n    --color-border: rgb(0 0 0 / 0.08);\n    --color-dark-border: rgb(0 0 0 / 0.55);\n    --font-sans: Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif;\n    --font-serif: \"EB Garamond\", Georgia, Times, serif;\n    --font-serif-alt: Georgia, Times, serif;\n    --font-mono: \"JetBrains Mono\", Menlo, Consolas, Monaco, \"Liberation Mono\", \"Lucida Console\", monospace;\n    --container-width: 1320px;\n    --container-gap: clamp(24px, 1.7032rem + 1.9355vw, 48px);\n    --grid-gap: 42px;\n}\n\n:root.has-light-text,\n:is(.gh-navigation, .gh-footer).has-accent-color {\n    --color-lighter-gray: rgb(255 255 255 / 0.1);\n    --color-darker-gray: #fff;\n    --color-secondary-text: rgb(255 255 255 / 0.64);\n    --color-border: rgb(255 255 255 / 0.15);\n    --color-dark-border: rgb(255 255 255 / 0.5);\n}\n\n/* 2. Fonts\n/* ---------------------------------------------------------- */\n\n/* Fonts are preloaded and defined in default.hbs to avoid layout shift */\n\n/* 3. Resets\n/* ---------------------------------------------------------- */\n\n*, *::before, *::after {\n    box-sizing: border-box;\n}\n\n* {\n    margin: 0;\n}\n\nhtml {\n    font-size: 62.5%;\n}\n\nbody {\n    line-height: 1.6;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n\nimg, picture, video, canvas, svg {\n    display: block;\n    height: auto;\n    max-width: 100%;\n}\n\niframe {\n    display: block;\n}\n\ninput, button, textarea, select {\n    font: inherit;\n}\n\np, h1, h2, h3, h4, h5, h6 {\n    overflow-wrap: break-word;\n}\n\nh1, h2, h3, h4, h5, h6 {\n    line-height: 1.2;\n}\n\n/* 4. Globals\n/* ---------------------------------------------------------- */\n\nbody {\n    font-family: var(--gh-font-body, var(--font-sans));\n    font-size: 1.6rem;\n    background-color: var(--background-color);\n    color: var(--color-primary-text);\n}\n\nh1, h2, h3, h4, h5, h6 {\n    font-family: var(--gh-font-heading, var(--font-sans));\n}\n\na {\n    color: var(--color-darker-gray);\n    text-decoration: none;\n}\n\na:hover {\n    opacity: 0.8;\n}\n\n.gh-button {\n    display: inline-flex;\n    gap: 0.4em;\n    align-items: center;\n    justify-content: center;\n    padding: 0.8em 1.4em;\n    font-size: 1.5rem;\n    font-weight: 600;\n    letter-spacing: -0.004em;\n    line-height: 1;\n    color: var(--color-white);\n    cursor: pointer;\n    background-color: var(--ghost-accent-color);\n    border: 0;\n    border-radius: 100px;\n}\n\n.gh-button:hover {\n    opacity: 0.95;\n}\n\n.gh-icon-button {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    width: 32px;\n    height: 32px;\n    padding: 0;\n    color: var(--color-darker-gray);\n    cursor: pointer;\n    background-color: transparent;\n    border: 0;\n    outline: none;\n}\n\n.gh-icon-button:hover :is(svg, span) {\n    opacity: 0.8;\n}\n\n.gh-icon-button svg {\n    width: 20px;\n    height: 20px;\n}\n\n.gh-form {\n    display: flex;\n    align-items: center;\n    position: relative;\n    max-width: 560px;\n    width: 100%;\n    height: 56px;\n    font-size: 1.7rem;\n    font-weight: 450;\n    letter-spacing: -0.008em;\n    border-radius: 40px;\n    background-color: var(--color-lighter-gray);\n    transition: background-color 0.2s ease;\n}\n\n.gh-form.success {\n    pointer-events: none;\n}\n\n.gh-form.error {\n    box-shadow: 0 0 0 1px red;\n}\n\n.gh-form:hover {\n    background-color: rgb(0 0 0 / 0.065);\n}\n\n.has-light-text .gh-form:hover,\n.gh-footer.has-accent-color .gh-form:hover {\n    background-color: rgb(255 255 255 / 0.15);\n}\n\n.gh-form-input {\n    position: absolute;\n    inset: 0;\n    padding-inline: 26px;\n    width: 100%;\n    height: 100%;\n    font-size: inherit;\n    letter-spacing: inherit;\n    line-height: 1.1;\n    border: 0;\n    border-radius: 40px;\n    background-color: transparent;\n    outline: none;\n    transition: 0.3s ease-in-out;\n}\n\n.gh-form-input::placeholder,\nbutton.gh-form-input {\n    color: rgb(0 0 0 / 0.3);\n}\n\n:is(.has-serif-title, .has-mono-title):not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-form-input,\nbody[class*=\" gh-font-heading\"]:not(.gh-font-heading-fira-sans):not(.gh-font-heading-inter):not(.gh-font-heading-manrope):not(.gh-font-heading-noto-sans):not(.gh-font-heading-nunito):not(.gh-font-heading-poppins):not(.gh-font-heading-roboto):not(.gh-font-heading-space-grotesk) .gh-form-input {\n    padding-inline: 20px;\n}\n\n.gh-form.gh-form.success .gh-form-input {\n    opacity: 0.5;\n}\n\n.has-light-text .gh-form-input,\n.gh-footer.has-accent-color .gh-form-input {\n    color: #fff;\n}\n\n.has-light-text .gh-form-input::placeholder,\n.has-light-text button.gh-form-input,\n.gh-footer.has-accent-color .gh-form-input::placeholder {\n    color: rgb(255 255 255 / 0.55);\n}\n\n.gh-header.is-classic.has-image .gh-form-input {\n    color: #15171a;\n}\n\n.gh-header.is-classic.has-image .gh-form-input::placeholder,\n.gh-header.is-classic.has-image button.gh-form-input,\n.gh-header.is-classic.has-image .gh-form > svg {\n    color: rgb(0 0 0 / 0.5);\n}\n\nbutton.gh-form-input {\n    padding-inline-start: 56px;\n    text-align: left;\n    color: var(--color-secondary-text);\n    cursor: pointer;\n}\n\n:is(.has-serif-title,.has-mono-title):not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) button.gh-form-input,\nbody[class*=\" gh-font-heading\"]:not(.gh-font-heading-fira-sans):not(.gh-font-heading-inter):not(.gh-font-heading-manrope):not(.gh-font-heading-noto-sans):not(.gh-font-heading-nunito):not(.gh-font-heading-poppins):not(.gh-font-heading-roboto):not(.gh-font-heading-space-grotesk) button.gh-form-input {\n    padding-inline-start: 50px;\n}\n\n.gh-form .gh-button {\n    position: absolute;\n    right: 6px;\n    padding-inline: 32px;\n    height: 44px;\n    font-size: inherit;\n}\n\n.gh-form > svg {\n    position: relative;\n    left: 22px;\n    width: 20px;\n    height: 20px;\n    color: var(--color-secondary-text);\n}\n\n:is(.has-serif-title,.has-mono-title):not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-form > svg {\n    left: 16px;\n}\n\n.gh-form .gh-button svg {\n    display: none;\n    position: absolute;\n    margin-top: 1px;\n}\n\n.gh-form:is(.loading, .success) .gh-button span {\n    visibility: hidden;\n}\n\n.gh-form.loading .gh-button svg:first-of-type {\n    display: block;\n}\n\n.gh-form.success .gh-button svg:last-of-type {\n    display: block;\n}\n\n.gh-form [data-members-error] {\n    position: absolute;\n    top: 100%;\n    margin-top: 10px;\n    width: 100%;\n    font-size: 1.4rem;\n    line-height: 1.4;\n}\n\n@media (max-width: 576px) {\n    .gh-form {\n        font-size: 1.6rem;\n    }\n\n    .gh-form .gh-button {\n        padding-inline: 12px;\n    }\n\n    .gh-form .gh-button span span {\n        display: none;\n    }\n\n    .gh-form .gh-button span svg {\n        display: inline;\n        position: static;\n        margin-top: 2px;\n        width: 20px;\n        height: 20px;\n    }\n}\n\n/* 5. Layout\n/* ---------------------------------------------------------- */\n\n.gh-viewport {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    min-height: 100vh;\n}\n\n.gh-outer {\n    padding: 0 max(4vmin, 20px);\n}\n\n.gh-outer .gh-outer {\n    padding: 0;\n}\n\n.gh-inner {\n    margin: 0 auto;\n    max-width: var(--container-width);\n    width: 100%;\n}\n\n.gh-main {\n    flex-grow: 1;\n}\n\n.gh-canvas,\n.kg-width-full.kg-content-wide {\n    --main: min(var(--content-width, 720px), 100% - var(--container-gap) * 2);\n    --wide: minmax(0, calc((var(--container-width, 1200px) - var(--content-width, 720px)) / 2));\n    --full: minmax(var(--container-gap), 1fr);\n\n    display: grid;\n    grid-template-columns:\n        [full-start] var(--full)\n        [wide-start] var(--wide)\n        [main-start] var(--main) [main-end]\n        var(--wide) [wide-end]\n        var(--full) [full-end];\n}\n\n.gh-canvas > * {\n    grid-column: main;\n}\n\n.kg-width-wide,\n.kg-content-wide > div {\n    grid-column: wide;\n}\n\n.kg-width-full {\n    grid-column: full;\n}\n\n/* 6. Navigation\n/* ---------------------------------------------------------- */\n\n.gh-navigation {\n    height: 100px;\n    font-size: 1.5rem;\n    font-weight: 550;\n    background-color: var(--background-color);\n    color: var(--color-darker-gray);\n}\n\n.gh-navigation :is(.gh-navigation-logo, a:not(.gh-button), .gh-icon-button) {\n    color: inherit;\n}\n\n.gh-navigation-inner {\n    display: grid;\n    grid-auto-flow: row dense;\n    column-gap: 24px;\n    align-items: center;\n    height: 100%;\n}\n\n.gh-navigation-brand {\n    line-height: 1;\n}\n\n.gh-navigation-logo {\n    position: relative;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-size: calc(2.4rem * var(--factor, 1));\n    font-weight: 725;\n    letter-spacing: -0.015em;\n    white-space: nowrap;\n}\n\n.gh-navigation-logo img {\n    max-height: 40px;\n}\n\n.gh-navigation-menu {\n    display: flex;\n    gap: 24px;\n    align-items: center;\n}\n\n.gh-navigation .nav {\n    display: inline-flex;\n    gap: 28px;\n    align-items: center;\n    padding: 0;\n    margin: 0;\n    white-space: nowrap;\n    list-style: none;\n}\n\n.gh-navigation .gh-more-toggle {\n    position: relative;\n    margin: 0 -6px;\n    font-size: inherit;\n    text-transform: inherit;\n}\n\n.gh-navigation .gh-more-toggle svg {\n    width: 24px;\n    height: 24px;\n}\n\n.gh-navigation-actions {\n    display: flex;\n    gap: 24px;\n    align-items: center;\n    justify-content: flex-end;\n    background-color: var(--background-color);\n}\n\n.gh-navigation.has-accent-color .gh-navigation-actions {\n    background-color: var(--ghost-accent-color);\n}\n\n.gh-navigation-members {\n    display: flex;\n    gap: 20px;\n    align-items: center;\n    white-space: nowrap;\n}\n\n.gh-navigation-members .gh-button {\n    font-size: inherit;\n    font-weight: 600;\n}\n\n.gh-search {\n    margin-right: -2px;\n    margin-left: -2px;\n}\n\n@media (max-width: 767px) {\n    .gh-navigation-logo {\n        white-space: normal;\n    }\n\n    .gh-navigation-members {\n        flex-direction: column-reverse;\n        gap: 16px;\n        width: 100%;\n    }\n\n    .gh-navigation-actions .gh-search {\n        display: none;\n    }\n}\n\n@media (min-width: 768px) {\n    .gh-navigation-brand .gh-search {\n        display: none;\n    }\n\n    .gh-navigation:not(.is-dropdown-loaded) .gh-navigation-menu .nav > li {\n        opacity: 0;\n    }\n}\n\n/* 6.1. Navigation styles */\n\n.gh-navigation.has-accent-color {\n    background-color: var(--ghost-accent-color);\n}\n\n.gh-navigation.has-accent-color .gh-button {\n    background-color: #fff;\n    color: #15171a;\n}\n\n/* 6.2. Navigation layouts */\n\n/*\n======================================================================\nLOGO   Home About Collection Author Portal             Login Subscribe\n======================================================================\n*/\n\n.gh-navigation.is-left-logo .gh-navigation-inner {\n    grid-template-columns: auto 1fr auto;\n}\n\n@media (min-width: 768px) {\n    .gh-navigation.is-left-logo .gh-navigation-logo:not(:has(img)) {\n        top: -2px;\n    }\n}\n\n@media (min-width: 992px) {\n    .gh-navigation.is-left-logo .gh-navigation-menu {\n        margin-right: 100px;\n        margin-left: 16px;\n    }\n}\n\n/*\n======================================================================\nHome About Collection            LOGO                  Login Subscribe\n======================================================================\n*/\n\n.gh-navigation.is-middle-logo .gh-navigation-inner {\n    grid-template-columns: 1fr auto 1fr;\n}\n\n.gh-navigation.is-middle-logo .gh-navigation-brand {\n    grid-column-start: 2;\n}\n\n.gh-navigation.is-middle-logo .gh-navigation-actions {\n    gap: 28px;\n}\n\n@media (min-width: 992px) {\n    .gh-navigation.is-middle-logo .gh-navigation-menu {\n        margin-right: 64px;\n    }\n}\n\n/*\n======================================================================\nSearch                         LOGO                    Login Subscribe\n                 Home About Collection Author Portal\n======================================================================\n*/\n\n.gh-navigation.is-stacked {\n    position: relative;\n    height: auto;\n}\n\n.gh-navigation.is-stacked .gh-navigation-inner {\n    grid-template-columns: 1fr auto 1fr;\n}\n\n.gh-navigation.is-stacked .gh-navigation-brand {\n    display: flex;\n    align-items: center;\n    grid-row-start: 1;\n    grid-column-start: 2;\n    min-height: 80px;\n}\n\n@media (max-width: 767px) {\n    .gh-navigation.is-stacked .gh-navigation-brand {\n        min-height: unset;\n    }\n}\n\n@media (min-width: 992px) {\n    .gh-navigation.is-stacked .gh-navigation-inner {\n        padding: 0;\n    }\n\n    .gh-navigation.is-stacked .gh-navigation-brand {\n        display: flex;\n        align-items: center;\n        height: 80px;\n    }\n\n    .gh-navigation.is-stacked .gh-navigation-menu {\n        grid-row-start: 2;\n        grid-column: 1 / 4;\n        justify-content: center;\n        height: 60px;\n        margin: 0 48px;\n    }\n\n    .gh-navigation.is-stacked .gh-navigation-menu::before,\n    .gh-navigation.is-stacked .gh-navigation-menu::after {\n        position: absolute;\n        top: 80px;\n        left: 0;\n        width: 100%;\n        height: 1px;\n        content: \"\";\n        background-color: var(--color-border);\n    }\n\n    .gh-navigation.is-stacked .gh-navigation-menu::after {\n        top: 140px;\n    }\n\n    .gh-navigation.is-stacked .gh-navigation-actions {\n        grid-row-start: 1;\n        grid-column: 1 / 4;\n        justify-content: space-between;\n    }\n\n    .gh-navigation.is-stacked .gh-navigation-actions .gh-search {\n        display: flex;\n        gap: 10px;\n        width: auto;\n    }\n}\n\n/* 6.3. Dropdown menu */\n\n.gh-dropdown {\n    position: absolute;\n    top: 100%;\n    right: -16px;\n    z-index: 90;\n    width: 200px;\n    padding: 12px 0;\n    margin-top: 24px;\n    text-align: left;\n    visibility: hidden;\n    background-color: #fff;\n    border-radius: 5px;\n    box-shadow: 0 0 0 1px rgb(0 0 0 / 0.04), 0 7px 20px -5px rgb(0 0 0 / 0.15);\n    opacity: 0;\n    transition: opacity 0.3s, transform 0.2s;\n    transform: translate3d(0, 6px, 0);\n}\n\n.gh-dropdown.is-left {\n    right: auto;\n    left: -16px;\n}\n\n.is-dropdown-mega .gh-dropdown {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    grid-auto-flow: column;\n    column-gap: 40px;\n    width: auto;\n    padding: 20px 32px;\n}\n\n.is-dropdown-open .gh-dropdown {\n    visibility: visible;\n    opacity: 1;\n    transform: translateY(0);\n}\n\n.gh-dropdown li a {\n    display: block;\n    padding: 7px 20px;\n    line-height: 1.5;\n    white-space: normal;\n    color: #15171a !important;\n}\n\n.is-dropdown-mega .gh-dropdown li a {\n    padding: 8px 0;\n}\n\n/* 6.4. Mobile menu */\n\n.gh-burger {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    position: relative;\n    display: none;\n    margin-right: -7px;\n    margin-left: 4px;\n}\n\n.gh-burger svg {\n    width: 24px;\n    height: 24px;\n}\n\n.gh-burger svg:last-child {\n    display: none;\n}\n\n.is-open .gh-burger svg:first-child {\n    display: none;\n}\n\n.is-open .gh-burger svg:last-child {\n    display: block;\n}\n\n@media (max-width: 767px) {\n    .gh-burger {\n        display: flex;\n    }\n\n    #gh-navigation {\n        height: 64px;\n    }\n\n    #gh-navigation .gh-navigation-inner {\n        grid-template-rows: auto 1fr auto;\n        grid-template-columns: 1fr;\n        gap: 48px;\n    }\n\n    #gh-navigation .gh-navigation-brand {\n        display: grid;\n        grid-template-columns: 1fr auto auto;\n        grid-column-start: 1;\n        align-items: center;\n        height: 64px;\n    }\n\n    #gh-navigation .gh-navigation-logo {\n        font-size: 2.2rem;\n    }\n\n    #gh-navigation .gh-navigation-menu,\n    #gh-navigation .gh-navigation-actions {\n        position: fixed;\n        justify-content: center;\n        visibility: hidden;\n        opacity: 0;\n    }\n\n    #gh-navigation .gh-navigation-menu {\n        transition: none;\n        transform: translateY(0);\n    }\n\n    #gh-navigation .nav {\n        gap: 20px;\n        align-items: center;\n        line-height: 1.4;\n    }\n\n    #gh-navigation .nav a {\n        font-size: 1.75rem;\n        font-weight: 600;\n        text-transform: none;\n    }\n\n    #gh-navigation .nav li {\n        opacity: 0;\n        transform: translateY(-4px);\n    }\n\n    #gh-navigation .gh-navigation-actions {\n        text-align: center;\n    }\n\n    #gh-navigation :is(.gh-button, a[data-portal=\"signin\"]) {\n        opacity: 0;\n        transform: translateY(8px);\n    }\n\n    #gh-navigation .gh-button {\n        width: 100%;\n        font-size: 1.75rem;\n        text-transform: none;\n    }\n\n    #gh-navigation a[data-portal=\"signin\"] {\n        font-size: 1.75rem;\n    }\n\n    #gh-main {\n        transition: opacity 0.4s;\n    }\n\n    .is-open#gh-navigation {\n        position: fixed;\n        inset: 0;\n        z-index: 3999999;\n        height: 100%;\n        overflow-y: scroll;\n        -webkit-overflow-scrolling: touch;\n    }\n\n    .is-open#gh-navigation .gh-navigation-menu,\n    .is-open#gh-navigation .gh-navigation-actions {\n        position: static;\n        visibility: visible;\n        opacity: 1;\n    }\n\n    .is-open#gh-navigation .nav {\n        display: flex;\n        flex-direction: column;\n    }\n\n    .is-open#gh-navigation .nav li {\n        opacity: 1;\n        transition: transform 0.2s, opacity 0.2s;\n        transform: translateY(0);\n    }\n\n    .is-open#gh-navigation .gh-navigation-actions {\n        position: sticky;\n        right: 0;\n        bottom: 0;\n        left: 0;\n        display: inline-flex;\n        flex-direction: column;\n        gap: 12px;\n        align-items: center;\n        padding: var(--container-gap) 0;\n    }\n\n    .is-open#gh-navigation :is(.gh-button, a[data-portal=\"signin\"]) {\n        opacity: 1;\n        transition: transform 0.4s, opacity 0.4s;\n        transition-delay: 0.2s;\n        transform: translateY(0);\n    }\n\n    .is-open#gh-navigation a[data-portal=\"signin\"] {\n        transition-delay: 0.4s;\n    }\n\n    .is-open#gh-main {\n        opacity: 0;\n    }\n}\n\n/* 7. Card\n/* ---------------------------------------------------------- */\n\n.gh-card {\n    position: relative;\n}\n\n.gh-card-link {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n}\n\n.gh-card-link:hover {\n    opacity: 1;\n}\n\n.gh-card-image {\n    position: relative;\n    flex-shrink: 0;\n    aspect-ratio: 16 / 9;\n}\n\n.gh-card-image img {\n    position: absolute;\n    inset: 0;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n.gh-card-wrapper {\n    flex-grow: 1;\n}\n\n.gh-card-tag {\n    display: none;\n    margin-bottom: 4px;\n    font-size: 1.2rem;\n    font-weight: 500;\n    letter-spacing: 0.01em;\n    text-transform: uppercase;\n}\n\n.gh-card-title {\n    font-size: calc(1.9rem * var(--factor, 1));\n    font-weight: 725;\n    letter-spacing: -0.014em;\n    line-height: 1.3;\n}\n\n.gh-card-link:hover .gh-card-title {\n    opacity: 0.8;\n}\n\n.gh-card-excerpt {\n    display: -webkit-box;\n    overflow-y: hidden;\n    margin-top: 8px;\n    font-size: 1.45rem;\n    line-height: 1.4;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n}\n\n.gh-card-meta {\n    align-items: center;\n    padding-bottom: 1px;\n    font-size: 1.25rem;\n    font-weight: 500;\n    line-height: 1.4;\n    letter-spacing: -0.004em;\n    color: var(--color-secondary-text);\n}\n\n.gh-card-meta svg {\n    position: relative;\n    top: 1px;\n    display: inline-block;\n    width: 12px;\n    height: 12px;\n    margin-right: 2px;\n}\n\n.gh-card-meta:not(:empty) {\n    margin-top: 8px;\n}\n\n.gh-card-author + .gh-card-date::before {\n    content: \"—\";\n    margin-right: 4px;\n}\n\n/* 8. Header\n/* ---------------------------------------------------------- */\n\n.gh-header {\n    position: relative;\n    margin-top: 40px;\n}\n\n.gh-header-inner {\n    position: relative;\n    overflow: hidden;\n}\n\n/* 8.1. Magazine layout */\n\n.gh-header.is-magazine .gh-header-inner {\n    display: grid;\n    grid-template-columns: repeat(16, 1fr);\n    gap: var(--grid-gap);\n}\n\n.gh-header.is-magazine .gh-header-inner > div {\n    display: flex;\n    flex-direction: column;\n    gap: var(--grid-gap);\n    grid-row: 1;\n}\n\n.gh-header.is-magazine .gh-header-left {\n    grid-column: 1 / span 4;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card {\n    position: relative;\n    grid-column: 5 / span 8;\n}\n\n.gh-header.is-magazine .gh-header-right {\n    grid-column: 13 / -1;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-link {\n    gap: 28px;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-image {\n    aspect-ratio: 1.618033;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-tag {\n    display: block;\n    margin-bottom: 12px;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-title {\n    font-size: clamp(2.8rem, 1.36vw + 2.25rem, 4rem);\n    font-weight: 700;\n    line-height: 1.1;\n    letter-spacing: -0.022em;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-excerpt {\n    margin-top: clamp(12px, 0.45vw + 10.18px, 16px);\n    font-size: 1.8rem;\n    letter-spacing: -0.02em;\n    max-width: 90%;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-meta:not(:empty) {\n    margin-top: 14px;\n}\n\n.gh-header.is-magazine :is(.gh-header-left, .gh-header-right) .gh-card:last-child .gh-card-image {\n    display: none;\n}\n\n.gh-header.is-magazine .gh-header-inner > div .gh-card-excerpt {\n    display: none;\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card::before,\n.gh-header.is-magazine .gh-header-inner > .gh-card::after {\n    position: absolute;\n    top: 0;\n    left: calc(var(--grid-gap) / -2);\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    background-color: var(--color-border);\n}\n\n.gh-header.is-magazine .gh-header-inner > .gh-card::after {\n    right: calc(var(--grid-gap) / -2);\n    left: auto;\n}\n\n.gh-header.is-magazine .gh-header-inner > div .gh-card + .gh-card::before {\n    position: absolute;\n    top: calc(var(--grid-gap) / -2);\n    left: 0;\n    content: \"\";\n    width: 100%;\n    height: 1px;\n    background-color: var(--color-border);\n}\n\n@media (max-width: 1199px) {\n    .gh-header.is-magazine .gh-header-inner {\n        grid-template-columns: repeat(12, 1fr);\n    }\n\n    .gh-header.is-magazine .gh-header-inner > .gh-card {\n        grid-column: 1 / span 8;\n    }\n\n    .gh-header.is-magazine .gh-header-left {\n        grid-column: 9 / -1;\n    }\n\n    .gh-header.is-magazine .gh-header-inner > div.gh-header-right {\n        grid-column: 1 / -1;\n        grid-row: 2;\n        flex-direction: row;\n    }\n\n    .gh-header.is-magazine .gh-header-right .gh-card {\n        flex: 1;\n    }\n\n    .gh-header.is-magazine .gh-header-right .gh-card:last-child .gh-card-image {\n        display: block;\n    }\n\n    .gh-header.is-magazine .gh-header-right {\n        position: relative;\n    }\n\n    .gh-header.is-magazine .gh-header-right::before {\n        position: absolute;\n        top: calc(var(--grid-gap) / -2);\n        left: 0;\n        content: \"\";\n        width: 100%;\n        height: 1px;\n        background-color: var(--color-border);\n    }\n\n    .gh-header.is-magazine .gh-header-right .gh-card::before {\n        display: none;\n    }\n\n    .gh-header.is-magazine .gh-header-right .gh-card::after {\n        position: absolute;\n        top: 0;\n        right: calc(var(--grid-gap) / -2);\n        content: \"\";\n        width: 1px;\n        height: 100%;\n        background-color: var(--color-border);\n    }\n}\n\n@media (max-width: 991px) {\n    .gh-header.is-magazine .gh-header-left .gh-card:nth-child(2) .gh-card-image {\n        display: none;\n    }\n}\n\n@media (max-width: 767px) {\n    .gh-header.is-magazine .gh-header-inner {\n        display: flex;\n        flex-direction: column;\n        gap: var(--grid-gap);\n    }\n\n    .gh-header.is-magazine .gh-header-inner > div.gh-header-right {\n        flex-direction: column;\n    }\n\n    .gh-header.is-magazine .gh-card-image {\n        display: block !important;\n    }\n\n    .gh-header.is-magazine .gh-card::before {\n        display: block !important;\n        position: absolute;\n        top: calc(var(--grid-gap) / -2);\n        left: 0;\n        content: \"\";\n        width: 100%;\n        height: 1px;\n        background-color: var(--color-border);\n    }\n}\n\n/* 8.2. Highlight layout */\n\n.gh-header.is-highlight .gh-header-inner {\n    display: grid;\n    grid-template-columns: repeat(16, 1fr);\n    gap: var(--grid-gap);\n}\n\n.gh-header.is-highlight .gh-header-left {\n    position: relative;\n    grid-column: span 8;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card {\n    grid-column: span 8;\n    grid-row: span 3;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card .gh-card-link {\n    gap: 28px;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card .gh-card-image {\n    aspect-ratio: 3 / 2;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card .gh-card-tag {\n    display: block;\n    margin-bottom: 12px;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card .gh-card-title {\n    font-size: clamp(2.8rem, 1.36vw + 2.25rem, 4rem);\n    font-weight: 700;\n    line-height: 1.1;\n    letter-spacing: -0.022em;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card .gh-card-excerpt {\n    margin-top: clamp(12px, 0.45vw + 10.18px, 16px);\n    font-size: 1.8rem;\n}\n\n.gh-header.is-highlight .gh-header-left .gh-card .gh-card-meta:not(:empty) {\n    margin-top: 12px;\n}\n\n.gh-header.is-highlight .gh-header-middle {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    gap: var(--grid-gap);\n    grid-column: 9 / span 4;\n}\n\n.gh-header.is-highlight .gh-header-middle .gh-card:last-child .gh-card-image {\n    display: none;\n}\n\n.gh-header.is-highlight .gh-header-middle .gh-card .gh-card-excerpt {\n    display: none;\n}\n\n.gh-header.is-highlight .gh-header-right {\n    grid-column: 13 / -1;\n}\n\n.gh-header.is-highlight .gh-featured {\n    margin-top: 0;\n    padding: 0;\n}\n\n.gh-header.is-highlight .gh-featured-feed {\n    display: flex;\n    flex-direction: column;\n}\n\n.gh-header.is-highlight .gh-featured-feed .gh-card-title {\n    font-size: clamp(1.4rem, 0.23vw + 1.31rem, 1.6rem);\n}\n\n.gh-header.is-highlight .gh-header-left::after,\n.gh-header.is-highlight .gh-header-middle::after {\n    position: absolute;\n    top: 0;\n    right: calc(var(--grid-gap) / -2);\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    background-color: var(--color-border);\n}\n\n.gh-header.is-highlight .gh-card::before {\n    position: absolute;\n    top: calc(var(--grid-gap) / -2);\n    left: 0;\n    content: \"\";\n    width: 100%;\n    height: 1px;\n    background-color: var(--color-border);\n}\n\n.gh-header.is-highlight .gh-featured .gh-card:first-child::before {\n    display: none;\n}\n\n@media (max-width: 1199px) {\n    .gh-header.is-highlight .gh-header-inner {\n        grid-template-columns: repeat(9, 1fr);\n    }\n\n    .gh-header.is-highlight .gh-header-left {\n        grid-column: span 6;\n    }\n\n    .gh-header.is-highlight .gh-header-middle {\n        grid-column: 7 / -1;\n    }\n\n    .gh-header.is-highlight .gh-header-right {\n        grid-column: 1 / -1;\n    }\n\n    .gh-header.is-highlight .gh-featured-feed {\n        display: grid;\n        grid-template-columns: repeat(3, 1fr);\n    }\n\n    .gh-header.is-highlight .gh-featured-feed .gh-card:before {\n        width: calc(100% + var(--grid-gap));\n    }\n\n    .gh-header.is-highlight .gh-featured-feed .gh-card::after {\n        position: absolute;\n        top: 0;\n        left: calc(var(--grid-gap) / -2);\n        content: \"\";\n        width: 1px;\n        height: 100%;\n        background-color: var(--color-border);\n    }\n}\n\n@media (max-width: 991px) {\n    .gh-header.is-highlight .gh-header-middle .gh-card:nth-child(2) .gh-card-image {\n        display: none;\n    }\n}\n\n@media (max-width: 767px) {\n    .gh-header.is-highlight .gh-header-inner {\n        display: flex;\n        flex-direction: column;\n    }\n\n    .gh-header.is-highlight .gh-featured-feed {\n        display: flex;\n        /* gap: var(--grid-gap); */\n    }\n\n    .gh-header.is-highlight .gh-card-image {\n        display: block !important;\n    }\n\n    .gh-header.is-highlight .gh-header-middle .gh-card .gh-card-excerpt {\n        display: -webkit-box;\n    }\n}\n\n/* 8.3. Classic layout */\n\n.gh-header.is-classic {\n    display: flex;\n    margin-top: 0;\n    padding-block: 160px;\n}\n\n.gh-header.is-classic .gh-header-inner {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 48px;\n    overflow: unset;\n    margin: auto;\n    max-width: 1020px;\n    text-align: center;\n}\n\n.gh-header.is-classic .gh-header-title {\n    font-size: calc(clamp(3rem, 1.82vw + 2.27rem, 4.6rem) * var(--factor, 1));\n    line-height: 1.1;\n    letter-spacing: -0.028em;\n}\n\n.gh-header.is-classic.has-image {\n    margin-top: 0;\n}\n\n.gh-header.is-classic.has-image::before {\n    position: absolute;\n    inset: 0;\n    content: \"\";\n    opacity: 0.3;\n    background-color: var(--color-black);\n    transition: opacity 1.5s ease;\n}\n\n.gh-header.is-classic.has-image .gh-header-inner {\n    color: #fff;\n}\n\n.gh-header.is-classic.has-image .gh-header-image {\n    position: absolute;\n    inset: 0;\n    z-index: -1;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n.gh-header.is-classic.has-image .gh-form {\n    background-color: #fff;\n}\n\n/* 9. CTA\n/* ---------------------------------------------------------- */\n\n.gh-cta {\n    display: none;\n    margin-top: max(4vw, 40px);\n}\n\n.gh-header:is(.is-highlight, .is-magazine) + .gh-cta {\n    display: block;\n}\n\n.gh-cta-inner {\n    position: relative;\n    overflow: hidden;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 36px;\n    padding: clamp(48px, 3.64vw + 33.45px, 80px) clamp(40px, 2.73vw + 29.09px, 64px);\n    text-align: center;\n    background-color: var(--color-lighter-gray);\n}\n\n.gh-cta-content {\n    max-width: 640px;\n}\n\n.gh-cta-title {\n    font-size: clamp(2.8rem,1.36vw + 2.25rem,4rem);\n    font-weight: 700;\n    line-height: 1.1;\n    letter-spacing: -0.021em;\n}\n\n.gh-cta-description {\n    margin-top: 12px;\n    font-size: 1.8rem;\n    line-height: 1.4;\n    letter-spacing: -0.015em;\n}\n\n/* 10. Featured posts\n/* ---------------------------------------------------------- */\n\n.gh-featured {\n    margin-top: 100px;\n}\n\n.gh-navigation + .gh-featured {\n    margin-top: 64px;\n}\n\n.gh-header.is-classic:not(.has-image) + .gh-featured {\n    margin-top: 0;\n}\n\n.gh-featured-inner {\n    overflow: hidden;\n}\n\n.gh-featured-title {\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    font-size: 1.2rem;\n    font-weight: 550;\n    letter-spacing: 0.025em;\n    text-transform: uppercase;\n    border-bottom: 1px solid var(--color-border);\n}\n\n.gh-featured-feed {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: var(--grid-gap);\n}\n\n.gh-featured-feed .gh-card {\n    container-type: inline-size;\n    container-name: featured-card;\n}\n\n.gh-featured-feed .gh-card-link {\n    display: flex;\n    flex-direction: row-reverse;\n    align-items: flex-start;\n}\n\n.gh-featured-feed .gh-card-image {\n    width: 72px;\n    aspect-ratio: 1;\n}\n\n@container featured-card (width < 240px) {\n    .gh-featured-feed .gh-card-image {\n        display: none;\n    }\n}\n\n@container featured-card (240px <= width <= 270px) {\n    .gh-featured-feed .gh-card-image {\n        width: 64px;\n    }\n}\n\n.gh-featured-feed .gh-card-wrapper {\n    container-type: inline-size;\n    container-name: featured-card-wrapper;\n}\n\n.gh-featured-feed .gh-card-title {\n    font-size: 1.6rem;\n    font-weight: 650;\n    letter-spacing: -0.011em;\n}\n\n@container featured-card-wrapper (width < 170px) {\n    .gh-featured-feed .gh-card-title {\n        font-size: 1.6rem;\n    }\n}\n\n.gh-featured-feed .gh-card-excerpt {\n    display: none;\n}\n\n.gh-featured-feed .gh-card-meta:not(:empty) {\n    margin-top: 8px;\n}\n\n.gh-featured-feed .gh-card::before {\n    position: absolute;\n    top: 0;\n    left: calc(var(--grid-gap) / -2);\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    background-color: var(--color-border);\n}\n\n@media (max-width: 1199px) {\n    .gh-viewport > .gh-featured .gh-featured-feed {\n        grid-template-columns: repeat(3, 1fr);\n    }\n\n    .gh-viewport > .gh-featured .gh-featured-feed .gh-card:nth-child(4) {\n        display: none;\n    }\n}\n\n@media (max-width: 767px) {\n    .gh-viewport > .gh-featured .gh-featured-feed {\n        display: flex;\n        flex-direction: column;\n    }\n}\n\n/* 11. Container\n/* ---------------------------------------------------------- */\n\n.gh-container {\n    flex-grow: 1;\n    margin-top: 64px;\n}\n\n.gh-container-inner {\n    display: grid;\n    grid-template-columns: repeat(16, 1fr);\n    column-gap: var(--grid-gap);\n}\n\n:is(.gh-featured, .gh-cta) + .gh-container {\n    margin-top: max(4vw, 40px);\n}\n\n.gh-header.is-classic:not(.has-image) + .gh-container {\n    margin-top: 0;\n}\n\n.gh-navigation + .gh-container .gh-container-title,\n:is(.paged, .tag-template, .author-template) .gh-container:not(.has-sidebar) .gh-container-title {\n    display: none;\n}\n\n.gh-more {\n    display: none;\n    grid-column: 1 / -1;\n    margin-top: 48px;\n    font-size: calc(1.9rem * var(--factor, 1));\n    font-weight: 725;\n    letter-spacing: -0.014em;\n}\n\n.gh-container.has-sidebar .gh-more {\n    grid-column: span 12;\n}\n\n.home-template .gh-feed:has(> :nth-child(12):last-child) ~ .gh-more {\n    display: block;\n}\n\n.gh-more a {\n    display: flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.gh-more svg {\n    margin-top: -1px;\n    width: 18px;\n    height: 18px;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-more svg {\n    margin-top: 0;\n}\n\n/* 11.1. With sidebar */\n\n.gh-container.has-sidebar .gh-main {\n    grid-column: 1 / span 12;\n    position: relative;\n}\n\n.gh-container.has-sidebar .gh-sidebar {\n    grid-column: 13 / -1;\n}\n\n.gh-container.has-sidebar .gh-main::after {\n    position: absolute;\n    top: 0;\n    right: calc(var(--grid-gap) / -2);\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    background-color: var(--color-border);\n}\n\n/* 11.2. Without sidebar */\n\n.gh-container:not(.has-sidebar) :is(.gh-container-title, .gh-main, .gh-more) {\n    grid-column: 3 / span 12;\n}\n\n.gh-container.is-list.no-image:not(.has-sidebar) :is(.gh-container-title, .gh-main, .gh-more) {\n    grid-column: 4 / span 10;\n}\n\n.gh-header:is(.is-highlight, .is-magazine) ~ .gh-container.is-grid:not(.has-sidebar) :is(.gh-container-title, .gh-main, .gh-more) {\n    grid-column: 1 / -1;\n}\n\n@media (max-width: 1199px) {\n    .gh-container-inner {\n        display: block;\n        overflow: hidden;\n    }\n\n    .gh-container.has-sidebar .gh-sidebar {\n        display: none;\n    }\n}\n\n/* 12. Post list\n/* ---------------------------------------------------------- */\n\n.gh-container-title {\n    grid-column: 1 / -1;\n    margin-bottom: calc(var(--grid-gap) / 2);\n    padding-bottom: 12px;\n    font-size: 1.2rem;\n    font-weight: 550;\n    letter-spacing: 0.025em;\n    text-transform: uppercase;\n    border-bottom: 1px solid var(--color-border);\n}\n\n.gh-container:not(:has(.gh-card)) .gh-container-title {\n    display: none;\n}\n\n.gh-container .gh-feed {\n    gap: var(--grid-gap);\n}\n\n.gh-container .gh-card-meta:not(:empty) {\n    margin-top: 12px;\n}\n\n/* 12.1. List style */\n\n.gh-container.is-list .gh-feed {\n    display: flex;\n    flex-direction: column;\n    container-type: inline-size;\n    container-name: list-feed;\n}\n\n.gh-container.is-list .gh-card-link {\n    flex-direction: row;\n    align-items: center;\n    gap: 24px;\n}\n\n.gh-container.is-list .no-image .gh-card-link {\n    padding-block: 20px;\n}\n\n.gh-container.is-list .gh-card-image {\n    flex-shrink: 0;\n    width: 220px;\n    aspect-ratio: 1.618033;\n}\n\n@container list-feed (width < 600px) {\n    .gh-container.is-list .gh-card-image {\n        width: 160px;\n    }\n}\n\n.gh-container.is-list .gh-card:not(.no-image) .gh-card-wrapper {\n    max-width: 600px;\n}\n\n.gh-container.is-list .gh-card-title {\n    --factor: 1.05;\n}\n\n.gh-container.is-list .no-image .gh-card-title {\n    --factor: 1.2;\n}\n\n.gh-container.is-list .gh-card-excerpt {\n    margin-top: 6px;\n}\n\n.gh-container.is-list .gh-card + .gh-card::before {\n    position: absolute;\n    top: calc(var(--grid-gap) / -2);\n    left: 0;\n    content: \"\";\n    width: 100%;\n    height: 1px;\n    background-color: var(--color-border);\n}\n\n.home-template .gh-container.is-list .gh-card:first-child:before {\n    display: none;\n}\n\n@media (max-width: 767px) {\n    .gh-container.is-list .gh-card-link {\n        flex-direction: column;\n        align-items: flex-start;\n    }\n\n    .gh-container.is-list .gh-card-image {\n        width: 100%;\n    }\n}\n\n/* 12.2. Grid style */\n\n.gh-container.is-grid .gh-feed {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(248px, 1fr));\n    row-gap: calc(var(--grid-gap) * 1.5);\n    overflow: hidden;\n}\n\n.gh-container.is-grid .gh-card::before {\n    position: absolute;\n    top: calc(var(--grid-gap) / -2);\n    right: calc(var(--grid-gap) / -2);\n    left: calc(var(--grid-gap) / -2);\n    content: \"\";\n    height: 1px;\n    background-color: var(--color-border);\n}\n\n.gh-container.is-grid .gh-card::after {\n    position: absolute;\n    top: 0;\n    left: calc(var(--grid-gap) / -2);\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    background-color: var(--color-border);\n}\n\n@media (max-width: 767px) {\n    .gh-container.is-grid .gh-feed {\n        grid-template-columns: repeat(2, 1fr);\n    }\n}\n\n@media (max-width: 576px) {\n    .gh-container.is-grid .gh-feed {\n        display: flex;\n        flex-direction: column;\n    }\n}\n\n/* 12.3. No image list */\n\n.gh-container.is-list.no-image .gh-card-image {\n    display: none;\n}\n\n/* 13. Sidebar\n/* ---------------------------------------------------------- */\n\n.gh-sidebar-inner {\n    position: sticky;\n    top: calc(var(--grid-gap) / 2);\n}\n\n.gh-sidebar-title {\n    grid-column: 1 / -1;\n    margin-bottom: calc(var(--grid-gap) / 2);\n    padding-bottom: 12px;\n    font-size: 1.2rem;\n    font-weight: 550;\n    letter-spacing: 0.025em;\n    text-transform: uppercase;\n    border-bottom: 1px solid var(--color-border);\n}\n\n.gh-about {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    padding: 48px 24px;\n    min-height: 380px;\n    text-align: center;\n    background-color: var(--color-lighter-gray);\n}\n\n.gh-about-icon {\n    margin-bottom: 24px;\n    width: 60px;\n    height: 60px;\n    border-radius: 50%;\n}\n\n.gh-about-title {\n    font-size: calc(2.4rem * var(--factor, 1));\n    font-weight: 700;\n    letter-spacing: -0.019em;\n}\n\n.gh-about-description {\n    margin-top: 12px;\n    font-size: 1.45rem;\n    line-height: 1.4;\n}\n\n.gh-about .gh-button {\n    margin-top: 32px;\n}\n\n.gh-recommendations {\n    margin-top: 48px;\n}\n\n.gh-recommendations .recommendations {\n    display: flex;\n    flex-direction: column;\n    gap: 26px;\n    margin: 0;\n    padding: 0;\n    list-style-type: none;\n}\n\n.gh-recommendations .recommendation:first-child {\n    margin-top: 4px;\n}\n\n.gh-recommendations .recommendation a {\n    display: grid;\n    grid-template-columns: 24px auto;\n    gap: 4px 12px;\n}\n\n.gh-recommendations .recommendation a:hover {\n    opacity: 1;\n}\n\n.gh-recommendations .recommendation-favicon {\n    grid-row: span 2;\n    width: 100%;\n    border-radius: 4px;\n}\n\n.gh-recommendations .recommendation-title {\n    margin-top: -2px;\n    font-size: 1.5rem;\n    font-weight: 650;\n    letter-spacing: -0.009em;\n}\n\n.gh-recommendations .recommendation a:hover .recommendation-title {\n    opacity: 0.8;\n}\n\n.gh-recommendations .recommendation-url {\n    order: 1;\n    overflow: hidden;\n    font-size: 1.4rem;\n    line-height: 1.25;\n    color: var(--color-secondary-text);\n    text-overflow: ellipsis;\n}\n\n.gh-recommendations .recommendation-description {\n    display: -webkit-box;\n    display: none;\n    overflow-y: hidden;\n    grid-column: 2;\n    font-size: 1.4rem;\n    line-height: 1.4;\n    color: var(--color-secondary-text);\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n}\n\n.gh-recommendations button {\n    display: inline-flex;\n    align-items: center;\n    gap: 2px;\n    margin-top: 24px;\n    padding: 0;\n    cursor: pointer;\n    font-size: 1.2rem;\n    font-weight: 550;\n    letter-spacing: 0.025em;\n    text-transform: uppercase;\n    background-color: transparent;\n    border: 0;\n    color: var(--color-darker-gray);\n}\n\n.gh-recommendations button svg {\n    margin-top: -1px;\n    width: 12px;\n}\n\n.gh-recommendations button:hover {\n    opacity: 0.8;\n}\n\n/* 14. Post/page\n/* ---------------------------------------------------------- */\n\n/* 14.1. Article */\n\n.gh-article {\n    --container-width: 1120px;\n\n    word-break: break-word;\n}\n\n.gh-article-header {\n    margin: clamp(40px, 3.64vw + 25.45px, 72px) 0 40px;\n}\n\n.gh-article-tag {\n    margin-bottom: 12px;\n    font-size: 1.3rem;\n    font-weight: 500;\n    letter-spacing: 0.01em;\n    text-transform: uppercase;\n    color: var(--ghost-accent-color);\n}\n\n.gh-article-title {\n    font-size: calc(clamp(3.4rem, 1.36vw + 2.85rem, 4.6rem) * var(--factor, 1));\n    line-height: 1.1;\n    letter-spacing: -0.022em;\n}\n\n.gh-article-excerpt {\n    margin-top: clamp(12px, 0.45vw + 10.18px, 16px);\n    max-width: 720px;\n    font-size: clamp(1.7rem, 0.23vw + 1.61rem, 1.9rem);\n    line-height: 1.45;\n    letter-spacing: -0.018em;\n}\n\n.gh-article-meta {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-top: 20px;\n    margin-left: 6px;\n}\n\n.gh-article-author-image {\n    display: flex;\n    margin-right: 8px;\n}\n\n.gh-article-author-image a {\n    position: relative;\n    margin: 0 -8px;\n    width: 56px;\n    height: 56px;\n    overflow: hidden;\n    background-color: var(--color-light-gray);\n    border-radius: 50%;\n    border: 3px solid var(--background-color);\n}\n\n.gh-article-author-image a:first-child {\n    z-index: 10;\n}\n\n.gh-article-author-image a:nth-child(2) {\n    z-index: 9;\n}\n\n.gh-article-author-image a:nth-child(3) {\n    z-index: 8;\n}\n\n.gh-article-author-image :is(img, svg) {\n    position: absolute;\n    inset: 0;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n.gh-article-meta-wrapper {\n    display: flex;\n    flex-direction: column;\n    gap: 4px;\n}\n\n.gh-article-author-name {\n    font-size: 1.6rem;\n    font-weight: 650;\n    letter-spacing: -0.013em;\n}\n\n.gh-article-meta-content {\n    font-size: 1.35rem;\n    font-weight: 500;\n    line-height: 1.2;\n    letter-spacing: -0.006em;\n    color: var(--color-secondary-text);\n}\n\n.gh-article-image {\n    grid-column: wide;\n    margin-top: 40px;\n}\n\n.gh-article-image img {\n    width: 100%;\n}\n\n/* 14.2. Page template */\n\n.page-template .gh-article-header {\n    margin-block: 72px 32px;\n}\n\n/* 14.3. Page without header */\n\n.page-template .gh-content:only-child > *:first-child:not(.kg-width-full) {\n    margin-top: 64px;\n}\n\n.page-template .gh-content > *:last-child:not(.kg-width-full) {\n    margin-bottom: 6vw;\n}\n\n.page-template .gh-footer {\n    margin-top: 0;\n}\n\n/* 15. Content\n/* ---------------------------------------------------------- */\n\n/* Content refers to styling all page and post content that is\ncreated within the Ghost editor. The main content handles\nheadings, text, images and lists. We deal with cards lower down. */\n\n.gh-content {\n    font-size: var(--content-font-size, 1.7rem);\n    letter-spacing: -0.01em;\n}\n\n/* Default vertical spacing */\n.gh-content > * + * {\n    margin-top: calc(28px * var(--content-spacing-factor, 1));\n    margin-bottom: 0;\n}\n\n/* Remove space between full-width cards */\n.gh-content > .kg-width-full + .kg-width-full:not(.kg-width-full.kg-card-hascaption + .kg-width-full) {\n    margin-top: 0;\n}\n\n/* Add back a top margin to all headings,\nunless a heading is the very first element in the post content */\n.gh-content > [id]:not(:first-child) {\n    margin-top: calc(56px * var(--content-spacing-factor, 1));\n}\n\n/* Add drop cap setting */\n.post-template .gh-content.drop-cap > p:first-of-type:first-letter {\n    margin :0 0.2em 0em 0;\n    font-size: 3.1em;\n    float:left;\n    line-height: 1;\n    margin-left: -1px;\n    font-weight: 700;\n}\n\n.has-serif-body.post-template:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content.drop-cap > p:first-of-type:first-letter {\n    font-size: 3.2em;\n}\n\n/* Add a small margin between a heading and paragraph after it */\n.gh-content > [id] + p {\n    margin-top: calc(12px * var(--content-spacing-factor, 1));\n}\n\n/* A larger margin before/after dividers, blockquotes and embeds */\n.gh-content > :is(hr, blockquote, iframe) {\n    position: relative;\n    margin-top: calc(48px * var(--content-spacing-factor, 1)) !important;\n}\n\n.gh-content > :is(hr, blockquote, iframe) + * {\n    margin-top: calc(48px * var(--content-spacing-factor, 1)) !important;\n}\n\n/* Now the content typography styles */\n.gh-content h1 {\n    font-size: calc(2.2em * var(--factor, 1));\n    letter-spacing: -0.02em;\n}\n\n.gh-content h2 {\n    font-size: calc(1.6em * var(--factor, 1));\n    letter-spacing: -0.02em;\n}\n\n.gh-content h3 {\n    font-size: calc(1.3em * var(--factor, 1));\n    letter-spacing: -0.017em;\n}\n\n.gh-content a {\n    color: var(--ghost-accent-color);\n    text-decoration: underline;\n}\n\n.gh-content .kg-callout-card .kg-callout-text,\n.gh-content .kg-toggle-card .kg-toggle-content > :is(ul, ol, p) {\n    font-size: 0.95em;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > blockquote,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > ol,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > ul,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > dl,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > p,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-callout-text,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-content > ol,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-content > ul,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-content > p {\n    font-family: var(--font-serif-alt);\n}\n\n.gh-content :is(ul, ol) {\n    padding-left: 28px;\n}\n\n.gh-content :is(li + li, li :is(ul, ol)) {\n    margin-top: 8px;\n}\n\n.gh-content ol ol li {\n    list-style-type: lower-alpha;\n}\n\n.gh-content ol ol ol li {\n    list-style-type: lower-roman;\n}\n\n.gh-content hr {\n    width: 100%;\n    height: 1px;\n    background-color: var(--color-border);\n    border: 0;\n}\n\n.gh-content .gh-table {\n    overflow-x: scroll;\n    -webkit-overflow-scrolling: touch;\n}\n\n.gh-content .gh-table table {\n    width: 100%;\n    font-family: var(--font-sans);\n    font-size: 1.5rem;\n    white-space: nowrap;\n    vertical-align: top;\n    border-spacing: 0;\n    border-collapse: collapse;\n}\n\n.gh-content .gh-table table th {\n    font-size: 1.2rem;\n    font-weight: 700;\n    color: var(--color-darkgrey);\n    text-align: left;\n    text-transform: uppercase;\n    letter-spacing: 0.2px;\n}\n\n.gh-content .gh-table table :is(th, td),\n.gh-content .gh-table table td {\n    padding: 6px 12px;\n    border-bottom: 1px solid var(--color-border);\n}\n\n.gh-content .gh-table table :is(th, td):first-child {\n    padding-left: 0;\n}\n\n.gh-content .gh-table table :is(th, td):last-child {\n    padding-right: 0;\n}\n\n.gh-content pre {\n    overflow: auto;\n    padding: 16px;\n    font-size: 1.5rem;\n    line-height: 1.5em;\n    background: var(--color-lighter-gray);\n    border-radius: 6px;\n    font-family: var(--font-mono);\n}\n\n.gh-content :not(pre) > code {\n    vertical-align: baseline;\n    padding: 0.15em 0.4em;\n    font-weight: 400;\n    font-size: 0.95em;\n    line-height: 1em;\n    background: var(--color-lighter-gray);\n    border-radius: 0.25em;\n    font-family: var(--font-mono);\n}\n\n/* 16. Cards\n/* ---------------------------------------------------------- */\n\n/* Add extra margin before/after any cards, except for when immediately preceeded by a heading */\n\n.gh-content :not(.kg-card):not(table):not([id]) + :is(.kg-card, table) {\n    margin-top: calc(48px * var(--content-spacing-factor, 1));\n}\n\n.gh-content :is(.kg-card, table) + :not(.kg-card):not(table):not([id]) {\n    margin-top: calc(48px * var(--content-spacing-factor, 1));\n}\n\n.gh-content :not(.kg-card):not([id]) + .kg-card.kg-width-full {\n    margin-top: calc(68px * var(--content-spacing-factor, 1));\n}\n\n.gh-content .kg-card.kg-width-full + :not(.kg-card):not([id]) {\n    margin-top: calc(68px * var(--content-spacing-factor, 1));\n}\n\n/* Image */\n\n.kg-image {\n    margin-right: auto;\n    margin-left: auto;\n}\n\n/* Embed */\n\n.kg-embed-card {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    width: 100%;\n}\n\n/* Gallery */\n\n.kg-image[width][height],\n.kg-gallery-image {\n    cursor: pointer;\n}\n\n.kg-image-card a:hover,\n.kg-gallery-image a:hover {\n    opacity: 1 !important;\n}\n\n/* Toggle */\n\n.kg-card.kg-toggle-card .kg-toggle-heading-text {\n    font-size: 2rem;\n    font-weight: 700;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-toggle-card .kg-toggle-heading-text {\n    font-family: var(--font-serif);\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-toggle-card .kg-toggle-heading-text {\n    font-family: var(--font-mono);\n}\n\n/* Callout */\n\n.kg-callout-card.kg-card {\n    border-radius: 0.25em;\n}\n\n.kg-callout-card-accent a {\n    text-decoration: underline;\n}\n\n/* Blockquote */\n\nblockquote:not([class]) {\n    padding-left: 2rem;\n    border-left: 4px solid var(--ghost-accent-color);\n}\n\nblockquote.kg-blockquote-alt {\n    font-style: normal;\n    font-weight: 400;\n    color: var(--color-secondary-text);\n}\n\n/* Button */\n\n.kg-card.kg-button-card .kg-btn {\n    height: unset;\n    padding: .6em 1.2em;\n    text-align: center;\n    font-size: 1em;\n    line-height: 1.2em;\n}\n\n/* Header */\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-card.kg-header-card h2.kg-header-card-header {\n    font-family: var(--font-serif);\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-card.kg-header-card h2.kg-header-card-header {\n    font-family: var(--font-mono);\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .kg-header-card h3.kg-header-card-subheader {\n    font-family: var(--font-serif);\n}\n\n/* Bookmark */\n\n.kg-bookmark-card .kg-bookmark-container {\n    border-radius: 0.25em !important;\n}\n\n.kg-bookmark-card .kg-bookmark-container:hover {\n    opacity: 1;\n}\n\n.kg-bookmark-card a.kg-bookmark-container,\n.kg-bookmark-card a.kg-bookmark-container:hover {\n    background: var(--background-color) !important;\n    color: var(--color-darker-gray) !important;\n}\n\n.kg-bookmark-card .kg-bookmark-content {\n    padding: 1.15em;\n}\n\n.kg-bookmark-card .kg-bookmark-title {\n    font-size: 0.9em;\n}\n\n.kg-bookmark-card .kg-bookmark-description {\n    max-height: none;\n    margin-top: 0.3em;\n    font-size: 0.8em;\n}\n\n.kg-bookmark-card .kg-bookmark-metadata {\n    font-size: 0.8em;\n}\n\n.kg-bookmark-card .kg-bookmark-thumbnail img {\n    border-radius: 0 0.2em 0.2em 0;\n}\n\n/* Product */\n\n.kg-product-card.kg-card .kg-product-card-image {\n    margin-bottom: 12px;\n}\n\n.kg-product-card.kg-card a.kg-product-card-button {\n    height: 2.8em;\n    margin-top: 12px;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-product-card.kg-card .kg-product-card-title {\n    font-family: var(--font-serif);\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-product-card.kg-card .kg-product-card-title {\n    font-family: var(--font-mono);\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .kg-product-card-description :is(p, ul, ol) {\n    font-family: var(--font-serif-alt);\n}\n\n/* File */\n\n.kg-file-card.kg-card .kg-file-card-container {\n    padding: 0.6em;\n}\n\n.kg-file-card.kg-card .kg-file-card-contents {\n    margin: 4px 8px 6px;\n}\n\n.kg-file-card.kg-card .kg-file-card-metadata {\n    font-size: 0.9em;\n}\n\n.kg-file-card.kg-card .kg-file-card-filesize::before {\n    margin-right: 6px;\n    margin-left: 6px;\n}\n\n/* Caption */\n\nfigcaption {\n    margin-top: 12px;\n    font-size: 1.4rem;\n    text-align: center;\n}\n\n.kg-card.kg-width-full figcaption {\n    padding: 0 16px;\n}\n\nfigcaption a {\n    color: var(--ghost-accent-color);\n    text-decoration: underline;\n}\n\n/* 17. Comments\n/* ---------------------------------------------------------- */\n\n.gh-comments {\n    margin-top: 48px;\n}\n\n/* 18. Recent posts\n/* ---------------------------------------------------------- */\n\n.post-template .gh-container {\n    margin-top: 120px;\n}\n\n.post-template .gh-container-inner {\n    display: block;\n}\n\n.post-template .gh-container.is-grid .gh-feed {\n    grid-template-columns: repeat(4, 1fr);\n}\n\n.post-template .gh-container .gh-container-title {\n    display: block;\n}\n\n/* 19. Archive\n/* ---------------------------------------------------------- */\n\n.gh-archive {\n    display: grid;\n    grid-template-columns: repeat(16, 1fr);\n    gap: var(--grid-gap);\n    margin-block: 80px 24px;\n}\n\n.gh-archive.has-image {\n    margin-top: 48px;\n}\n\n.gh-archive-inner {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: var(--grid-gap);\n    grid-column: 1 / -1;\n    padding-bottom: clamp(40px, 2.73vw + 29.09px, 64px);\n    border-bottom: 1px solid var(--color-border);\n}\n\n.gh-archive.has-image .gh-archive-inner {\n    align-items: center;\n    grid-column: 1 / -1;\n}\n\n.gh-archive:not(.has-sidebar):not(.has-image) .gh-archive-inner {\n    grid-column: 3 / span 12;\n}\n\n.gh-archive .gh-article-image {\n    grid-column: auto;\n    margin-top: 0;\n}\n\n:is(.tag-template, .author-template) .gh-container {\n    margin-top: 0;\n}\n\n.author-template .gh-archive-inner {\n    display: flex;\n    flex-direction: row-reverse;\n    justify-content: flex-end;\n    gap: 24px;\n}\n\n.author-template .gh-article-image {\n    margin-top: 0;\n    width: 120px;\n    height: 120px;\n    border-radius: 50%;\n    object-fit: cover;\n}\n\n.author-template .gh-article-title {\n    font-size: 3.6rem;\n}\n\n.gh-author-meta {\n    display: flex;\n    gap: 10px;\n    margin-top: 14px;\n    font-size: 1.5rem;\n    font-weight: 550;\n    color: var(--color-secondary-text);\n}\n\n.gh-author-meta a {\n    color: inherit;\n}\n\n.gh-author-social {\n    display: flex;\n    gap: 16px;\n    align-items: center;\n    padding-left: 3px;\n}\n\n.gh-author-social svg {\n    width: 20px;\n    height: 20px;\n}\n\n@media (max-width: 1199px) {\n    .gh-archive {\n        display: block;\n    }\n}\n\n@media (max-width: 767px) {\n    .gh-archive-inner {\n        display: flex;\n        flex-direction: column-reverse;\n        align-items: flex-start;\n    }\n\n    .author-template .gh-archive-inner {\n        flex-direction: column-reverse;\n    }\n}\n\n/* 20. Design settings\n/* ---------------------------------------------------------- */\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) {\n    --factor: 1.15;\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) {\n    --factor: 1.1;\n}\n\n.has-sans-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) :is(.is-title, .gh-content :is(h2, h3)) {\n    font-family: var(--gh-font-heading, var(--font-sans));\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) :is(.is-title, .gh-content :is(h2, h3)) {\n    font-family: var(--gh-font-heading, var(--font-serif));\n    font-weight: 550;\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) :is(.is-title, .gh-content :is(h2, h3)) {\n    font-family: var(--gh-font-heading, var(--font-mono));\n}\n\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .is-body {\n    font-family: var(--gh-font-body, var(--font-sans));\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .is-body {\n    font-family: var(--gh-font-body, var(--font-serif-alt));\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-header.is-classic .gh-header-title {\n    font-weight: 550;\n    letter-spacing: -0.015em;\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-header.is-classic .gh-header-title {\n    letter-spacing: -0.01em;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-form {\n    border-radius: 0;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-card-title {\n    line-height: 1.15;\n    letter-spacing: -0.006em;\n    font-size: calc(2.0rem*var(--factor, 1))\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-featured-feed .gh-card-title {\n    font-size: calc(1.6rem*var(--factor, 1))\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-featured-feed .gh-card-title {\n    font-size: calc(1.5rem*var(--factor, 1));\n    letter-spacing: 0;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-header.is-highlight .gh-featured-feed .gh-card-title {\n    font-size: clamp(1.6rem, 0.23vw + 1.51rem, 1.8rem);\n}\n\n.has-mono-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-card-title {\n    font-size: calc(1.8rem*var(--factor, 1));\n    line-height: 1.2;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-about-title {\n    letter-spacing: -0.009em;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-footer-signup-header {\n    letter-spacing: -0.019em;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-article-title {\n    letter-spacing: -0.019em;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) {\n    --content-font-size: 1.9rem;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-card-excerpt {\n    font-size: 1.65rem;\n    line-height: 1.4;\n    letter-spacing: 0.0005em;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-header.is-magazine .gh-header-inner > .gh-card .gh-card-excerpt,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-header.is-highlight .gh-card:first-child .gh-card-excerpt {\n    font-size: 1.8rem;\n    letter-spacing: -0.001em;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-header.is-magazine .gh-header-inner>.gh-card .gh-card-title,\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-header.is-highlight .gh-header-left .gh-card-title {\n    font-weight: 550;\n    font-size: clamp(3.2rem,1.82vw + 2.47rem,4.9rem)\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-about-description {\n    font-size: 1.6rem;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-article-excerpt {\n    letter-spacing: 0;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-footer-signup-subhead {\n    letter-spacing: 0;\n}\n\n:is(.has-serif-title,.has-mono-title):not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) :is(.gh-button, .gh-form, .gh-form-input),\nbody[class*=\" gh-font-heading\"]:not(.gh-font-heading-fira-sans):not(.gh-font-heading-inter):not(.gh-font-heading-manrope):not(.gh-font-heading-noto-sans):not(.gh-font-heading-nunito):not(.gh-font-heading-poppins):not(.gh-font-heading-roboto):not(.gh-font-heading-space-grotesk) :is(.gh-button, .gh-form, .gh-form-input) {\n    border-radius: 0;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-cta-title {\n    font-size: 4.8rem;\n}\n\n/* 21. Footer\n/* ---------------------------------------------------------- */\n\n.gh-footer {\n    margin-top: 12vw;\n    font-size: 1.5rem;\n    color: var(--color-darker-gray);\n}\n\n.gh-footer a:not(.gh-button) {\n    color: inherit;\n}\n\n/* 21.1 Footer styles */\n\n.gh-footer.has-accent-color {\n    background-color: var(--ghost-accent-color);\n}\n\n.gh-footer.has-accent-color .gh-footer-bar {\n    border-top: 0;\n}\n\n.gh-footer.has-accent-color .gh-button {\n    background-color: #fff;\n    color: #15171a;\n}\n\n/* 21.2. Footer bar */\n\n.gh-footer-bar {\n    display: grid;\n    grid-template-columns: 1fr auto 1fr;\n    align-items: center;\n    column-gap: 32px;\n    margin-bottom: 100px;\n    padding-block: 28px;\n    font-weight: 550;\n    border-block: 1px solid var(--color-border);\n}\n\n.gh-footer-logo {\n    position: relative;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-size: calc(2.4rem * var(--factor, 1));\n    font-weight: 725;\n    letter-spacing: -0.015em;\n    white-space: nowrap;\n}\n\n.gh-footer-logo img {\n    max-height: 40px;\n}\n\n.gh-footer-menu .nav {\n    display: flex;\n    justify-content: center;\n    gap: 8px 28px;\n    flex-wrap: wrap;\n    margin: 0;\n    padding: 0;\n    list-style-type: none;\n}\n\n.gh-footer-copyright {\n    text-align: right;\n    white-space: nowrap;\n}\n\n.gh-footer-copyright a {\n    text-decoration: underline;\n}\n\n/* 21.3. Footer signup */\n\n.gh-footer-signup {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding-bottom: 160px;\n    text-align: center;\n}\n\n.gh-footer-signup-header {\n    font-size: calc(clamp(2.8rem,1.36vw + 2.25rem,4rem) * var(--factor, 1));\n    font-weight: 700;\n    letter-spacing: -0.03em;\n}\n\n.gh-footer-signup-subhead {\n    margin-top: 12px;\n    max-width: 640px;\n    font-size: 1.8rem;\n    font-weight: 450;\n    line-height: 1.4;\n    letter-spacing: -0.014em;\n    opacity: 0.75;\n}\n\n.gh-footer-signup .gh-form {\n    margin-top: 40px;\n}\n\n@media (max-width: 991px) {\n    .gh-footer-bar {\n        display: flex;\n        flex-direction: column;\n        gap: 20px;\n    }\n}\n\n/* 22. Lightbox\n/* ---------------------------------------------------------- */\n\n.pswp {\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 3999999;\n    display: none;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    -ms-touch-action: none;\n    touch-action: none;\n    outline: none;\n    backface-visibility: hidden;\n    -webkit-text-size-adjust: 100%;\n}\n\n.pswp img {\n    max-width: none;\n}\n\n.pswp--animate_opacity {\n    opacity: 0.001;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    will-change: opacity;\n}\n\n.pswp--open {\n    display: block;\n}\n\n.pswp--zoom-allowed .pswp__img {\n    cursor: zoom-in;\n}\n\n.pswp--zoomed-in .pswp__img {\n    cursor: grab;\n}\n\n.pswp--dragging .pswp__img {\n    cursor: grabbing;\n}\n\n.pswp__bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, 0.85);\n    opacity: 0;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    transform: translateZ(0);\n    backface-visibility: hidden;\n    will-change: opacity;\n}\n\n.pswp__scroll-wrap {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n}\n\n.pswp__container,\n.pswp__zoom-wrap {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    touch-action: none;\n    backface-visibility: hidden;\n}\n\n.pswp__container,\n.pswp__img {\n    user-select: none;\n    -webkit-tap-highlight-color: transparent;\n    -webkit-touch-callout: none;\n}\n\n.pswp__zoom-wrap {\n    position: absolute;\n    width: 100%;\n    transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    transform-origin: left top;\n}\n\n.pswp--animated-in .pswp__bg,\n.pswp--animated-in .pswp__zoom-wrap {\n    transition: none;\n}\n\n.pswp__item {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    overflow: hidden;\n}\n\n.pswp__img {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: auto;\n    height: auto;\n}\n\n.pswp__img--placeholder {\n    backface-visibility: hidden;\n}\n\n.pswp__img--placeholder--blank {\n    background: var(--color-black);\n}\n\n.pswp--ie .pswp__img {\n    top: 0;\n    left: 0;\n    width: 100% !important;\n    height: auto !important;\n}\n\n.pswp__error-msg {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    width: 100%;\n    margin-top: -8px;\n    font-size: 14px;\n    line-height: 16px;\n    color: var(--color-secondary-text);\n    text-align: center;\n}\n\n.pswp__error-msg a {\n    color: var(--color-secondary-text);\n    text-decoration: underline;\n}\n\n.pswp__button {\n    position: relative;\n    display: block;\n    float: right;\n    width: 44px;\n    height: 44px;\n    padding: 0;\n    margin: 0;\n    overflow: visible;\n    appearance: none;\n    cursor: pointer;\n    background: none;\n    border: 0;\n    box-shadow: none;\n    transition: opacity 0.2s;\n}\n\n.pswp__button:focus,\n.pswp__button:hover {\n    opacity: 1;\n}\n\n.pswp__button:active {\n    outline: none;\n    opacity: 0.9;\n}\n\n.pswp__button::-moz-focus-inner {\n    padding: 0;\n    border: 0;\n}\n\n.pswp__ui--over-close .pswp__button--close {\n    opacity: 1;\n}\n\n.pswp__button,\n.pswp__button--arrow--left::before,\n.pswp__button--arrow--right::before {\n    width: 44px;\n    height: 44px;\n    background: url(\"../images/default-skin.png\") 0 0 no-repeat;\n    background-size: 264px 88px;\n}\n\n@media (-webkit-min-device-pixel-ratio: 1.1), (-webkit-min-device-pixel-ratio: 1.09375), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {\n    .pswp--svg .pswp__button,\n    .pswp--svg .pswp__button--arrow--left::before,\n    .pswp--svg .pswp__button--arrow--right::before {\n        background-image: url(\"../images/default-skin.svg\");\n    }\n\n    .pswp--svg .pswp__button--arrow--left,\n    .pswp--svg .pswp__button--arrow--right {\n        background: none;\n    }\n}\n\n.pswp__button--close {\n    background-position: 0 -44px;\n}\n\n.pswp__button--share {\n    background-position: -44px -44px;\n}\n\n.pswp__button--fs {\n    display: none;\n}\n\n.pswp--supports-fs .pswp__button--fs {\n    display: block;\n}\n\n.pswp--fs .pswp__button--fs {\n    background-position: -44px 0;\n}\n\n.pswp__button--zoom {\n    display: none;\n    background-position: -88px 0;\n}\n\n.pswp--zoom-allowed .pswp__button--zoom {\n    display: block;\n}\n\n.pswp--zoomed-in .pswp__button--zoom {\n    background-position: -132px 0;\n}\n\n.pswp--touch .pswp__button--arrow--left,\n.pswp--touch .pswp__button--arrow--right {\n    visibility: hidden;\n}\n\n.pswp__button--arrow--left,\n.pswp__button--arrow--right {\n    position: absolute;\n    top: 50%;\n    width: 70px;\n    height: 100px;\n    margin-top: -50px;\n    background: none;\n}\n\n.pswp__button--arrow--left {\n    left: 0;\n}\n\n.pswp__button--arrow--right {\n    right: 0;\n}\n\n.pswp__button--arrow--left::before,\n.pswp__button--arrow--right::before {\n    position: absolute;\n    top: 35px;\n    width: 32px;\n    height: 30px;\n    content: \"\";\n}\n\n.pswp__button--arrow--left::before {\n    left: 6px;\n    background-position: -138px -44px;\n}\n\n.pswp__button--arrow--right::before {\n    right: 6px;\n    background-position: -94px -44px;\n}\n\n.pswp__counter {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 44px;\n    padding: 0 15px;\n    font-size: 11px;\n    font-weight: 700;\n    line-height: 44px;\n    color: var(--color-white);\n    user-select: none;\n}\n\n.pswp__caption {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    min-height: 44px;\n}\n\n.pswp__caption__center {\n    max-width: 420px;\n    padding: 25px 15px 30px;\n    margin: 0 auto;\n    font-size: 11px;\n    line-height: 1.6;\n    color: var(--color-white);\n    text-align: center;\n}\n\n.pswp__caption__center .post-caption-title {\n    margin-bottom: 7px;\n    font-size: 15px;\n    font-weight: 500;\n    text-transform: uppercase;\n}\n\n.pswp__caption__center .post-caption-meta-item + .post-caption-meta-item::before {\n    padding: 0 4px;\n    content: \"\\02022\";\n}\n\n.pswp__caption--empty {\n    display: none;\n}\n\n.pswp__caption--fake {\n    visibility: hidden;\n}\n\n.pswp__preloader {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    width: 44px;\n    height: 44px;\n    margin-left: -22px;\n    opacity: 0;\n    transition: opacity 0.25s ease-out;\n    direction: ltr;\n    will-change: opacity;\n}\n\n.pswp__preloader__icn {\n    width: 20px;\n    height: 20px;\n    margin: 12px;\n}\n\n.pswp__preloader--active {\n    opacity: 1;\n}\n\n.pswp__preloader--active .pswp__preloader__icn {\n    background: url(\"../images/preloader.gif\") 0 0 no-repeat;\n}\n\n.pswp--css_animation .pswp__preloader--active {\n    opacity: 1;\n}\n\n.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {\n    animation: clockwise 500ms linear infinite;\n}\n\n.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {\n    animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;\n}\n\n.pswp--css_animation .pswp__preloader__icn {\n    position: absolute;\n    top: 15px;\n    left: 15px;\n    width: 14px;\n    height: 14px;\n    margin: 0;\n    background: none;\n    opacity: 0.75;\n}\n\n.pswp--css_animation .pswp__preloader__cut {\n    position: relative;\n    width: 7px;\n    height: 14px;\n    overflow: hidden;\n}\n\n.pswp--css_animation .pswp__preloader__donut {\n    position: absolute;\n    top: 0;\n    left: 0;\n    box-sizing: border-box;\n    width: 14px;\n    height: 14px;\n    margin: 0;\n    background: none;\n    border: 2px solid var(--color-white);\n    border-bottom-color: transparent;\n    border-left-color: transparent;\n    border-radius: 50%;\n}\n\n@media screen and (max-width: 1024px) {\n    .pswp__preloader {\n        position: relative;\n        top: auto;\n        left: auto;\n        float: right;\n        margin: 0;\n    }\n}\n\n@keyframes clockwise {\n    0% {\n        transform: rotate(0deg);\n    }\n\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n@keyframes donut-rotate {\n    0% {\n        transform: rotate(0);\n    }\n\n    50% {\n        transform: rotate(-140deg);\n    }\n\n    100% {\n        transform: rotate(0);\n    }\n}\n\n.pswp__ui {\n    z-index: 1550;\n    visibility: visible;\n    opacity: 1;\n    -webkit-font-smoothing: auto;\n}\n\n.pswp__top-bar {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 44px;\n}\n\n.pswp__caption,\n.pswp__top-bar,\n.pswp--has_mouse .pswp__button--arrow--left,\n.pswp--has_mouse .pswp__button--arrow--right {\n    backface-visibility: hidden;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    will-change: opacity;\n}\n\n.pswp--has_mouse .pswp__button--arrow--left,\n.pswp--has_mouse .pswp__button--arrow--right {\n    visibility: visible;\n}\n\n.pswp__ui--idle .pswp__top-bar {\n    opacity: 0;\n}\n\n.pswp__ui--idle .pswp__button--arrow--left,\n.pswp__ui--idle .pswp__button--arrow--right {\n    opacity: 0;\n}\n\n.pswp__ui--hidden .pswp__top-bar,\n.pswp__ui--hidden .pswp__caption,\n.pswp__ui--hidden .pswp__button--arrow--left,\n.pswp__ui--hidden .pswp__button--arrow--right {\n    opacity: 0.001;\n}\n\n.pswp__ui--one-slide .pswp__button--arrow--left,\n.pswp__ui--one-slide .pswp__button--arrow--right,\n.pswp__ui--one-slide .pswp__counter {\n    display: none;\n}\n\n.pswp__element--disabled {\n    display: none !important;\n}\n\n.pswp--minimal--dark .pswp__top-bar {\n    background: none;\n}\n"]}