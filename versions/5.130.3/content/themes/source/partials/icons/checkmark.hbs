<svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
    <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
    <style>
        .checkmark {
            width: 40px;
            height: 40px;
            display: block;
            stroke-width: 2.5;
            stroke: currentColor;
            stroke-miterlimit: 10;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke .3s cubic-bezier(0.650, 0.000, 0.450, 1.000) forwards;
        }

        @keyframes stroke {
            100% { stroke-dashoffset: 0; }
        }
    </style>
</svg>