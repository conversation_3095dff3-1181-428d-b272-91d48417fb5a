<svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" viewBox="0 0 24 24">
    <g stroke-linecap="round" stroke-width="2" fill="currentColor" stroke="none" stroke-linejoin="round" class="nc-icon-wrapper">
        <g class="nc-loop-dots-4-24-icon-o">
            <circle cx="4" cy="12" r="3"></circle>
            <circle cx="12" cy="12" r="3"></circle>
            <circle cx="20" cy="12" r="3"></circle>
        </g>
        <style data-cap="butt">
            .nc-loop-dots-4-24-icon-o{--animation-duration:0.8s}
            .nc-loop-dots-4-24-icon-o *{opacity:.4;transform:scale(.75);animation:nc-loop-dots-4-anim var(--animation-duration) infinite}
            .nc-loop-dots-4-24-icon-o :nth-child(1){transform-origin:4px 12px;animation-delay:-.3s;animation-delay:calc(var(--animation-duration)/-2.666)}
            .nc-loop-dots-4-24-icon-o :nth-child(2){transform-origin:12px 12px;animation-delay:-.15s;animation-delay:calc(var(--animation-duration)/-5.333)}
            .nc-loop-dots-4-24-icon-o :nth-child(3){transform-origin:20px 12px}
            @keyframes nc-loop-dots-4-anim{0%,100%{opacity:.4;transform:scale(.75)}50%{opacity:1;transform:scale(1)}}
        </style>
    </g>
</svg>