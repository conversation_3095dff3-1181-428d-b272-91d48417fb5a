{{!< default}}
{{!-- The tag above means - insert everything in this file into the {body} of the default.hbs template --}}

<main id="site-main" class="site-main outer">
<div class="inner posts">

    <div class="post-feed">

        {{#author}}
        <section class="post-card post-card-large">

            {{#if cover_image}}
            <div class="post-card-image-link">
                {{!-- This is a responsive image, it loads different sizes depending on device
                https://medium.freecodecamp.org/a-guide-to-responsive-images-with-ready-to-use-templates-c400bd65c433 --}}
                <img class="post-card-image"
                    srcset="{{img_url cover_image size="s"}} 300w,
                            {{img_url cover_image size="m"}} 600w,
                            {{img_url cover_image size="l"}} 1000w,
                            {{img_url cover_image size="xl"}} 2000w"
                    sizes="(max-width: 1000px) 400px, 800px"
                    src="{{img_url cover_image size="m"}}"
                    alt="{{title}}"
                />
            </div>
            {{/if}}

            <div class="post-card-content">
            <div class="post-card-content-link">

                {{#if profile_image}}
                    <img class="author-profile-pic" src="{{profile_image}}" alt="{{name}}" />
                {{/if}}

                <header class="post-card-header">
                    <h2 class="post-card-title">{{name}}</h2>
                </header>

                {{#if bio}}
                    <div class="post-card-excerpt">{{bio}}</div>
                {{/if}}

                <footer class="author-profile-footer">
                    {{#if location}}
                        <div class="author-profile-location">{{location}}</div>
                    {{/if}}
                    <div class="author-profile-meta">
                        {{#if website}}
                            <a class="author-profile-social-link" href="{{website}}" target="_blank" rel="noopener">{{website}}</a>
                        {{/if}}
                        {{#if twitter}}
                            <a class="author-profile-social-link" href="{{social_url type="twitter"}}" target="_blank" rel="noopener">{{> "icons/twitter"}}</a>
                        {{/if}}
                        {{#if facebook}}
                            <a class="author-profile-social-link" href="{{social_url type="facebook"}}" target="_blank" rel="noopener">{{> "icons/facebook"}}</a>
                        {{/if}}
                        {{#if linkedin}}
                            <a class="author-profile-social-link" href="{{social_url type="linkedin"}}" target="_blank" rel="noopener">{{> "icons/linkedin"}}</a>
                        {{/if}}
                        {{#if bluesky}}
                            <a class="author-profile-social-link" href="{{social_url type="bluesky"}}" target="_blank" rel="noopener">{{> "icons/bluesky"}}</a>
                        {{/if}}
                        {{#if threads}}
                            <a class="author-profile-social-link" href="{{social_url type="threads"}}" target="_blank" rel="noopener">{{> "icons/threads"}}</a>
                        {{/if}}
                        {{#if mastodon}}
                            <a class="author-profile-social-link" href="{{social_url type="mastodon"}}" target="_blank" rel="noopener">{{> "icons/mastodon"}}</a>
                        {{/if}}
                        {{#if tiktok}}
                            <a class="author-profile-social-link" href="{{social_url type="tiktok"}}" target="_blank" rel="noopener">{{> "icons/tiktok"}}</a>
                        {{/if}}
                        {{#if youtube}}
                            <a class="author-profile-social-link" href="{{social_url type="youtube"}}" target="_blank" rel="noopener">{{> "icons/youtube"}}</a>
                        {{/if}}
                        {{#if instagram}}
                            <a class="author-profile-social-link" href="{{social_url type="instagram"}}" target="_blank" rel="noopener">{{> "icons/instagram"}}</a>
                        {{/if}}
                    </div>
                </footer>

            </div>
            </div>

        </section>
        {{/author}}

        {{#foreach posts}}
            {{!-- The tag below includes the markup for each post - partials/post-card.hbs --}}
            {{> "post-card"}}
        {{/foreach}}

    </div>

    {{pagination}}
    
</div>
</main>
