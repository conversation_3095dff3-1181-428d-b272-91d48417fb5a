{"version": 3, "sources": ["global.css", "screen.css"], "names": [], "mappings": "AAGA,8YA+EI,QAAS,CACT,YAAa,CACb,cAAe,CAJf,QAAS,CACT,SAAU,CAIV,uBACJ,CACA,KACI,aACJ,CACA,aAEI,WACJ,CACA,oDAII,UAAW,CACX,YACJ,CAKA,IACI,aAAc,CAEd,WAAY,CADZ,cAEJ,CACA,KAII,yBAA0B,CAC1B,6BAA8B,CAJ9B,qBAAsB,CACtB,sBAIJ,CACA,iBAGI,kBACJ,CACA,EACI,4BACJ,CACA,iBAEI,SACJ,CACA,SAEI,eACJ,CACA,SAGI,iBACJ,CACA,GAEI,aAAc,CADd,cAEJ,CACA,MACI,aACJ,CACA,QAGI,aAAc,CACd,aAAc,CAFd,iBAAkB,CAGlB,uBACJ,CACA,IACI,SACJ,CACA,IACI,aACJ,CACA,IACI,QACJ,CACA,eACI,eACJ,CACA,KACI,wBACJ,CACA,kBAII,+BAAiC,CACjC,aACJ,CACA,IAII,kBAAmB,CACnB,sCAA2C,CAC3C,iBAAkB,CAClB,+CAAoD,CALpD,4BAA6B,CAC7B,gBAAiB,CAFjB,eAOJ,CACA,yBACI,IACI,gBACJ,CACJ,CACA,sCAMI,aAAc,CACd,YAAa,CAFb,QAGJ,CACA,OAEI,WAAY,CADZ,gBAEJ,CACA,cAEI,mBACJ,CACA,oEAOI,yBAA0B,CAF1B,cAGJ,CACA,sCAEI,cACJ,CACA,iDAGI,QAAS,CADT,SAEJ,CACA,MACI,kBACJ,CACA,YACI,YACJ,CACA,uCAEI,qBAAsB,CACtB,SACJ,CACA,4FAEI,WACJ,CACA,mBAGI,4BAA6B,CAF7B,sBAGJ,CACA,+FAEI,uBACJ,CACA,OAEI,QAAS,CADT,SAEJ,CACA,SACI,aACJ,CACA,MAEI,wBAAyB,CADzB,gBAEJ,CACA,MAEI,SACJ,CAMA,KAGI,yCAA6C,CAF7C,eAGJ,CACA,KAQI,iCAAkC,CAGlC,kCAAmC,CACnC,iCAAkC,CAClC,oCAAqC,CAJrC,eAAgB,CARhB,2BAA4B,CAC5B,gDAAkD,CAClD,gBAAiB,CAGjB,iBAAkB,CADlB,eAAgB,CAEhB,gBAAiB,CAHjB,iBAUJ,CAEA,iBAEI,kBAAmB,CADnB,gBAEJ,CAHA,YAEI,kBAAmB,CADnB,gBAEJ,CAEA,GAOI,QAAS,CACT,4BAA6B,CAN7B,aAAc,CAId,UAAW,CAFX,oBAAqB,CACrB,SAAU,CAJV,iBAAkB,CAElB,UAMJ,CAEA,kCAMI,qBACJ,CAEA,SAGI,QAAS,CAFT,QAAS,CACT,SAEJ,CAEA,SACI,eACJ,CAEA,qHAKI,gBACJ,CAEA,MAEI,kBAAmB,CACnB,mBACJ,CAEA,wBAII,aACJ,CAEA,MAEI,cACJ,CAEA,GAEI,iBAAkB,CADlB,iBAEJ,CAEA,MACI,eACJ,CAEA,GAII,aAAc,CAHd,UAAW,CAIX,eAAgB,CAHhB,iBAAkB,CAIlB,gBAAiB,CAHjB,WAIJ,CAEA,GACI,cAAiB,CACjB,eACJ,CAEA,WAGI,mBAAoB,CAFpB,cAAe,CACf,eAEJ,CAEA,iBACI,oBAAqB,CAErB,cAAgB,CADhB,wBAA2B,CAE3B,UACJ,CAEA,wBACI,qBACJ,CAEA,gBACI,eACJ,CACA,kBACI,eACJ,CAEA,EACI,aAAc,CACd,oBACJ,CAEA,kBAUI,iCAAkC,CAFlC,mDAAqD,CACrD,eAAgB,CAEhB,qBAAuB,CAJvB,gBAAiB,CADjB,YAMJ,CAEA,GAEI,gBAAiB,CACjB,eAAgB,CAChB,sBAAwB,CAHxB,eAIJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,gBAAiB,CACjB,eAAgB,CAFhB,mBAGJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,gBAAiB,CACjB,eAAgB,CAFhB,mBAGJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,cAAe,CADf,mBAEJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,cACJ,CAEA,MAJI,mBAOJ,CAHA,GAEI,gBACJ,CCpbA,MAGI,qBAAsB,CACtB,sBAAuB,CACvB,mBAAoB,CACpB,wBAAyB,CACzB,uBAAwB,CACxB,yBAA0B,CAC1B,8BAA+B,CAC/B,sBAAuB,CACvB,oBAAqB,CACrB,wBAAyB,CAYzB,mJAA8J,CAC9J,gCAAmC,CACnC,mCAEJ,CAMA,UAEI,YAAa,CACb,qBAAsB,CACtB,gBAAiB,CAHjB,iBAIJ,CAEA,cACI,WACJ,CAGA,OAEI,yBAA2B,CAD3B,iBAEJ,CAGA,OACI,aAAc,CACd,gBAAiB,CACjB,UACJ,CAMA,aAGI,oCAAqC,CADrC,UAAW,CADX,iBAGJ,CAEA,mBAII,QAAS,CAGT,WAAY,CAFZ,MAAO,CAGP,mBAAiB,CAAjB,gBAAiB,CAPjB,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAIN,UAGJ,CAEA,qBAII,kBAAmB,CAInB,2BAA4B,CAL5B,YAAa,CAGb,qBAAsB,CADtB,+BAAgC,CAJhC,iBAAkB,CAMlB,iBAAkB,CALlB,WAOJ,CAEA,gCAEI,0CAA2C,CAC3C,UAAW,CAFX,gBAGJ,CAEA,kCACI,gBAAiB,CACjB,eACJ,CAEA,6CACI,oBAAqB,CACrB,8BACJ,CAEA,gCAEI,oBAAqB,CADrB,aAEJ,CAEA,mBACI,iBACJ,CAEA,qDACI,sBACJ,CAEA,WACI,aAAc,CACd,aAAc,CACd,gBACJ,CAEA,6CAEI,aAAc,CADd,iBAAkB,CAElB,eACJ,CAEA,YAII,cAAe,CACf,eAAgB,CAHhB,QAAS,CACT,SAAU,CAFV,UAKJ,CAEA,4FACI,oDACJ,CAEA,uBACI,cACJ,CAEA,8CACI,gBACJ,CAEA,yDACI,gBACJ,CAEA,kBACI,oBAAqB,CAGrB,cAAe,CACf,eAAgB,CAChB,eAAgB,CAHhB,eAAgB,CADhB,UAKJ,CAEA,8BACI,mDACJ,CAEA,8GACI,oDACJ,CAEA,8CAGI,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAHhB,eAAgB,CADhB,eAKJ,CAEA,6BACI,eACJ,CAEA,8BACI,iCACJ,CAEA,6BAEI,UAAW,CADX,sBAEJ,CAEA,yDACI,gBACJ,CAEA,2FACI,gBACJ,CAEA,yBACI,gDACI,gCACJ,CACJ,CAEA,yBACI,qBACI,+BACJ,CACJ,CAEA,yBACI,gCACI,gBACJ,CACA,mBACI,QACJ,CACA,WACI,aACJ,CACA,YACI,0BACJ,CACA,kBACI,0BACJ,CAIA,2DACI,yBACJ,CACJ,CAMA,SAII,qBAAsB,CAFtB,gBAAiB,CADjB,WAAY,CAEZ,iBAAkB,CAElB,WACJ,CAEA,wCACI,0CAA2C,CAC3C,UACJ,CAEA,uFAMI,4BAA6B,CAC7B,UAAW,CAHX,MAAO,CAHP,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAGN,YAGJ,CAEA,WACI,oBACJ,CAEA,eAKI,kBAAmB,CAHnB,oBAAgB,CAAhB,eAAgB,CADhB,YAAa,CAGb,wBAAyB,CAEzB,WACJ,CAKA,iDATI,mCAWJ,CAEA,+EACI,YACJ,CAEA,+EACI,iBACJ,CAEA,yBACI,iCAEI,gBAAiB,CADjB,iBAEJ,CACJ,CAEA,oCACI,kCACJ,CAEA,oCACI,mBACJ,CAEA,yBACI,mCACI,iBACJ,CACJ,CAEA,0BACI,WACJ,CAEA,gCACI,kCACJ,CAEA,gCAEI,mBAAoB,CADpB,gBAEJ,CAEA,yBACI,gCACI,SACJ,CAEA,gCAGI,kBAAmB,CADnB,YAAa,CAEb,WAAY,CAHZ,iBAIJ,CAEA,+BAEI,eAAkB,CADlB,gBAAiB,CAGjB,WAAY,CADZ,sBAAuB,CAEvB,aACJ,CAEA,2EAQI,uCAAwC,CADxC,UAAW,CADX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAClB,QAAS,CAET,UAIJ,CAEA,+FAEI,mCACJ,CAEA,qCACI,SACJ,CAEA,kCAEI,eAAkB,CADlB,gBAAiB,CAEjB,6BACJ,CACJ,CAKA,eAEI,kBAAmB,CADnB,YAAa,CAEb,WAAY,CACZ,oBACJ,CAEA,cAMI,aAAc,CALd,aAAc,CACd,mDAAqD,CAErD,gBAAiB,CADjB,eAAgB,CAEhB,qBAAuB,CAEvB,kBACJ,CAEA,uBACI,eACJ,CAEA,yBACI,UACJ,CAEA,kBACI,eACJ,CAMA,cAEI,kBAAmB,CADnB,YAAa,CAGb,eAAgB,CADhB,cAEJ,CAEA,mBAEI,kBAAmB,CADnB,mBAAoB,CAEpB,cAAe,CACf,QAAS,CACT,eAAgB,CAChB,QAAS,CACT,SACJ,CAEA,sBACI,QAAS,CACT,SACJ,CAEA,qBAGI,aAAc,CAFd,oBAAqB,CACrB,eAEJ,CAEA,2BACI,UACJ,CAEA,+BAOI,4BAA6B,CAD7B,iBAAkB,CAHlB,WAAY,CACZ,aAAc,CACd,SAAU,CAJV,iBAAkB,CAOlB,sBAAuB,CANvB,UAOJ,CAEA,mCAEI,WAAY,CADZ,UAEJ,CAEA,yBACI,oDACI,SACJ,CACJ,CAMA,aAWI,qBAAsB,CACtB,iBAAkB,CAClB,oEAA8E,CAN9E,eAAgB,CAChB,SAAU,CAFV,cAAe,CALf,iBAAkB,CAElB,WAAY,CAOZ,eAAgB,CARhB,QAAS,CAYT,8BAAiC,CACjC,oCAAwC,CANxC,iBAAkB,CAJlB,WAAY,CADZ,UAYJ,CAEA,kCAEI,UAAW,CADX,UAEJ,CAEA,+BAII,oBAAgB,CAAhB,eAAgB,CAHhB,YAAa,CAEb,qBAAsB,CADtB,6BAA8B,CAG9B,eAAgB,CAChB,iBACJ,CAEA,+BACI,SAAU,CAEV,uBAAwB,CADxB,kBAEJ,CAEA,gCAGI,aAAc,CAFd,aAAc,CACd,gBAEJ,CAEA,oCACI,aACJ,CAMA,WAEI,kBAAmB,CADnB,YAAa,CAEb,QACJ,CAEA,gBAEI,aAAc,CADd,aAEJ,CAEA,sBACI,UACJ,CAEA,oBAEI,WAAY,CADZ,UAEJ,CAEA,iBAGI,kBAAmB,CAFnB,YAAa,CAGb,QAAS,CAFT,wBAAyB,CAGzB,eAAgB,CAChB,gBACJ,CAEA,iBAGI,kBAAmB,CAFnB,YAAa,CACb,QAEJ,CAEA,cAEI,aAAc,CADd,eAEJ,CAEA,gBAGI,kBAAmB,CAQnB,oCAAqC,CAFrC,kBAAmB,CACnB,UAAW,CATX,mBAAoB,CAOpB,gBAAiB,CAFjB,eAAgB,CADhB,WAAY,CAHZ,sBAAuB,CAKvB,sBAAwB,CAHxB,gBAQJ,CAEA,2BAEI,eAAgB,CADhB,2BAEJ,CAEA,yBACI,iBACI,6BAA8B,CAC9B,QAAS,CACT,UACJ,CACJ,CAMA,WAEI,kBAAmB,CAMnB,4BAA6B,CAC7B,QAAS,CAFT,cAAe,CANf,mBAAoB,CAIpB,WAAY,CAFZ,sBAAuB,CAOvB,YAAa,CAJb,SAAU,CAFV,UAOJ,CAEA,iBACI,UACJ,CAEA,0BACI,gBACJ,CAEA,4BACI,iBACJ,CAEA,yBACI,4BACI,YACJ,CACJ,CAEA,yBACI,0BACI,YACJ,CACJ,CAMA,WAUI,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAFhB,4BAA6B,CAC7B,QAAS,CAFT,cAAe,CALf,YAAa,CAEb,WAAY,CAEZ,iBAAkB,CADlB,SAAU,CAJV,iBAAkB,CAElB,UAQJ,CAEA,mCAOI,sCAAuC,CADvC,UAAW,CADX,UAAW,CAFX,QAAS,CADT,iBAAkB,CAMlB,uDAAiE,CAJjE,UAKJ,CAEA,yDAEI,qBACJ,CAEA,kBACI,QACJ,CAEA,iBACI,WACJ,CAEA,gCACI,QAAS,CACT,uBACJ,CAEA,+BACI,WAAY,CACZ,wBACJ,CAOA,yBACI,SACI,WACJ,CAEA,wBAGI,QAAS,CADT,yBAA0B,CAD1B,gCAGJ,CAEA,wBAII,kBAAmB,CAHnB,YAAa,CAEb,mBAAoB,CADpB,mCAAoC,CAGpC,WACJ,CAEA,uBACI,gBACJ,CAEA,mCACI,gBACJ,CAEA,oBACI,aACJ,CAEA,iDAGI,sBAAuB,CAEvB,SAAU,CAHV,cAAe,CAEf,iBAEJ,CAEA,uBACI,QAAS,CAET,uBAAwB,CADxB,eAEJ,CAEA,cAEI,kBAAmB,CADnB,QAAS,CAET,eACJ,CAEA,gBACI,gBAAiB,CACjB,eAAgB,CAChB,mBACJ,CAEA,iBACI,SAAU,CACV,0BACJ,CAEA,4CACI,SAAU,CACV,yBACJ,CAEA,yBAEI,gBAAiB,CAEjB,SAAU,CADV,mBAAoB,CAEpB,yBAA0B,CAJ1B,UAKJ,CAEA,uBAMI,gCAAiC,CAFjC,WAAY,CAFZ,OAAQ,CAGR,iBAAkB,CAJlB,cAAe,CAEf,eAIJ,CAEA,mFAEI,0CACJ,CAEA,6EAII,SAAU,CAFV,eAAgB,CAChB,kBAEJ,CAEA,4BACI,YAAa,CACb,qBACJ,CAEA,+BACI,SAAU,CAEV,uBAAwB,CADxB,oCAEJ,CAEA,wCAQI,kBAAmB,CAEnB,qBAAsB,CAPtB,QAAS,CAET,mBAAoB,CACpB,qBAAsB,CACtB,QAAS,CAHT,MAAO,CAKP,yCAA4C,CAR5C,uBAAgB,CAAhB,eAAgB,CAChB,OASJ,CAEA,0DACI,SAAU,CAGV,uBAAwB,CAFxB,oCAAwC,CACxC,oBAEJ,CAEA,qCACI,oBACJ,CACJ,CAMA,WAEI,YAAa,CACb,iBAAkB,CAClB,mCAAqC,CACrC,6BAA+B,CAJ/B,iBAKJ,CAEA,+CACI,gBACJ,CAEA,yBACI,WACI,6BACJ,CACJ,CAEA,yBACI,WAEI,aAAc,CADd,yBAEJ,CACJ,CAEA,WAKI,qBAAsB,CAFtB,YAAa,CACb,qBAAsB,CAFtB,kBAAmB,CADnB,iBAAkB,CAKlB,qBACJ,CAEA,sBAGI,aAAc,CACd,kBAAmB,CAFnB,eAAgB,CADhB,iBAIJ,CAEA,4BACI,UAAW,CACX,aAAc,CACd,kBACJ,CAEA,4DAEI,iCAA0B,CAA1B,yBAA0B,CAD1B,+BAEJ,CAEA,uEAEI,OAAQ,CACR,gBAAiB,CAFjB,iBAGJ,CAEA,6FACI,gBACJ,CAEA,iBAKI,yDAA0D,CAD1D,WAAY,CAFZ,OAAQ,CAIR,mBAAiB,CAAjB,gBAAiB,CALjB,iBAAkB,CAElB,UAIJ,CAEA,kFACI,eACJ,CAEA,kBAKI,kBAAmB,CAKnB,UAAW,CANX,YAAa,CAIb,gBAAiB,CACjB,eAAgB,CAFhB,OAAQ,CALR,OAAQ,CAIR,sBAAuB,CALvB,iBAAkB,CAElB,UAQJ,CAEA,wBAGI,2BAA4B,CAD5B,aAAc,CADd,iBAGJ,CAEA,8BACI,oBACJ,CAEA,6CACI,SACJ,CAEA,4BACI,YACJ,CAEA,gBAEI,kBAAmB,CAGnB,iCAAkC,CAJlC,YAAa,CAKb,gBAAiB,CACjB,eAAgB,CAJhB,QAAS,CAKT,sBAAwB,CACxB,aAAc,CALd,eAMJ,CAEA,oBAGI,kBAAmB,CAGnB,+BAAgC,CAJhC,YAAa,CAEb,OAAQ,CACR,iBAAkB,CAJlB,iBAMJ,CAEA,wBAEI,MAAO,CADP,iBAEJ,CAEA,iBAEI,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAHhB,QAIJ,CAEA,+CACI,UACJ,CAEA,2BACI,YACJ,CAEA,iGACI,oDAAsD,CACtD,sBACJ,CAEA,qBAEI,gBAAiB,CADjB,eAEJ,CAEA,mBAEI,YAAa,CACb,qBAAsB,CAFtB,WAGJ,CAEA,mBAOI,oBAAqB,CACrB,2BAA4B,CAP5B,mBAAoB,CAIpB,gBAAiB,CACjB,eAAgB,CAHhB,eAAgB,CAChB,eAAgB,CAFhB,iBAAkB,CAOlB,qBACJ,CAEA,2FACI,gDACJ,CAEA,sGACI,oBACJ,CAEA,uDACI,cACJ,CAEA,wEACI,aAAc,CACd,eACJ,CAEA,gBAOI,iCAAkC,CADlC,gBAAiB,CAFjB,eAAgB,CAChB,SAGJ,CAEA,kCARI,kBAAmB,CADnB,YAAa,CAEb,OAWJ,CAEA,sCAII,4CAA6C,CAC7C,iBAAkB,CAFlB,UAAW,CADX,UAAW,CADX,SAKJ,CAEA,qBACI,YACJ,CAEA,sBAII,eAAqD,CACrD,kBAAmB,CAJnB,aAAc,CAEd,WAAY,CAIZ,mBAAiB,CAAjB,gBAAiB,CALjB,UAMJ,CAEA,aACI,YAAa,CAGb,eAAgB,CAFhB,gBAAiB,CACjB,SAEJ,CAEA,kBAEI,aAAc,CACd,QAAS,CACT,SAAU,CAHV,iBAIJ,CAWA,0BACI,iBAGI,cAAe,CAEf,YAAa,CAHb,YAAa,CADb,kBAAmB,CAGnB,mCAEJ,CAEA,kDACI,YACJ,CAEA,uCAEI,kBAAmB,CACnB,eAAgB,CAFhB,iBAGJ,CAEA,oCACI,kBACJ,CAEA,6CACI,kBACJ,CAEA,kCAGI,WAAY,CAFZ,iBAAkB,CAClB,UAEJ,CAEA,iCACI,kBACJ,CAEA,kCAEI,gBAAiB,CACjB,gBAAiB,CAFjB,YAGJ,CAEA,oCACI,eACJ,CAEA,gBACI,kBACJ,CAEA,sCACI,kBACJ,CAEA,gCACI,kBACJ,CAEA,iCACI,gBAAiB,CACjB,eACJ,CAEA,mCAEI,gBAAiB,CADjB,eAEJ,CAEA,gDACI,aACJ,CAEA,mEACI,QACJ,CAEA,mBACI,kBACJ,CAEA,oCACI,cACJ,CACJ,CAKA,YAGI,kBAAmB,CAFnB,YAAa,CACb,kCAAmC,CAEnC,gBACJ,CAEA,oCACI,YACJ,CAEA,cACI,gBAAiB,CACjB,eACJ,CAEA,yBAEI,iCAAkC,CADlC,mBAEJ,CAEA,yBACI,mBAAoB,CACpB,gBACJ,CAEA,yBACI,yBACI,YACJ,CACJ,CAMA,SACI,qBACJ,CAEA,wBACI,yCACJ,CAEA,+BACI,6BACJ,CAEA,+BACI,4CACJ,CAEA,aAEI,gBAAiB,CADjB,kBAEJ,CAEA,eACI,iCACJ,CAEA,eAKI,2BAA4B,CAH5B,kCAAqC,CACrC,eAAgB,CAChB,gBAAiB,CAHjB,eAKJ,CAEA,+FACI,oDACJ,CAEA,iBAKI,2BAA4B,CAF5B,cAAe,CACf,gBAAiB,CAHjB,eAAgB,CAChB,eAIJ,CAEA,0BACI,+BAAkC,CAElC,4BAA8B,CAD9B,UAEJ,CAEA,2BACI,+BACJ,CAEA,4BACI,+BACJ,CAEA,8BACI,aAAc,CACd,gBAAiB,CACjB,iBAAkB,CAClB,UACJ,CAEA,yBACI,iBAEI,gBAAiB,CADjB,eAEJ,CACJ,CAWA,0CAEI,qBAAuB,CACvB,4DAA+D,CAC/D,uFAA2F,CAC3F,6BAA+B,CAE/B,YAAa,CACb,yJAMJ,CAEA,aACI,+BACJ,CAEA,oCAEI,+BACJ,CAEA,eACI,+BACJ,CAEA,mBACI,UACJ,CAWA,gBAEI,eAAgB,CADhB,4BAEJ,CAGA,gGACI,YACJ,CAGA,iBAEI,2BAA4B,CAD5B,QAEJ,CAEA,iGACI,oDACJ,CAIA,mCACI,cACJ,CAGA,mBACI,2BACJ,CAGA,sCAGI,4BAA8B,CAD9B,iBAEJ,CACA,0CAEI,sCACJ,CAGA,cACI,+BAAgC,CAChC,yBAA0B,CAC1B,qBACJ,CAEA,+FAKI,iDAAmD,CAEnD,cAAe,CADf,eAAgB,CAEhB,iBACJ,CAEA,kBACE,aACF,CAEA,uEACI,2BACJ,CAEA,2DACI,8BACJ,CAEA,mMAII,6BAA8B,CAE9B,gBAAiB,CADjB,eAAgB,CAEhB,iBACJ,CAEA,sLAGI,gBAAiB,CACjB,iBACJ,CAEA,+CACI,gBAAiB,CACjB,iBACJ,CAEA,oDACI,cACJ,CAEA,65BASI,gDACJ,CAEA,2QAOI,kBACJ,CAEA,oCAEI,iBAAkB,CAClB,SAAU,CAFV,iBAGJ,CAEA,2CAOI,oCAAqC,CAFrC,QAAS,CAJT,UAAW,CAEX,WAAY,CADZ,iBAAkB,CAElB,KAAM,CAEN,WAEJ,CAEA,2BAQI,kBAAmB,CALnB,wBAAyB,CAMzB,mBAAqB,CAFrB,aAAc,CAFd,cAAgB,CADhB,yBAA2B,CAE3B,eAAgB,CAJhB,kBAA4B,CAD5B,qBASJ,CAEA,gBAMI,gCAAiC,CACjC,iBAAkB,CAClB,+DAAiE,CALjE,uBAAwB,CACxB,gBAAiB,CACjB,iBAAkB,CAJlB,aAAc,CACd,iBAOJ,CAEA,qBACI,2BACJ,CAEA,wBACI,2BACJ,CAEA,yBACI,kSASI,gBACJ,CAEA,sLAGI,gBACJ,CAEA,2CACI,sBACJ,CACJ,CAgBA,kFACI,gBACJ,CAGA,eAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,UACJ,CAGA,mBACI,WACJ,CAEA,2CAEI,cACJ,CAEA,wHACI,oDACJ,CAEA,sCACI,yBACJ,CAEA,mBAEI,0BAA2B,CAD3B,6BAEJ,CAEA,2FACI,gDACJ,CAEA,sCACI,gCACJ,CAEA,wDACI,aACJ,CAEA,yHACI,oDACJ,CAIA,WAGI,oBAAsB,CACtB,gBAAiB,CACjB,iBAAkB,CAJlB,uBAAwB,CACxB,iBAIJ,CACA,kBACI,oBACJ,CACA,aACI,yBACJ,CAIA,uBAII,6BACJ,CAEA,yCACI,gBACJ,CAMA,kCACI,YAAa,CACb,6BACJ,CAEA,sCACI,+BACJ,CACA,sCACI,gBACJ,CAEA,6CACI,+BACJ,CAEA,kBACI,gBACJ,CAIA,eACI,kBACJ,CAEA,WACI,gBACJ,CAEA,aACI,QACJ,CAEA,kBAII,yBAA2B,CAH3B,gBAAiB,CACjB,eAAiB,CACjB,8BAEJ,CAGA,mCAWI,gCAAiC,CACjC,sJAA8L,CAC9L,mCAAqC,CAErC,2BAA4B,CAD5B,mCAAqC,CARrC,wBAAyB,CADzB,gBAAiB,CAJjB,oBAAqB,CAMrB,4BAA6B,CAC7B,gBAAiB,CALjB,cAAe,CADf,eAAgB,CAQhB,kBAAmB,CADnB,kBAAmB,CALnB,UAYJ,CAEA,kDACI,kEAAgG,CAEhG,2BAA4B,CAD5B,yBAEJ,CAEA,iDACI,mEAA+F,CAC/F,0BAA2B,CAE3B,2BAA4B,CAD5B,yBAEJ,CAEA,sCAOI,wBAAqD,CANrD,2BAA4B,CAC5B,gBAAiB,CACjB,eAAgB,CAChB,mBAAqB,CACrB,eAAgB,CAChB,wBAEJ,CAEA,4EAGI,wBAA4D,CAD5D,gBAEJ,CAMA,gBACI,YAAa,CACb,6BAA8B,CAC9B,4BACJ,CAEA,wBAGI,kBAAmB,CADnB,YAAa,CADb,WAGJ,CAEA,qCACI,0BAA2B,CAC3B,kBACJ,CAEA,qBACI,iCAAkC,CAClC,gBAAiB,CACjB,iBACJ,CAEA,kCAEI,gBAAiB,CACjB,eAAgB,CAChB,gBAAiB,CAHjB,cAIJ,CAEA,2BACI,oBAAqB,CACrB,YACJ,CAEA,eAQI,oCAAqC,CAFrC,qBAAsB,CACtB,iBAAkB,CANlB,aAAc,CAId,yBAA2B,CAF3B,aAAc,CADd,eAAgB,CAEhB,wBAKJ,CAEA,8BACI,eACJ,CAEA,yBACI,wBAEI,sBAAuB,CADvB,qBAAsB,CAEtB,QACJ,CAEA,qCACI,kBACJ,CACA,kCACI,iBACJ,CACJ,CAMA,YACI,iBAAkB,CAClB,iBACJ,CAEA,kBAEI,kCAAqC,CACrC,eAAgB,CAFhB,4BAGJ,CAEA,kGACI,oDACJ,CAEA,mBAGI,kBAAmB,CAOnB,eAAgB,CAChB,oCAAqC,CACrC,iBAAkB,CAHlB,iCAAkC,CAPlC,mBAAoB,CAMpB,gBAAiB,CAJjB,6BAA8B,CAE9B,eAAgB,CAChB,wBAAyB,CANzB,iBAAkB,CAYlB,2BAA6B,CAR7B,UASJ,CAEA,yBACI,oBACJ,CAEA,wBAOI,oCAAqC,CACrC,iBAAkB,CALlB,UAAW,CAFX,oBAAqB,CAGrB,gBAAiB,CACjB,eAAgB,CAChB,sBAAwB,CAJxB,gBAOJ,CAMA,gBACI,kBACJ,CAEA,4BACI,2BACJ,CAEA,WAGI,cAAe,CAFf,YAAa,CACb,mCAEJ,CAEA,2BACI,YACJ,CAEA,0BACI,WACI,mCACJ,CACA,mCACI,YACJ,CACJ,CAEA,yBACI,WACI,mCACJ,CACA,mCACI,YACJ,CACJ,CAIA,UAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,kBACJ,CAGA,eAEI,oBAAqB,CADrB,YAAa,CAEb,6BAA8B,CAC9B,kBAAmB,CAEnB,eAAgB,CADhB,UAEJ,CAEA,aAII,gBAAiB,CADjB,eAAgB,CADhB,eAAgB,CADhB,UAIJ,CAEA,yBACI,0BAA2B,CAC3B,eAAgB,CAChB,kBACJ,CAEA,+BAEI,eAAgB,CADhB,UAEJ,CAMA,oBAMI,eAAqD,CACrD,iBAAkB,CANlB,aAAc,CAEd,WAAY,CAEZ,eAAgB,CADhB,mBAAiB,CAAjB,gBAAiB,CAFjB,UAMJ,CAEA,uBACI,eACJ,CAEA,yBACI,eACJ,CAEA,qBACI,YAAa,CACb,QACJ,CAEA,4BAEI,iCAAkC,CADlC,gBAEJ,CAEA,kCACI,2BACJ,CAEA,gCAEI,WAAY,CADZ,UAEJ,CAEA,0BACI,gEACI,kBAAmB,CACnB,eACJ,CACJ,CAMA,qDACI,oBAAuB,CACvB,OACJ,CAEA,kDACI,OACJ,CAEA,0BACI,6DACI,kBAAmB,CACnB,eACJ,CACJ,CAKA,eACI,oBACJ,CAEA,eACI,mBAAoB,CACpB,iBACJ,CAEA,YAEI,cAAe,CAEf,mBAAoB,CADpB,eAAgB,CAFhB,QAIJ,CAEA,mBAEI,iCAAkC,CAClC,gBAAiB,CAGjB,eAAgB,CADhB,sBAAwB,CADxB,iBAAkB,CAHlB,QAMJ,CAEA,YACI,oBAAqB,CACrB,cACJ,CAEA,yBACI,0BAGI,kBAAmB,CAFnB,eAAgB,CAChB,gBAEJ,CACJ,CAEA,yBACI,eACI,gBACJ,CACA,YACI,iBACJ,CACA,eACI,mBACJ,CACA,mBAEI,gBAAiB,CADjB,cAEJ,CACJ,CAEA,yBACI,eACI,gBACJ,CACA,eACI,mBACJ,CACJ,CAMA,aAMI,kBAAmD,CADnD,UAAW,CAHX,2BAA+B,CAE/B,oBAAqB,CADrB,gBAAiB,CAFjB,iBAMJ,CAEA,4BACI,YACJ,CAEA,oBAEI,aAAc,CAEd,wBAA4B,CAH5B,YAAa,CAIb,gBAAiB,CAFjB,mCAGJ,CAEA,0BACI,UAAW,CAEX,eAAgB,CADhB,sBAEJ,CAEA,eACI,wBACJ,CAEA,qBACI,UAA0B,CAC1B,oBACJ,CAEA,oBACI,YAAa,CAEb,cAAe,CADf,sBAAuB,CAIvB,eAAgB,CAFhB,eAAgB,CAChB,SAEJ,CAEA,oBAEI,kBAAmB,CADnB,mBAAoB,CAIpB,eAAgB,CADhB,QAAS,CADT,SAGJ,CAEA,mBAGI,kBAAmB,CADnB,mBAAoB,CAEpB,gBAAiB,CAHjB,iBAIJ,CAEA,+CAMI,eAAgB,CAChB,kBAAmB,CANnB,UAAW,CACX,aAAc,CAEd,UAAW,CACX,iBAAkB,CAFlB,SAKJ,CAEA,yBACI,oBAGI,UAAW,CADX,yBAA0B,CAD1B,eAAgB,CAGhB,iBACJ,CACA,kDAEI,UAAW,CACX,gBACJ,CACA,wBACI,kBACJ,CACJ,CAMA,oBAEI,gCAAiC,CADjC,yBAEJ,CAEA,mBACI,UACJ,CAEA,mBACI,kBACJ,CAEA,4BACI,UACJ,CAEA,6CACI,gCAAiC,CACjC,UACJ,CAEA,iEAEI,qBACJ,CAEA,oCACI,UACJ,CAEA,kCACI,SACJ,CAEA,gCACI,gCACJ,CAEA,iHACI,aACJ,CAMA,mEACI,UACJ,CAEA,kCACI,iCACJ,CAcA,oJACI,UACJ,CAEA,gCACI,iCACJ,CAEA,gCACI,wBACJ,CAEA,8BAEI,wBAAyD,CADzD,kCAEJ,CAEA,qCACI,SACJ,CAEA,0CACI,0BACJ,CAEA,mDACI,UACJ,CAEA,+CACI,yBACJ,CAEA,gCACI,wBACJ,CAEA,+BACI,kBACJ,CAEA,0CACI,kBAAmD,CACnD,oBAAqD,CACrD,uBACJ,CAEA,qCACI,UACJ,CAEA,gCAEI,eAAgB,CADhB,UAEJ,CAEA,kBACI,wBACJ,CAEA,oCACI,kBAAmD,CACnD,0CACJ,CAEA,0BACI,wBACJ,CAEA,iEACI,0FACJ,CAEA,gEACI,yFACJ,CAEA,qDAEI,wBAAyD,CADzD,yBAEJ,CAEA,0GAEI,wBACJ,CAEA,+CACI,aACJ,CAEA,8CAEI,gCAAiC,CADjC,wBAEJ,CAEA,6CACI,kBACJ,CAEA,8CACI,kBACJ,CAEA,mHAEI,UACJ,CAEA,wHAEI,0CAA4C,CAC5C,oBACJ,CAEA,iCACI,UACJ,CAEA,yBACI,6HAEI,gCACJ,CACJ,CAEA,mCACI,qBAEI,gCAAiC,CADjC,yBAEJ,CAEA,oBACI,UACJ,CAEA,oBACI,kBACJ,CAEA,6BACI,UACJ,CAEA,8CACI,gCAAiC,CACjC,UACJ,CAEA,mEAEI,qBACJ,CAEA,qCACI,UACJ,CAEA,mCACI,SACJ,CAEA,iCACI,gCACJ,CAEA,kHACI,aACJ,CAMA,qEACI,UACJ,CAEA,mCACI,iCACJ,CAcA,wJACI,UACJ,CAEA,iCACI,iCACJ,CAEA,iCACI,wBACJ,CAEA,+BAEI,wBAAyD,CADzD,kCAEJ,CAEA,sCACI,SACJ,CAEA,2CACI,0BACJ,CAEA,oDACI,UACJ,CAEA,gDACI,yBACJ,CAEA,iCACI,wBACJ,CAEA,gCACI,kBACJ,CAEA,2CACI,kBAAmD,CACnD,oBAAqD,CACrD,uBACJ,CAEA,sCACI,UACJ,CAEA,iCAEI,eAAgB,CADhB,UAEJ,CAEA,mBACI,wBACJ,CAEA,qCACI,kBAAmD,CACnD,0CACJ,CAEA,2BACI,wBACJ,CAEA,kEACI,0FACJ,CAEA,iEACI,yFACJ,CAEA,sDAEI,wBAAyD,CADzD,yBAEJ,CAEA,4GAEI,wBACJ,CAEA,gDACI,aACJ,CAEA,+CAEI,gCAAiC,CADjC,wBAEJ,CAEA,8CACI,kBACJ,CAEA,+CACI,kBACJ,CAEA,qHAEI,UACJ,CAEA,0HAEI,0CAA4C,CAC5C,oBACJ,CAEA,kCACI,UACJ,CAEA,yBACI,+HAEI,gCACJ,CACJ,CACJ,CAMA,MAaI,6BAA8B,CAD9B,kCAA2B,CAA3B,0BAA2B,CAP3B,YAAa,CAEb,WAAY,CAJZ,MAAO,CAQP,YAAa,CAHb,eAAgB,CAPhB,iBAAkB,CAClB,KAAM,CAQN,iBAAkB,CAJlB,UAAW,CAFX,eAUJ,CAEA,UACI,cACJ,CAEA,uBACI,YAAc,CACd,iDAAuD,CACvD,mBACJ,CAEA,YACI,aACJ,CAEA,+BACI,cACJ,CAEA,4BACI,mBAAY,CAAZ,WACJ,CAEA,2BACI,uBAAgB,CAAhB,eACJ,CAEA,UAUI,kCAA2B,CAA3B,0BAA2B,CAJ3B,gCAAqC,CACrC,SAAU,CAEV,uBAAwB,CADxB,iDAAuD,CAGvD,mBACJ,CAEA,6BATI,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAiBJ,CAPA,mBAMI,eACJ,CAEA,kCAQI,kCAA2B,CAA3B,0BAA2B,CAH3B,QAAS,CACT,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAIN,iBAEJ,CAEA,4BAGI,uCAAwC,CACxC,0BAA2B,CAF3B,wBAAiB,CAAjB,qBAAiB,CAAjB,gBAGJ,CAEA,iBACI,iBAAkB,CAGlB,yBAA0B,CAD1B,mDAAyD,CADzD,UAGJ,CAEA,iEAEI,eACJ,CAEA,YAII,QAAS,CAET,eAAgB,CAHhB,OAIJ,CAEA,uBAJI,MAAO,CAJP,iBAAkB,CAClB,KAaJ,CANA,WAKI,WAAY,CADZ,UAEJ,CAEA,wBACI,kCAA2B,CAA3B,0BACJ,CAEA,+BACI,eACJ,CAEA,qBAII,qBAAuB,CAFvB,MAAO,CADP,KAAM,CAEN,oBAEJ,CAEA,iBAQI,iCAAkC,CAFlC,cAAe,CAHf,MAAO,CAIP,gBAAiB,CAFjB,eAAgB,CAJhB,iBAAkB,CAQlB,iBAAkB,CAPlB,OAAQ,CAER,UAMJ,CAEA,mBACI,iCAAkC,CAClC,yBACJ,CAEA,cASI,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAEhB,eAAgB,CAChB,QAAS,CACT,eAAgB,CAHhB,cAAe,CARf,aAAc,CACd,WAAY,CAEZ,WAAY,CAEZ,QAAS,CACT,gBAAiB,CAFjB,SAAU,CALV,iBAAkB,CAalB,sBAAwB,CAVxB,UAWJ,CAEA,wCAEI,SACJ,CAEA,qBAEI,UAAY,CADZ,YAEJ,CAEA,gCAEI,QAAS,CADT,SAEJ,CAEA,2CACI,SACJ,CAEA,mFAKI,wDAA2D,CAC3D,0BAA2B,CAF3B,WAAY,CADZ,UAIJ,CAEA,sIACI,oHAGI,gDACJ,CAEA,6EAEI,eACJ,CACJ,CAEA,qBACI,2BACJ,CAEA,qBACI,+BACJ,CAEA,kBACI,YACJ,CAEA,qCACI,aACJ,CAEA,4BACI,2BACJ,CAEA,oBAEI,2BAA4B,CAD5B,YAEJ,CAEA,wCACI,aACJ,CAEA,qCACI,4BACJ,CAEA,iFAEI,iBACJ,CAEA,uDAOI,eAAgB,CAFhB,YAAa,CACb,gBAAiB,CAJjB,iBAAkB,CAClB,OAAQ,CACR,UAIJ,CAEA,2BACI,MACJ,CAEA,4BACI,OACJ,CAEA,qEAMI,UAAW,CADX,WAAY,CAHZ,iBAAkB,CAClB,QAAS,CACT,UAGJ,CAEA,kCAEI,gCAAiC,CADjC,QAEJ,CAEA,mCAEI,+BAAgC,CADhC,SAEJ,CAEA,eASI,UAAW,CAHX,cAAe,CACf,eAAgB,CAHhB,WAAY,CADZ,MAAO,CAKP,gBAAiB,CAHjB,cAAe,CAJf,iBAAkB,CAClB,KAAM,CAQN,wBAAiB,CAAjB,qBAAiB,CAAjB,gBACJ,CAEA,eAEI,QAAS,CACT,MAAO,CAEP,eAAgB,CAJhB,iBAAkB,CAGlB,UAEJ,CAEA,uBAMI,UAAW,CAFX,cAAe,CACf,eAAgB,CAFhB,aAAc,CAFd,eAAgB,CAChB,sBAAuB,CAKvB,iBACJ,CAEA,2CAEI,cAAe,CACf,eAAgB,CAFhB,iBAAkB,CAGlB,wBACJ,CAEA,8EAEI,gBAAiB,CADjB,aAEJ,CAEA,sBACI,YACJ,CAEA,qBACI,iBACJ,CAEA,iBASI,aAAc,CAJd,WAAY,CAFZ,QAAS,CAGT,iBAAkB,CAClB,SAAU,CANV,iBAAkB,CAClB,KAAM,CAMN,gCAAkC,CAJlC,UAAW,CAMX,mBACJ,CAEA,sBAEI,WAAY,CACZ,WAAY,CAFZ,UAGJ,CAEA,yBACI,SACJ,CAEA,+CACI,qDACJ,CAEA,8CACI,SACJ,CAEA,oEACI,+CAA0C,CAA1C,uCACJ,CAEA,sEACI,mEAAqE,CAArE,2DACJ,CAEA,2CAOI,eAAgB,CAFhB,WAAY,CAFZ,SAAU,CAGV,QAAS,CAET,WAAa,CAPb,iBAAkB,CAClB,QAAS,CAET,UAKJ,CAEA,2CAGI,WAAY,CACZ,eAAgB,CAHhB,iBAAkB,CAClB,SAGJ,CAEA,6CAQI,eAAgB,CAGhB,8CAA8B,CAC9B,iBAAkB,CADlB,kBAA8B,CAA9B,gBAA8B,CAP9B,qBAAsB,CAEtB,WAAY,CAHZ,MAAO,CAIP,QAAS,CANT,iBAAkB,CAClB,KAAM,CAGN,UAQJ,CAEA,qCACI,iBAII,WAAY,CADZ,SAAU,CAEV,QAAS,CAJT,iBAAkB,CAClB,QAIJ,CACJ,CAEA,6BACI,GACI,sBACJ,CAEA,GACI,uBACJ,CACJ,CARA,qBACI,GACI,sBACJ,CAEA,GACI,uBACJ,CACJ,CAEA,gCACI,GACI,mBACJ,CAEA,IACI,yBACJ,CAEA,GACI,mBACJ,CACJ,CAZA,wBACI,GACI,mBACJ,CAEA,IACI,yBACJ,CAEA,GACI,mBACJ,CACJ,CAEA,UAII,2BAA4B,CAD5B,SAAU,CADV,kBAAmB,CADnB,YAIJ,CAEA,eAKI,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAEJ,CAEA,uHAII,kCAA2B,CAA3B,0BAA2B,CAC3B,iDAAuD,CACvD,mBACJ,CAEA,yFAEI,kBACJ,CAMA,sHAEI,SACJ,CAEA,6JAII,YACJ,CAEA,qIAGI,YACJ,CAEA,yBACI,sBACJ,CAEA,oCACI,eACJ", "file": "screen.css", "sourcesContent": ["/* Reset\n/* ---------------------------------------------------------- */\n\nhtml,\nbody,\ndiv,\nspan,\napplet,\nobject,\niframe,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\np,\nblockquote,\npre,\na,\nabbr,\nacronym,\naddress,\nbig,\ncite,\ncode,\ndel,\ndfn,\nem,\nimg,\nins,\nkbd,\nq,\ns,\nsamp,\nsmall,\nstrike,\nstrong,\nsub,\nsup,\ntt,\nvar,\ndl,\ndt,\ndd,\nol,\nul,\nli,\nfieldset,\nform,\nlabel,\nlegend,\ntable,\ncaption,\ntbody,\ntfoot,\nthead,\ntr,\nth,\ntd,\narticle,\naside,\ncanvas,\ndetails,\nembed,\nfigure,\nfigcaption,\nfooter,\nheader,\nhgroup,\nmenu,\nnav,\noutput,\nruby,\nsection,\nsummary,\ntime,\nmark,\naudio,\nvideo {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    font: inherit;\n    font-size: 100%;\n    vertical-align: baseline;\n}\nbody {\n    line-height: 1;\n}\nblockquote,\nq {\n    quotes: none;\n}\nblockquote:before,\nblockquote:after,\nq:before,\nq:after {\n    content: \"\";\n    content: none;\n}\ntable {\n    border-spacing: 0;\n    border-collapse: collapse;\n}\nimg {\n    display: block;\n    max-width: 100%;\n    height: auto;\n}\nhtml {\n    box-sizing: border-box;\n    font-family: sans-serif;\n\n    -ms-text-size-adjust: 100%;\n    -webkit-text-size-adjust: 100%;\n}\n*,\n*:before,\n*:after {\n    box-sizing: inherit;\n}\na {\n    background-color: transparent;\n}\na:active,\na:hover {\n    outline: 0;\n}\nb,\nstrong {\n    font-weight: bold;\n}\ni,\nem,\ndfn {\n    font-style: italic;\n}\nh1 {\n    margin: 0.67em 0;\n    font-size: 2em;\n}\nsmall {\n    font-size: 80%;\n}\nsub,\nsup {\n    position: relative;\n    font-size: 75%;\n    line-height: 0;\n    vertical-align: baseline;\n}\nsup {\n    top: -0.5em;\n}\nsub {\n    bottom: -0.25em;\n}\nimg {\n    border: 0;\n}\nsvg:not(:root) {\n    overflow: hidden;\n}\nmark {\n    background-color: #fdffb6;\n}\ncode,\nkbd,\npre,\nsamp {\n    font-family: monospace, monospace;\n    font-size: 1em;\n}\nkbd {\n    padding: 3px 5px;\n    font-family: var(--font-mono);\n    font-size: 1.5rem;\n    background: #f6f8fa;\n    border: 1px solid rgba(124, 139, 154, 0.25);\n    border-radius: 6px;\n    box-shadow: inset 0 -1px 0 rgba(124, 139, 154, 0.25);\n}\n@media (max-width: 600px) {\n    kbd {\n        font-size: 1.3rem;\n    }\n}\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n    margin: 0; /* 3 */\n    color: inherit; /* 1 */\n    font: inherit; /* 2 */\n}\nbutton {\n    overflow: visible;\n    border: none;\n}\nbutton,\nselect {\n    text-transform: none;\n}\nbutton,\nhtml input[type=\"button\"],\n/* 1 */\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n    cursor: pointer; /* 3 */\n\n    -webkit-appearance: button; /* 2 */\n}\nbutton[disabled],\nhtml input[disabled] {\n    cursor: default;\n}\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n    padding: 0;\n    border: 0;\n}\ninput {\n    line-height: normal;\n}\ninput:focus {\n    outline: none;\n}\ninput[type=\"checkbox\"],\ninput[type=\"radio\"] {\n    box-sizing: border-box; /* 1 */\n    padding: 0; /* 2 */\n}\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n    height: auto;\n}\ninput[type=\"search\"] {\n    box-sizing: content-box; /* 2 */\n\n    -webkit-appearance: textfield; /* 1 */\n}\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n    -webkit-appearance: none;\n}\nlegend {\n    padding: 0; /* 2 */\n    border: 0; /* 1 */\n}\ntextarea {\n    overflow: auto;\n}\ntable {\n    border-spacing: 0;\n    border-collapse: collapse;\n}\ntd,\nth {\n    padding: 0;\n}\n\n/* ==========================================================================\n   Base styles: opinionated defaults\n   ========================================================================== */\n\nhtml {\n    font-size: 62.5%;\n\n    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\nbody {\n    color: var(--color-darkgrey);\n    font-family: var(--gh-font-body, var(--font-sans));\n    font-size: 1.6rem;\n    line-height: 1.6em;\n    font-weight: 400;\n    font-style: normal;\n    letter-spacing: 0;\n    text-rendering: optimizeLegibility;\n    background: #fff;\n\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    -moz-font-feature-settings: \"liga\" on;\n}\n\n::selection {\n    text-shadow: none;\n    background: #daf2fd;\n}\n\nhr {\n    position: relative;\n    display: block;\n    width: 100%;\n    margin: 2.5em 0 3.5em;\n    padding: 0;\n    height: 1px;\n    border: 0;\n    border-top: 1px solid #f0f0f0;\n}\n\naudio,\ncanvas,\niframe,\nimg,\nsvg,\nvideo {\n    vertical-align: middle;\n}\n\nfieldset {\n    margin: 0;\n    padding: 0;\n    border: 0;\n}\n\ntextarea {\n    resize: vertical;\n}\n\n::not(.gh-content) p,\n::not(.gh-content) ul,\n::not(.gh-content) ol,\n::not(.gh-content) dl,\n::not(.gh-content) blockquote {\n    margin: 0 0 1.5em 0;\n}\n\nol,\nul {\n    padding-left: 1.3em;\n    padding-right: 1.5em;\n}\n\nol ol,\nul ul,\nul ol,\nol ul {\n    margin: 0.5em 0;\n}\n\nul,\nol {\n    max-width: 100%;\n}\n\nli {\n    padding-left: 0.3em;\n    line-height: 1.6em;\n}\n\nli + li {\n    margin-top: 0.5em;\n}\n\ndt {\n    float: left;\n    margin: 0 20px 0 0;\n    width: 120px;\n    color: #daf2fd;\n    font-weight: 500;\n    text-align: right;\n}\n\ndd {\n    margin: 0 0 5px 0;\n    text-align: left;\n}\n\nblockquote {\n    margin: 1.5em 0;\n    padding: 0 1.6em 0 1.6em;\n    border-left: #daf2fd;\n}\n\nblockquote small {\n    display: inline-block;\n    margin: 0.8em 0 0.8em 1.5em;\n    font-size: 0.9em;\n    opacity: 0.8;\n}\n/* Quotation marks */\nblockquote small:before {\n    content: \"\\2014 \\00A0\";\n}\n\nblockquote cite {\n    font-weight: bold;\n}\nblockquote cite a {\n    font-weight: normal;\n}\n\na {\n    color: #15171A;\n    text-decoration: none;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n    margin-top: 0;\n    line-height: 1.15;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-weight: 600;\n    text-rendering: optimizeLegibility;\n    letter-spacing: -0.01em;\n}\n\nh1 {\n    margin: 0 0 0.5em 0;\n    font-size: 4.8rem;\n    font-weight: 700;\n    letter-spacing: -0.015em;\n}\n@media (max-width: 600px) {\n    h1 {\n        font-size: 2.8rem;\n    }\n}\n\nh2 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2.8rem;\n    font-weight: 700;\n}\n@media (max-width: 600px) {\n    h2 {\n        font-size: 2.3rem;\n    }\n}\n\nh3 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2.4rem;\n    font-weight: 600;\n}\n@media (max-width: 600px) {\n    h3 {\n        font-size: 1.7rem;\n    }\n}\n\nh4 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2rem;\n}\n@media (max-width: 600px) {\n    h4 {\n        font-size: 1.7rem;\n    }\n}\n\nh5 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2rem;\n}\n\nh6 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 1.8rem;\n}\n", "/* Table of Contents\n/* ------------------------------------------------------------\n\nThis is a development CSS file which is built to a minified\nproduction stylesheet in assets/built/screen.css\n\n1.  Global Styles\n2.  Layout\n3.  Site Header\n4.  Site Navigation\n5.  Post Feed\n6.  Single Post\n  6.1. Post Byline\n  6.2. Subscribe\n  6.3. Read More\n  6.4. Comments\n7.  Author Template\n8.  Tag Template\n9.  Error Template\n10.  Site Footer\n11. Dark Mode\n12. Lightbox\n\n*/\n\n/* 1. Global - Set up the things\n/* ---------------------------------------------------------- */\n\n/* Import CSS reset and base styles */\n@import \"global.css\";\n\n:root {\n\n    /* Colours */\n    --color-green: #a4d037;\n    --color-yellow: #fecd35;\n    --color-red: #f05230;\n    --color-darkgrey: #15171A;\n    --color-midgrey: #738a94;\n    --color-lightgrey: #f1f1f1;\n    --color-secondary-text: #979797;\n    --color-border: #e1e1e1;\n    --color-wash: #e5eff5;\n    --color-darkmode: #151719;\n\n    /*\n    An accent color is also set by Ghost itself in\n    Ghost Admin > Settings > Brand\n\n    --ghost-accent-color: {value};\n\n    You can use this variable throughout your styles\n     */\n\n    /* Fonts */\n    --font-sans: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif;\n    --font-serif: Georgia, Times, serif;\n    --font-mono: Menlo, Courier, monospace;\n\n}\n\n\n/* 2. Layout - Page building blocks\n/* ---------------------------------------------------------- */\n\n.viewport {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    min-height: 100vh;\n}\n\n.site-content {\n    flex-grow: 1;\n}\n\n/* Full width page blocks */\n.outer {\n    position: relative;\n    padding: 0 max(4vmin, 20px);\n}\n\n/* Centered content container blocks */\n.inner {\n    margin: 0 auto;\n    max-width: 1200px;\n    width: 100%;\n}\n\n\n/* 3. Site Header\n/* ---------------------------------------------------------- */\n\n.site-header {\n    position: relative;\n    color: #fff;\n    background: var(--ghost-accent-color);\n}\n\n.site-header-cover {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n.site-header-content {\n    position: relative;\n    z-index: 100;\n    display: flex;\n    align-items: center;\n    padding-top: calc(19vmin + 44px);\n    padding-bottom: 19vmin;\n    text-align: center;\n    color: var(--color-darkgrey);\n}\n\n.has-cover .site-header-content {\n    min-height: 560px;\n    background-color: var(--ghost-accent-color);\n    color: #fff;\n}\n\n.site-header-content.left-aligned {\n    padding-bottom: 0;\n    text-align: left;\n}\n\n.has-cover .site-header-content.left-aligned {\n    align-items: flex-end;\n    padding-bottom: max(4vmin, 32px);\n}\n\n.site-header-content.no-content {\n    padding-top: 0;\n    padding-bottom: 2vmin;\n}\n\n.site-header-inner {\n    position: relative;\n}\n\n.site-header-content.left-aligned .site-header-inner {\n    align-items: flex-start;\n}\n\n.site-logo {\n    flex-shrink: 0;\n    margin: 0 auto;\n    max-height: 120px;\n}\n\n.site-header-content.left-aligned .site-logo {\n    margin-right: auto;\n    margin-left: 0;\n    max-height: 96px;\n}\n\n.site-title {\n    z-index: 10;\n    margin: 0;\n    padding: 0;\n    font-size: 5rem;\n    font-weight: 800;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .site-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.has-cover .site-title {\n    font-size: 6rem;\n}\n\n.site-header-content.left-aligned .site-title {\n    font-size: 4.4rem;\n}\n\n.has-cover .site-header-content.left-aligned .site-title {\n    font-size: 4.6rem;\n}\n\n.site-description {\n    display: inline-block;\n    z-index: 10;\n    max-width: 960px;\n    font-size: 6rem;\n    font-weight: 700;\n    line-height: 1.1;\n}\n\n.site-description:first-child {\n    font-family: var(--gh-font-heading, var(--font-sans));\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .site-description:first-child {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n:is(.site-logo, .site-title) + .site-description {\n    max-width: 640px;\n    margin-top: 16px;\n    font-size: 2.4rem;\n    font-weight: 400;\n    line-height: 1.4;\n}\n\n.site-logo + .site-description {\n    margin-top: 20px;\n}\n\n.site-title + .site-description {\n    color: var(--color-secondary-text);\n}\n\n.has-cover .site-description {\n    letter-spacing: -0.005em;\n    color: #fff;\n}\n\n.has-cover :is(.site-logo, .site-title) + .site-description {\n    font-size: 2.4rem;\n}\n\n.has-cover .site-header-content.left-aligned :is(.site-logo, .site-title) + .site-description {\n    font-size: 2.2rem;\n}\n\n@media (min-width: 992px) {\n    .is-head-stacked.has-cover .site-header-content {\n        padding-top: calc(19vmin + 120px);\n    }\n}\n\n@media (max-width: 991px) {\n    .site-header-content {\n        padding-top: calc(19vmin + 32px);\n    }\n}\n\n@media (max-width: 767px) {\n    .has-cover .site-header-content {\n        min-height: 240px;\n    }\n    .site-header-inner {\n        gap: 16px;\n    }\n    .site-logo {\n        max-width: 60%;\n    }\n    .site-title {\n        font-size: 3.4rem !important;\n    }\n    .site-description {\n        font-size: 2.2rem !important;\n    }\n    .site-logo + .site-description {\n        margin-top: 12px !important;\n    }\n    .site-title + .site-description {\n        margin-top: 12px !important;\n    }\n}\n\n\n/* 4. Site Navigation\n/* ---------------------------------------------------------- */\n\n.gh-head {\n    height: 88px;\n    font-size: 1.6rem;\n    line-height: 1.3em;\n    background-color: #fff;\n    z-index: 150;\n}\n\n.has-cover:not(.home-template) .gh-head {\n    background-color: var(--ghost-accent-color);\n    color: #fff;\n}\n\n:is(.home-template, .paged:not(.tag-template):not(.author-template)).has-cover .gh-head {\n    position: absolute;\n    top: 0;\n    right: 0;\n    left: 0;\n    z-index: 2000;\n    background-color: transparent;\n    color: #fff;\n}\n\n.gh-head a {\n    text-decoration: none;\n}\n\n.gh-head-inner {\n    display: grid;\n    column-gap: 40px;\n    grid-template-columns: auto 1fr auto;\n    grid-auto-flow: row dense;\n    align-items: center;\n    height: 100%;\n}\n\n/* Header styles\n/* ---------------------------------------------------------- */\n\n.is-head-left-logo .gh-head-inner {\n    grid-template-columns: auto 1fr auto;\n}\n\n.is-head-left-logo.home-template .gh-head:not(.is-header-hidden) .gh-head-logo {\n    display: none;\n}\n\n.is-head-left-logo.home-template .gh-head:not(.is-header-hidden) .gh-head-menu {\n    margin-left: -40px;\n}\n\n@media (min-width: 992px) {\n    .is-head-left-logo .gh-head-menu {\n        margin-right: 64px;\n        margin-left: 16px;\n    }\n}\n\n.is-head-middle-logo .gh-head-inner {\n    grid-template-columns: 1fr auto 1fr;\n}\n\n.is-head-middle-logo .gh-head-brand {\n    grid-column-start: 2;\n}\n\n@media (min-width: 992px) {\n    .is-head-middle-logo .gh-head-menu {\n        margin-right: 64px;\n    }\n}\n\n.is-head-stacked .gh-head {\n    height: auto;\n}\n\n.is-head-stacked .gh-head-inner {\n    grid-template-columns: 1fr auto 1fr;\n}\n\n.is-head-stacked .gh-head-brand {\n    grid-row-start: 1;\n    grid-column-start: 2;\n}\n\n@media (min-width: 992px) {\n    .is-head-stacked .gh-head-inner {\n        padding: 0;\n    }\n\n    .is-head-stacked .gh-head-brand {\n        position: relative;\n        display: flex;\n        align-items: center;\n        height: 80px;\n    }\n\n    .is-head-stacked .gh-head-menu {\n        grid-row-start: 2;\n        grid-column: 1 / 4;\n        justify-content: center;\n        height: 56px;\n        margin: 0 48px;\n    }\n\n    .is-head-stacked .gh-head-menu::before,\n    .is-head-stacked .gh-head-menu::after {\n        position: absolute;\n        top: 80px;\n        left: 0;\n        width: 100%;\n        height: 1px;\n        content: \"\";\n        background-color: var(--color-lightgrey);\n    }\n\n    .is-head-stacked.has-cover .gh-head-menu::before,\n    .is-head-stacked.has-cover .gh-head-menu::after {\n        background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .is-head-stacked .gh-head-menu::after {\n        top: 136px;\n    }\n\n    .is-head-stacked .gh-head-actions {\n        grid-row-start: 1;\n        grid-column: 1 / 4;\n        justify-content: space-between;\n    }\n}\n\n/* Brand\n/* ---------------------------------------------------------- */\n\n.gh-head-brand {\n    display: flex;\n    align-items: center;\n    height: 40px;\n    word-break: break-all;\n}\n\n.gh-head-logo {\n    display: block;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-weight: 800;\n    font-size: 2.6rem;\n    letter-spacing: -0.02em;\n    color: inherit;\n    white-space: nowrap;\n}\n\n.gh-head-logo.no-image {\n    margin-top: -5px;\n}\n\n.has-cover .gh-head-logo {\n    color: #fff;\n}\n\n.gh-head-logo img {\n    max-height: 40px;\n}\n\n\n/* Primary Navigation\n/* ---------------------------------------------------------- */\n\n.gh-head-menu {\n    display: flex;\n    align-items: center;\n    margin-top: 1px;\n    font-weight: 500;\n}\n\n.gh-head-menu .nav {\n    display: inline-flex;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 32px;\n    list-style: none;\n    margin: 0;\n    padding: 0;\n}\n\n.gh-head-menu .nav li {\n    margin: 0;\n    padding: 0;\n}\n\n.gh-head-menu .nav a {\n    display: inline-block;\n    line-height: 1.7;\n    color: inherit;\n}\n\n.gh-head-menu .nav a:hover {\n    opacity: 0.9;\n}\n\n.gh-head-menu .nav-more-toggle {\n    position: relative;\n    width: 30px;\n    height: 30px;\n    margin: 0 -6px;\n    padding: 0;\n    font-size: inherit;\n    background-color: transparent;\n    text-transform: inherit;\n}\n\n.gh-head-menu .nav-more-toggle svg {\n    width: 24px;\n    height: 24px;\n}\n\n@media (min-width: 992px) {\n    body:not(.is-dropdown-loaded) .gh-head-menu .nav > li {\n        opacity: 0;\n    }\n}\n\n\n/* Dropdown\n/* ---------------------------------------------------------- */\n\n.gh-dropdown {\n    position: absolute;\n    top: 100%;\n    right: -16px;\n    z-index: 90;\n    width: 200px;\n    padding: 12px 0;\n    margin-top: 24px;\n    opacity: 0;\n    visibility: hidden;\n    text-align: left;\n    background-color: #fff;\n    border-radius: 5px;\n    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 7px 20px -5px rgba(0, 0, 0, 0.15);\n    transform: translate3d(0, 6px, 0);\n    transition: opacity 0.3s, transform 0.2s;\n}\n\n.is-head-middle-logo .gh-dropdown {\n    right: auto;\n    left: -24px;\n}\n\n.is-dropdown-mega .gh-dropdown {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    grid-auto-flow: column;\n    column-gap: 40px;\n    min-width: 320px;\n    padding: 20px 32px;\n}\n\n.is-dropdown-open .gh-dropdown {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n}\n\n.gh-head-menu .gh-dropdown li a {\n    display: block;\n    padding: 6px 20px;\n    color: #15171a;\n}\n\n.is-dropdown-mega .gh-dropdown li a {\n    padding: 8px 0;\n}\n\n\n/* Secondary Navigation\n/* ---------------------------------------------------------- */\n\n.gh-social {\n    display: flex;\n    align-items: center;\n    gap: 20px;\n}\n\n.gh-social-link {\n    line-height: 0;\n    color: inherit;\n}\n\n.gh-social-link:hover {\n    opacity: 0.9;\n}\n\n.gh-social-link svg {\n    width: 18px;\n    height: 18px;\n}\n\n.gh-head-actions {\n    display: flex;\n    justify-content: flex-end;\n    align-items: center;\n    gap: 24px;\n    list-style: none;\n    text-align: right;\n}\n\n.gh-head-members {\n    display: flex;\n    gap: 20px;\n    align-items: center;\n}\n\n.gh-head-link {\n    font-weight: 500;\n    color: inherit;\n}\n\n.gh-head-button {\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n    padding: 8px 20px;\n    height: 44px;\n    font-weight: 600;\n    letter-spacing: -0.005em;\n    font-size: 1.6rem;\n    border-radius: 48px;\n    color: #fff;\n    background: var(--ghost-accent-color);\n}\n\n.has-cover .gh-head-button {\n    color: var(--color-darkgrey);\n    background: #fff;\n}\n\n@media (max-width: 767px) {\n    .gh-head-members {\n        flex-direction: column-reverse;\n        gap: 16px;\n        width: 100%;\n    }\n}\n\n\n/* Search\n/* ---------------------------------------------------------- */\n\n.gh-search {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    width: 32px;\n    height: 32px;\n    padding: 0;\n    cursor: pointer;\n    background-color: transparent;\n    border: 0;\n    outline: none;\n}\n\n.gh-search:hover {\n    opacity: 0.9;\n}\n\n.gh-head-brand .gh-search {\n    margin-right: 8px;\n}\n\n.gh-head-actions .gh-search {\n    margin-right: -4px;\n}\n\n@media (max-width: 767px) {\n    .gh-head-actions .gh-search {\n        display: none;\n    }\n}\n\n@media (min-width: 768px) {\n    .gh-head-brand .gh-search {\n        display: none;\n    }\n}\n\n\n/* Mobile Menu Trigger\n/* ---------------------------------------------------------- */\n\n.gh-burger {\n    position: relative;\n    display: none;\n    width: 30px;\n    height: 30px;\n    padding: 0;\n    margin-right: -3px;\n    cursor: pointer;\n    background-color: transparent;\n    border: 0;\n    appearance: none;\n}\n\n.gh-burger::before,\n.gh-burger::after {\n    position: absolute;\n    left: 3px;\n    width: 24px;\n    height: 1px;\n    content: \"\";\n    background-color: var(--color-darkgrey);\n    transition: all 0.2s cubic-bezier(0.04, 0.04, 0.12, 0.96) 0.1008s;\n}\n\n.has-cover .gh-burger::before,\n.has-cover .gh-burger::after {\n    background-color: #fff;\n}\n\n.gh-burger::before {\n    top: 11px;\n}\n\n.gh-burger::after {\n    bottom: 11px;\n}\n\n.gh-head-open .gh-burger::before {\n    top: 15px;\n    transform: rotate(45deg);\n}\n\n.gh-head-open .gh-burger::after {\n    bottom: 14px;\n    transform: rotate(-45deg);\n}\n\n\n/* Mobile Menu\n/* ---------------------------------------------------------- */\n/* IDs needed to ensure sufficient specificity */\n\n@media (max-width: 767px) {\n    #gh-head {\n        height: 64px;\n    }\n\n    #gh-head .gh-head-inner {\n        grid-template-rows: auto 1fr auto;\n        grid-template-columns: 1fr;\n        gap: 48px;\n    }\n\n    #gh-head .gh-head-brand {\n        display: grid;\n        grid-template-columns: 1fr auto auto;\n        grid-column-start: 1;\n        align-items: center;\n        height: 64px;\n    }\n\n    #gh-head .gh-head-logo {\n        font-size: 2.2rem;\n    }\n\n    #gh-head .gh-head-brand .gh-search {\n        margin-left: -6px;\n    }\n\n    #gh-head .gh-burger {\n        display: block;\n    }\n\n    #gh-head .gh-head-menu,\n    #gh-head .gh-head-actions {\n        position: fixed;\n        justify-content: center;\n        visibility: hidden;\n        opacity: 0;\n    }\n\n    #gh-head .gh-head-menu {\n        margin: 0;\n        transition: none;\n        transform: translateY(0);\n    }\n\n    #gh-head .nav {\n        gap: 16px;\n        align-items: center;\n        line-height: 1.4;\n    }\n\n    #gh-head .nav a {\n        font-size: 2.6rem;\n        font-weight: 600;\n        text-transform: none;\n    }\n\n    #gh-head .nav li {\n        opacity: 0;\n        transform: translateY(-4px);\n    }\n\n    #gh-head :is(.gh-head-button, .gh-head-link) {\n        opacity: 0;\n        transform: translateY(8px);\n    }\n\n    #gh-head .gh-head-button {\n        width: 100%;\n        font-size: 1.8rem;\n        text-transform: none;\n        opacity: 0;\n        transform: translateY(8px);\n    }\n\n    .gh-head-open #gh-head {\n        position: fixed;\n        inset: 0;\n        z-index: 3999999;\n        height: 100%;\n        overflow-y: scroll;\n        -webkit-overflow-scrolling: touch;\n    }\n\n    .gh-head-open.has-cover #gh-head,\n    .gh-head-open.has-cover #gh-head .gh-head-actions {\n        background-color: var(--ghost-accent-color);\n    }\n\n    .gh-head-open #gh-head .gh-head-menu,\n    .gh-head-open #gh-head .gh-head-actions {\n        position: static;\n        visibility: visible;\n        opacity: 1;\n    }\n\n    .gh-head-open #gh-head .nav {\n        display: flex;\n        flex-direction: column;\n    }\n\n    .gh-head-open #gh-head .nav li {\n        opacity: 1;\n        transition: transform 0.2s, opacity 0.2s;\n        transform: translateY(0);\n    }\n\n    .gh-head-open #gh-head .gh-head-actions {\n        position: sticky;\n        right: 0;\n        bottom: 0;\n        left: 0;\n        display: inline-flex;\n        flex-direction: column;\n        gap: 12px;\n        align-items: center;\n        padding: max(4vmin, 20px) 0 max(4vmin, 28px);\n        background-color: #fff;\n    }\n\n    .gh-head-open #gh-head :is(.gh-head-button, .gh-head-link) {\n        opacity: 1;\n        transition: transform 0.4s, opacity 0.4s;\n        transition-delay: 0.2s;\n        transform: translateY(0);\n    }\n\n    .gh-head-open #gh-head .gh-head-link {\n        transition-delay: 0.4s;\n    }\n}\n\n\n/* 5. Post Feed\n/* ---------------------------------------------------------- */\n\n.post-feed {\n    position: relative;\n    display: grid;\n    gap: 4.8vmin 4vmin;\n    grid-template-columns: repeat(6, 1fr);\n    padding: max(4.8vmin, 36px) 0 0;\n}\n\n:is(.tag-template, .author-template) .post-feed {\n    margin-top: 4vmin;\n}\n\n@media (max-width: 991px) {\n    .post-feed {\n        grid-template-columns: 1fr 1fr;\n    }\n}\n\n@media (max-width: 767px) {\n    .post-feed {\n        grid-template-columns: 1fr;\n        grid-gap: 40px;\n    }\n}\n\n.post-card {\n    position: relative;\n    grid-column: span 2;\n    display: flex;\n    flex-direction: column;\n    background-size: cover;\n    word-break: break-word;\n}\n\n.post-card-image-link {\n    position: relative;\n    overflow: hidden;\n    display: block;\n    margin-bottom: 32px;\n}\n\n.post-card-image-link::after {\n    content: \"\";\n    display: block;\n    padding-bottom: 55%;\n}\n\n.post-card[class*=\"post-access-\"] .post-card-image-link::after {\n    background-color: rgba(0, 0, 0, 0.5);\n    backdrop-filter: blur(3px);\n}\n\n.post-card.keep-ratio[class*=\"post-access-\"] .post-card-image-link::after {\n    position: absolute;\n    inset: 0;\n    padding-bottom: 0;\n}\n\n.post-card.keep-ratio:not(.post-card-large):not(.post-card-full) .post-card-image-link::after {\n    padding-bottom: 0;\n}\n\n.post-card-image {\n    position: absolute;\n    inset: 0;\n    width: 100%;\n    height: 100%;\n    background: var(--color-lightgrey) no-repeat center center;\n    object-fit: cover;\n}\n\n.post-card.keep-ratio:not(.post-card-large):not(.post-card-full) .post-card-image {\n    position: static;\n}\n\n.post-card-access {\n    position: absolute;\n    inset: 0;\n    z-index: 10;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 4px;\n    font-size: 1.5rem;\n    font-weight: 600;\n    color: #fff;\n}\n\n.post-card-content-link {\n    position: relative;\n    display: block;\n    color: var(--color-darkgrey);\n}\n\n.post-card-content-link:hover {\n    text-decoration: none;\n}\n\n.post-feed .no-image .post-card-content-link {\n    padding: 0;\n}\n\n.no-image .post-card-header {\n    margin-top: 0;\n}\n\n.post-card-tags {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n    margin: 0 0 10px;\n    color: var(--color-secondary-text);\n    font-size: 1.4rem;\n    font-weight: 600;\n    letter-spacing: -0.005em;\n    line-height: 1;\n}\n\n.post-card-featured {\n    position: relative;\n    display: flex;\n    align-items: center;\n    gap: 3px;\n    padding-left: 18px;\n    color: var(--ghost-accent-color);\n}\n\n.post-card-featured svg {\n    position: absolute;\n    left: 0;\n}\n\n.post-card-title {\n    margin: 0;\n    font-size: 2.6rem;\n    font-weight: 800;\n    line-height: 1.2;\n}\n\n.post-card-content-link:hover .post-card-title {\n    opacity: 0.9;\n}\n\n.no-image .post-card-title {\n    margin-top: 0;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .post-card-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n    letter-spacing: -0.005em;\n}\n\n.post-card-title svg {\n    margin-top: -3px;\n    margin-left: -1px;\n}\n\n.post-card-content {\n    flex-grow: 1;\n    display: flex;\n    flex-direction: column;\n}\n\n.post-card-excerpt {\n    display: -webkit-box;\n    overflow-y: hidden;\n    margin-top: 12px;\n    max-width: 720px;\n    font-size: 1.6rem;\n    line-height: 1.5;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    word-break: break-word;\n}\n\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .post-card-excerpt {\n    font-family: var(--gh-font-body, var(--font-sans));\n}\n\n.post-card:not(.post-card-large):not(.post-card-full):not(.dynamic):not(.no-image) .post-card-excerpt {\n    -webkit-line-clamp: 2;\n}\n\n:is(.tag-template, .author-template) .post-card-excerpt {\n    margin-top: 6px;\n}\n\n:is(.tag-template, .author-template) .post-card-large .post-card-excerpt {\n    display: block;\n    overflow-y: auto;\n}\n\n.post-card-meta {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n    margin-top: 12px;\n    padding: 0;\n    font-size: 1.3rem;\n    color: var(--color-secondary-text);\n}\n\n.post-card-meta > * {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n}\n\n.post-card-meta > * + *:not(script)::before {\n    width: 2px;\n    height: 2px;\n    content: \"\";\n    background-color: var(--color-secondary-text);\n    border-radius: 50%;\n}\n\n.post-card-meta .sep {\n    margin: 0 4px;\n}\n\n.author-profile-image {\n    display: block;\n    width: 100%;\n    height: 100%;\n    background: color-mod(var(--color-lightgrey) l(+10%));\n    border-radius: 100%;\n\n    object-fit: cover;\n}\n\n.author-list {\n    display: flex;\n    margin: 0 0 0 4px;\n    padding: 0;\n    list-style: none;\n}\n\n.author-list-item {\n    position: relative;\n    flex-shrink: 0;\n    margin: 0;\n    padding: 0;\n}\n\n\n/* Special Styling for home page grid (below):\n\nThe first post in the list is styled to be bigger than the others and take over\nthe full width of the grid to give it more emphasis. Wrapped in a media query to\nmake sure this only happens on large viewports / desktop-ish devices.\n\n*/\n\n@media (min-width: 1001px) {\n    .post-card-large {\n        grid-column: span 6;\n        display: grid;\n        grid-gap: 4vmin;\n        grid-template-columns: repeat(3, 1fr);\n        border-top: 0;\n    }\n\n    .post-card-large:not(.no-image) .post-card-header {\n        margin-top: 0;\n    }\n\n    .post-card-large .post-card-image-link {\n        position: relative;\n        grid-column: span 2;\n        margin-bottom: 0;\n    }\n\n    .post-card-large .post-card-content {\n        grid-column: span 1;\n    }\n\n    .post-card-large.no-image .post-card-content {\n        grid-column: span 2;\n    }\n\n    .post-card-large .post-card-image {\n        position: absolute;\n        width: 100%;\n        height: 100%;\n    }\n\n    .post-card-large .post-card-tags {\n        margin-bottom: 12px;\n    }\n\n    .post-card-large .post-card-title {\n        margin-top: 0;\n        font-size: 4.4rem;\n        line-height: 1.05;\n    }\n\n    .post-card-large .post-card-excerpt {\n        margin-top: 16px;\n    }\n\n    .post-card-full {\n        grid-column: span 6;\n    }\n\n    .post-card-full .post-card-image-link {\n        margin-bottom: 40px;\n    }\n\n    .post-card-full .post-card-tags {\n        margin-bottom: 14px;\n    }\n\n    .post-card-full .post-card-title {\n        font-size: 6.4rem;\n        line-height: 0.95;\n    }\n\n    .post-card-full .post-card-excerpt {\n        margin-top: 20px;\n        font-size: 1.8rem;\n    }\n\n    .post-card-large + .post-card-large:nth-child(even) {\n        margin: 32px 0;\n    }\n\n    .post-card-large + .post-card-large:nth-child(even) .post-card-content {\n        order: -1;\n    }\n\n    .post-card.dynamic {\n        grid-column: span 3;\n    }\n\n    .post-card.dynamic .post-card-title {\n        font-size: 3rem;\n    }\n}\n\n/* Pagination\n/* ---------------------------------------------------------- */\n\n.pagination {\n    display: none;\n    grid-template-columns: 1fr auto 1fr;\n    align-items: center;\n    margin-top: 8vmin;\n}\n\nhtml.no-infinite-scroll .pagination {\n    display: grid;\n}\n\n.pagination a {\n    font-size: 1.7rem;\n    font-weight: 600;\n}\n\n.pagination .page-number {\n    grid-column-start: 2;\n    color: var(--color-secondary-text);\n}\n\n.pagination .older-posts {\n    grid-column-start: 3;\n    text-align: right;\n}\n\n@media (max-width: 767px) {\n    .pagination .page-number {\n        display: none;\n    }\n}\n\n\n/* 6. Single Post\n/* ---------------------------------------------------------- */\n\n.article {\n    word-break: break-word;\n}\n\n.post-template .article {\n    padding: max(8vmin, 40px) 0 max(8vmin, 64px);\n}\n\n.post-template .article-header {\n    padding: 0 0 max(6.4vmin, 40px);\n}\n\n.page-template .article-header {\n    padding: max(12vmin, 64px) 0 max(3.2vmin, 28px);\n}\n\n.article-tag {\n    margin-bottom: 16px;\n    font-size: 1.6rem;\n}\n\n.article-tag a {\n    color: var(--color-secondary-text);\n}\n\n.article-title {\n    margin-bottom: 0;\n    font-size: clamp(3.2rem, 5vw, 5.2rem);\n    font-weight: 800;\n    line-height: 1.05;\n    color: var(--color-darkgrey);\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .article-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.article-excerpt {\n    margin-top: 20px;\n    max-width: 720px;\n    font-size: 2rem;\n    line-height: 1.45;\n    color: var(--color-darkgrey);\n}\n\n.gh-canvas .article-image {\n    grid-column: wide-start / wide-end;\n    width: 100%;\n    margin: max(6.4vmin, 40px) 0 0;\n}\n\n.image-full .article-image {\n    grid-column: full-start / full-end;\n}\n\n.image-small .article-image {\n    grid-column: main-start / main-end;\n}\n\n.gh-canvas .article-image img {\n    display: block;\n    margin-left: auto;\n    margin-right: auto;\n    width: 100%;\n}\n\n@media (max-width: 767px) {\n    .article-excerpt {\n        margin-top: 14px;\n        font-size: 1.7rem;\n    }\n}\n\n/* -------- */\n\n/* Content grid\n/* ---------------------------------------------------------- */\n\n/* Canvas creates a multi-column, centered grid which the post\nis laid out on top of. Canvas just defines the grid, we don't\nuse it for applying any other styles. */\n\n.gh-canvas,\n.kg-width-full.kg-content-wide {\n    --gap: max(4vmin, 20px);\n    --main: min(var(--content-width, 720px), 100% - var(--gap) * 2);\n    --wide: minmax(0, calc((var(--container-width, 1200px) - var(--content-width, 720px)) / 2));\n    --full: minmax(var(--gap), 1fr);\n\n    display: grid;\n    grid-template-columns:\n        [full-start] var(--full)\n        [wide-start] var(--wide)\n        [main-start] var(--main) [main-end]\n        var(--wide) [wide-end]\n        var(--full) [full-end];\n}\n\n.gh-canvas > * {\n    grid-column: main-start / main-end;\n}\n\n.kg-width-wide,\n.kg-content-wide > div {\n    grid-column: wide-start / wide-end;\n}\n\n.kg-width-full {\n    grid-column: full-start / full-end;\n}\n\n.kg-width-full img {\n    width: 100%;\n}\n\n\n/* Content\n/* ---------------------------------------------------------- */\n\n/* Content refers to styling all page and post content that is\ncreated within the Ghost editor. The main content handles\nheadings, text, images and lists. We deal with cards lower down. */\n\n/* Default vertical spacing */\n.gh-content > * + * {\n    margin-top: max(3.2vmin, 24px);\n    margin-bottom: 0;\n}\n\n/* Remove space between full-width cards */\n.gh-content > .kg-width-full + .kg-width-full:not(.kg-width-full.kg-card-hascaption + .kg-width-full) {\n    margin-top: 0;\n}\n\n/* [id] represents all headings h1-h6, reset all margins */\n.gh-content > [id] {\n    margin: 0;\n    color: var(--color-darkgrey);\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-content > [id] {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n/* Add back a top margin to all headings, unless a heading\nis the very first element in the post content */\n.gh-content > [id]:not(:first-child) {\n    margin: 2em 0 0;\n}\n\n/* Add a small margin between a heading and anything after it */\n.gh-content > [id] + * {\n    margin-top: 1.5rem !important;\n}\n\n/* A larger margin before/after HRs and blockquotes */\n.gh-content > hr,\n.gh-content > blockquote {\n    position: relative;\n    margin-top: max(4.8vmin, 32px);\n}\n.gh-content > hr + *,\n.gh-content > blockquote + * {\n    margin-top: max(4.8vmin, 32px) !important;\n}\n\n/* Now the content typography styles */\n.gh-content a {\n    color: var(--ghost-accent-color);\n    text-decoration: underline;\n    word-break: break-word;\n}\n\n.gh-content > blockquote:not([class]),\n.gh-content > ol,\n.gh-content > ul,\n.gh-content > dl,\n.gh-content > p {\n    font-family: var(--gh-font-body, var(--font-serif));\n    font-weight: 400;\n    font-size: 2rem;\n    line-height: 1.6em;\n}\n\n.gh-content > p img {\n  margin: 0 auto;\n}\n\n.page-template .gh-content:only-child > *:first-child:not(.kg-width-full) {\n    margin-top: max(12vmin, 64px);\n}\n\n.page-template .gh-content > *:last-child:not(.kg-width-full) {\n    margin-bottom: max(12vmin, 64px);\n}\n\n.gh-content .kg-callout-card .kg-callout-text,\n.gh-content .kg-toggle-card .kg-toggle-content > ol,\n.gh-content .kg-toggle-card .kg-toggle-content > ul,\n.gh-content .kg-toggle-card .kg-toggle-content > p {\n    font-family: var(--font-serif);\n    font-weight: 400;\n    font-size: 1.9rem;\n    line-height: 1.6em;\n}\n\n.gh-content .kg-product-card .kg-product-card-description > p,\n.gh-content .kg-product-card .kg-product-card-description > ol,\n.gh-content .kg-product-card .kg-product-card-description > ul {\n    font-size: 1.7rem;\n    line-height: 1.6em;\n}\n\n.gh-content .kg-callout-card .kg-callout-emoji {\n    font-size: 2.1rem;\n    line-height: 1.4em;\n}\n\n.gh-content .kg-toggle-card .kg-toggle-heading-text {\n    font-size: 2.0rem;\n}\n\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > blockquote,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > ol,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > ul,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > dl,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > p,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-callout-card .kg-callout-text,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-card .kg-toggle-content > ol,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-card .kg-toggle-content > ul,\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-card .kg-toggle-content > p {\n    font-family: var(--gh-font-body, var(--font-sans));\n}\n\n.gh-content > ul,\n.gh-content > ol,\n.gh-content > dl,\n.gh-content .kg-toggle-card .kg-toggle-content > ol,\n.gh-content .kg-toggle-card .kg-toggle-content > ul,\n.gh-content .kg-product-card .kg-product-card-description > ol,\n.gh-content .kg-product-card .kg-product-card-description > ul {\n    padding-left: 1.9em;\n}\n\n.gh-content > blockquote:not([class]) {\n    position: relative;\n    font-style: italic;\n    padding: 0;\n}\n\n.gh-content > blockquote:not([class])::before {\n    content: \"\";\n    position: absolute;\n    left: -1.5em;\n    top: 0;\n    bottom: 0;\n    width: 0.3rem;\n    background: var(--ghost-accent-color);\n}\n\n.gh-content :not(pre) > code {\n    vertical-align: middle;\n    padding: 0.15em 0.4em 0.15em;\n    border: #e1eaef 1px solid;\n    font-weight: 400 !important;\n    font-size: 0.9em;\n    line-height: 1em;\n    color: #15171A;\n    background: #f0f6f9;\n    border-radius: 0.25em;\n}\n\n.gh-content pre {\n    overflow: auto;\n    padding: 16px 20px;\n    color: var(--color-wash);\n    font-size: 1.4rem;\n    line-height: 1.5em;\n    background: var(--color-darkgrey);\n    border-radius: 5px;\n    box-shadow: 0 2px 6px -2px rgba(0,0,0,.1), 0 0 1px rgba(0,0,0,.4);\n}\n\n.gh-content ol ol li {\n    list-style-type: lower-alpha;\n}\n\n.gh-content ol ol ol li {\n    list-style-type: lower-roman;\n}\n\n@media (max-width: 650px) {\n    .gh-content > blockquote:not([class]),\n    .gh-content > ol,\n    .gh-content > ul,\n    .gh-content > dl,\n    .gh-content > p,\n    .gh-content .kg-callout-card .kg-callout-text,\n    .gh-content .kg-toggle-card .kg-toggle-content > ol,\n    .gh-content .kg-toggle-card .kg-toggle-content > ul,\n    .gh-content .kg-toggle-card .kg-toggle-content > p {\n        font-size: 1.8rem;\n    }\n\n    .gh-content .kg-product-card .kg-product-card-description > p,\n    .gh-content .kg-product-card .kg-product-card-description > ol,\n    .gh-content .kg-product-card .kg-product-card-description > ul {\n        font-size: 1.6rem;\n    }\n\n    .gh-content blockquote:not([class])::before {\n        left: min(-4vmin, -20px);\n    }\n}\n\n\n/* Cards\n/* ---------------------------------------------------------- */\n\n/* Cards are dynamic blocks of content which appear within Ghost\nposts, for example: embedded videos, tweets, galleries, or\nspecially styled bookmark links. We add extra styling here to\nmake sure they look good, and are given a bit of extra spacing. */\n\n/* Add extra margin before/after any cards,\nexcept for when immediately preceeded by a heading */\n.gh-content :not(.kg-card):not([id]) + .kg-card {\n    margin-top: 6vmin;\n}\n.gh-content .kg-card + :not(.kg-card) {\n    margin-top: 6vmin;\n}\n\n/* This keeps small embeds centered */\n.kg-embed-card {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    width: 100%;\n}\n\n/* This keeps small iamges centered */\n.kg-image-card img {\n    margin: auto;\n}\n\n.kg-image[width][height],\n.kg-gallery-image {\n    cursor: pointer;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-toggle-card .kg-toggle-heading-text {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.gh-content .kg-callout-card-accent a {\n    text-decoration: underline;\n}\n\n.kg-blockquote-alt {\n    font-family: var(--font-serif);\n    color: var(--color-midgrey);\n}\n\n.has-sans-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .kg-blockquote-alt {\n    font-family: var(--gh-font-body, var(--font-sans));\n}\n\n.kg-card.kg-header-card.kg-style-dark {\n    background: var(--color-darkgrey);\n}\n\n.kg-header-card.kg-style-light h2.kg-header-card-header {\n    color: color-mod(var(--color-darkgrey) l(-5%));\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-header-card h2.kg-header-card-header {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n\n/* Captions */\nfigcaption {\n    padding: 1.5rem 1.5rem 0;\n    text-align: center;\n    color: rgba(0,0,0,0.5);\n    font-size: 1.3rem;\n    line-height: 1.4em;\n}\nfigcaption strong {\n    color: rgba(0,0,0,0.8);\n}\nfigcaption a {\n    text-decoration: underline;\n}\n\n\n/* Highly specific styles for traditional Instagram embeds */\niframe.instagram-media {\n    margin-top: 6vmin !important;\n    margin-left: auto !important;\n    margin-right: auto !important;\n    margin-bottom: 0 !important;\n}\n\niframe.instagram-media + script + :not([id]) {\n    margin-top: 6vmin;\n}\n\n\n/* Card captions\n/* ---------------------------------------------------------- */\n\n.kg-width-full.kg-card-hascaption {\n    display: grid;\n    grid-template-columns: inherit;\n}\n\n.kg-width-wide.kg-card-hascaption img {\n    grid-column: wide-start / wide-end;\n}\n.kg-width-full.kg-card-hascaption img {\n    grid-column: 1 / -1;\n}\n\n.kg-width-full.kg-card-hascaption figcaption {\n    grid-column: main-start / main-end;\n}\n\n.article-comments {\n    margin: 6vmin 0 0 0;\n}\n\n/* -----old------ */\n\n.footnotes-sep {\n    margin-bottom: 30px;\n}\n\n.footnotes {\n    font-size: 1.5rem;\n}\n\n.footnotes p {\n    margin: 0;\n}\n\n.footnote-backref {\n    font-size: 1.2rem;\n    font-weight: bold;\n    text-decoration: none !important;\n    box-shadow: none !important;\n}\n\n/* Tables */\n.gh-content table:not(.gist table) {\n    display: inline-block;\n    overflow-x: auto;\n    max-width: 100%;\n    width: auto;\n    border-spacing: 0;\n    border-collapse: collapse;\n    font-family: var(--font-sans);\n    font-size: 1.6rem;\n    white-space: nowrap;\n    vertical-align: top;\n    -webkit-overflow-scrolling: touch;\n    background: radial-gradient(ellipse at left, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 75%) 0 center, radial-gradient(ellipse at right, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 75%) 100% center;\n    background-attachment: scroll, scroll;\n    background-size: 10px 100%, 10px 100%;\n    background-repeat: no-repeat;\n}\n\n.gh-content table:not(.gist table) td:first-child {\n    background-image: linear-gradient(to right, rgba(255,255,255, 1) 50%, rgba(255,255,255, 0) 100%);\n    background-size: 20px 100%;\n    background-repeat: no-repeat;\n}\n\n.gh-content table:not(.gist table) td:last-child {\n    background-image: linear-gradient(to left, rgba(255,255,255, 1) 50%, rgba(255,255,255, 0) 100%);\n    background-position: 100% 0;\n    background-size: 20px 100%;\n    background-repeat: no-repeat;\n}\n\n.gh-content table:not(.gist table) th {\n    color: var(--color-darkgrey);\n    font-size: 1.2rem;\n    font-weight: 700;\n    letter-spacing: 0.2px;\n    text-align: left;\n    text-transform: uppercase;\n    background-color: color-mod(var(--color-wash) l(+4%));\n}\n\n.gh-content table:not(.gist table) th,\n.gh-content table:not(.gist table) td {\n    padding: 6px 12px;\n    border: color-mod(var(--color-wash) l(-1%) s(-5%)) 1px solid;\n}\n\n\n/* 6.1. Post Byline\n/* ---------------------------------------------------------- */\n\n.article-byline {\n    display: flex;\n    justify-content: space-between;\n    margin: min(24px, 5.6vmin) 0 0;\n}\n\n.article-byline-content {\n    flex-grow: 1;\n    display: flex;\n    align-items: center;\n}\n\n.article-byline-content .author-list {\n    justify-content: flex-start;\n    padding: 0 14px 0 0;\n}\n\n.article-byline-meta {\n    color: var(--color-secondary-text);\n    font-size: 1.4rem;\n    line-height: 1.2em;\n}\n\n.article-byline-meta .author-name {\n    margin: 0 0 6px;\n    font-size: 1.7rem;\n    font-weight: 700;\n    letter-spacing: 0;\n}\n\n.article-byline-meta .bull {\n    display: inline-block;\n    margin: 0 2px;\n}\n\n.author-avatar {\n    display: block;\n    overflow: hidden;\n    margin: 0 -4px;\n    width: min(56px, 13.6vmin);\n    height: min(56px, 13.6vmin);\n    border: #fff 2px solid;\n    border-radius: 50%;\n    background-color: var(--color-border);\n}\n\n.page-template .article-title {\n    margin-bottom: 0;\n}\n\n@media (max-width: 767px) {\n    .article-byline-content {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 16px;\n    }\n\n    .article-byline-content .author-list {\n        padding-right: 12px;\n    }\n    .article-byline-meta .author-name {\n        margin-bottom: 4px;\n    }\n}\n\n\n/* 6.2. Subscribe\n/* ---------------------------------------------------------- */\n\n.footer-cta {\n    position: relative;\n    text-align: center;\n}\n\n.footer-cta-title {\n    margin: 0 0 min(24px, 6.4vmin);\n    font-size: clamp(2.6rem, 5vw, 3.8rem);\n    font-weight: 800;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .footer-cta-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.footer-cta-button {\n    position: relative;\n    display: inline-flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    max-width: 500px;\n    padding: 5px 5px 5px 15px;\n    font-size: 1.7rem;\n    color: var(--color-secondary-text);\n    background: #fff;\n    border: 1px solid var(--color-border);\n    border-radius: 8px;\n    transition: border-color 0.2s;\n}\n\n.footer-cta-button:hover {\n    border-color: color-mod(var(--color-border) l(-12%));\n}\n\n.footer-cta-button span {\n    display: inline-block;\n    padding: 9px 15px;\n    color: #fff;\n    font-size: 1.6rem;\n    font-weight: 600;\n    letter-spacing: -0.005em;\n    background: var(--ghost-accent-color);\n    border-radius: 6px;\n}\n\n\n/* 6.3. Read more\n/* ---------------------------------------------------------- */\n\n.read-more-wrap {\n    margin-top: 2.4vmin;\n}\n\n.footer-cta + .read-more-wrap {\n    margin-top: max(12vmin, 72px);\n}\n\n.read-more {\n    display: grid;\n    grid-template-columns: repeat(6, 1fr);\n    grid-gap: 4vmin;\n}\n\n.read-more .post-card-tags {\n    display: none;\n}\n\n@media (max-width: 1000px) {\n    .read-more {\n        grid-template-columns: repeat(4, 1fr);\n    }\n    .read-more .post-card:nth-child(3) {\n        display: none;\n    }\n}\n\n@media (max-width: 700px) {\n    .read-more {\n        grid-template-columns: repeat(2, 1fr);\n    }\n    .read-more .post-card:nth-child(2) {\n        display: none;\n    }\n}\n\n/* 6.4. Comments\n/* ---------------------------------------------------------- */\n.comments {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    margin: 60px 0 44px;\n}\n\n\n.comments-head {\n    display: flex;\n    align-items: baseline;\n    justify-content: space-between;\n    margin-bottom: 32px;\n    width: 100%;\n    max-width: 720px;\n}\n\n.comments h2 {\n    width: 100%;\n    max-width: 720px;\n    font-weight: 800;\n    font-size: 3.4rem;\n}\n\n.comments .comment-count {\n    color: var(--color-midgrey);\n    font-weight: 600;\n    white-space: nowrap;\n}\n\n.comments #ghost-comments-root {\n    width: 100%;\n    max-width: 720px;\n}\n\n\n/* 7. Author Template\n/* ---------------------------------------------------------- */\n\n.author-profile-pic {\n    display: block;\n    width: 80px;\n    height: 80px;\n    object-fit: cover;\n    margin: 0 0 2rem;\n    background: color-mod(var(--color-lightgrey) l(+10%));\n    border-radius: 50%;\n}\n\n.author-profile-footer {\n    margin-top: 16px;\n}\n\n.author-profile-location {\n    font-weight: 700;\n}\n\n.author-profile-meta {\n    display: flex;\n    gap: 12px;\n}\n\n.author-profile-social-link {\n    font-size: 1.3rem;\n    color: var(--color-secondary-text);\n}\n\n.author-profile-social-link:hover {\n    color: var(--color-darkgrey);\n}\n\n.author-profile-social-link svg {\n    width: 16px;\n    height: 16px;\n}\n\n@media (min-width: 1001px) {\n    .author-template .post-card-large .post-card-content:only-child {\n        grid-column: span 2;\n        max-width: 640px;\n    }\n}\n\n\n/* 8. Tag Template\n/* ---------------------------------------------------------- */\n\n.tag-template .post-card-large .post-card-image-link {\n    grid-column: 2 / span 2;\n    order: 2;\n}\n\n.tag-template .post-card-large .post-card-content {\n    order: 1;\n}\n\n@media (min-width: 1001px) {\n    .tag-template .post-card-large .post-card-content:only-child {\n        grid-column: span 2;\n        max-width: 640px;\n    }\n}\n\n/* 9. Error Template\n/* ---------------------------------------------------------- */\n\n.error-content {\n    padding: 14vw 4vw 2vw;\n}\n\n.error-message {\n    padding-bottom: 10vw;\n    text-align: center;\n}\n\n.error-code {\n    margin: 0;\n    font-size: 12vw;\n    line-height: 1em;\n    letter-spacing: -5px;\n}\n\n.error-description {\n    margin: 0;\n    color: var(--color-secondary-text);\n    font-size: 3.2rem;\n    line-height: 1.3em;\n    letter-spacing: -0.005em;\n    font-weight: 400;\n}\n\n.error-link {\n    display: inline-block;\n    margin-top: 5px;\n}\n\n@media (min-width: 940px) {\n    .error-content .post-card {\n        margin-bottom: 0;\n        padding-bottom: 0;\n        border-bottom: none;\n    }\n}\n\n@media (max-width: 800px) {\n    .error-content {\n        padding-top: 24vw;\n    }\n    .error-code {\n        font-size: 11.2rem;\n    }\n    .error-message {\n        padding-bottom: 16vw;\n    }\n    .error-description {\n        margin: 5px 0 0 0;\n        font-size: 1.8rem;\n    }\n}\n\n@media (max-width: 500px) {\n    .error-content {\n        padding-top: 28vw;\n    }\n    .error-message {\n        padding-bottom: 14vw;\n    }\n}\n\n\n/* 10. Site Footer\n/* ---------------------------------------------------------- */\n\n.site-footer {\n    position: relative;\n    margin: max(12vmin, 64px) 0 0 0;\n    padding-top: 48px;\n    padding-bottom: 140px;\n    color: #fff;\n    background: color-mod(var(--color-darkgrey) l(-5%));\n}\n\n.page-template .site-footer {\n    margin-top: 0;\n}\n\n.site-footer .inner {\n    display: grid;\n    grid-gap: 40px;\n    grid-template-columns: auto 1fr auto;\n    color: rgba(255,255,255,0.7);\n    font-size: 1.3rem;\n}\n\n.site-footer .copyright a {\n    color: #fff;\n    letter-spacing: -0.015em;\n    font-weight: 500;\n}\n\n.site-footer a {\n    color: rgba(255,255,255,0.7);\n}\n\n.site-footer a:hover {\n    color: rgba(255,255,255,1);\n    text-decoration: none;\n}\n\n.site-footer-nav ul {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n    margin: 0 0 20px;\n    padding: 0;\n    list-style: none;\n}\n\n.site-footer-nav li {\n    display: inline-flex;\n    align-items: center;\n    padding: 0;\n    margin: 0;\n    line-height: 2em;\n}\n\n.site-footer-nav a {\n    position: relative;\n    display: inline-flex;\n    align-items: center;\n    margin-left: 10px;\n}\n\n.site-footer-nav li:not(:first-child) a:before {\n    content: \"\";\n    display: block;\n    width: 2px;\n    height: 2px;\n    margin: 0 10px 0 0;\n    background: #fff;\n    border-radius: 100%;\n}\n\n@media (max-width: 767px) {\n    .site-footer .inner {\n        max-width: 500px;\n        grid-template-columns: 1fr;\n        grid-gap: 0;\n        text-align: center;\n    }\n    .site-footer .copyright,\n    .site-footer .copyright a {\n        color: #fff;\n        font-size: 1.5rem;\n    }\n    .site-footer .copyright {\n        margin-bottom: 16px;\n    }\n}\n\n\n/* 11. Dark Mode\n/* ---------------------------------------------------------- */\n\nhtml.dark-mode body {\n    color: rgba(255, 255, 255, 0.75);\n    background: var(--color-darkmode);\n}\n\nhtml.dark-mode img {\n    opacity: 0.9;\n}\n\nhtml.dark-mode kbd {\n    background: color-mod(var(--color-darkmode) l(+5%));\n}\n\nhtml.dark-mode figcaption a {\n    color: #fff;\n}\n\nhtml.dark-mode body:not(.has-cover) .gh-head {\n    background: var(--color-darkmode);\n    color: #fff;\n}\n\nhtml.dark-mode .gh-burger::before,\nhtml.dark-mode .gh-burger::after {\n    background-color: #fff;\n}\n\nhtml.dark-mode .site-header-content {\n    color: #fff;\n}\n\nhtml.dark-mode .site-header-cover {\n    opacity: 1;\n}\n\nhtml.dark-mode .post-card-image {\n    background: var(--color-darkmode);\n}\n\nhtml.dark-mode :is(.post-card-tags, .post-card-meta, .article-tag a, .byline-meta-content, .pagination .page-number) {\n    color: color-mod(var(--color-secondary-text) l(-22%));\n}\n\nhtml.dark-mode .post-card-featured {\n    color: #fff;\n}\n\nhtml.dark-mode .post-card-title {\n    color: #fff;\n}\n\nhtml.dark-mode .post-card-excerpt {\n    color: var(--color-secondary-text);\n}\n\nhtml.dark-mode .pagination a {\n    color: #fff;\n}\n\nhtml.dark-mode .author-profile-location {\n    color: #fff;\n}\n\nhtml.dark-mode .author-profile-social-link:hover {\n    color: #fff;\n}\n\nhtml.dark-mode .article-title {\n    color: #fff;\n}\n\nhtml.dark-mode .article-excerpt {\n    color: var(--color-secondary-text);\n}\n\nhtml.dark-mode .post-full-image {\n    background-color: color-mod(var(--color-darkmode) l(+8%));\n}\n\nhtml.dark-mode .author-avatar {\n    border-color: var(--color-darkmode);\n    background-color: color-mod(var(--color-darkmode) l(+8%));\n}\n\nhtml.dark-mode .author-profile-image {\n    opacity: 1;\n}\n\nhtml.dark-mode .author-profile-image path {\n    fill: var(--color-darkmode);\n}\n\nhtml.dark-mode .article-byline-meta .author-name a {\n    color: #fff;\n}\n\nhtml.dark-mode .no-image .author-social-link a {\n    color: rgba(255, 255, 255, 0.75);\n}\n\nhtml.dark-mode .gh-content > [id] {\n    color: rgba(255, 255, 255, 0.9);\n}\n\nhtml.dark-mode .gh-content pre {\n    background: color-mod(var(--color-darkgrey) l(-8%));\n}\n\nhtml.dark-mode .gh-content :not(pre) > code {\n    background: color-mod(var(--color-darkgrey) l(+6%));\n    border-color: color-mod(var(--color-darkmode) l(+8%));\n    color: var(--color-wash);\n}\n\n:where(html.dark-mode) .gh-content a {\n    color: #fff;\n}\n\nhtml.dark-mode .gh-content code {\n    color: #fff;\n    background: #000;\n}\n\nhtml.dark-mode hr {\n    border-top-color: color-mod(var(--color-darkmode) l(+8%));\n}\n\nhtml.dark-mode .gh-content hr:after {\n    background: color-mod(var(--color-darkmode) l(+8%));\n    box-shadow: var(--color-darkmode) 0 0 0 5px;\n}\n\nhtml.dark-mode figcaption {\n    color: rgba(255, 255, 255, 0.6);\n}\n\nhtml.dark-mode .gh-content table:not(.gist table) td:first-child {\n    background-image: linear-gradient(to right, var(--color-darkmode) 50%, color-mod(var(--color-darkmode) a(0%)) 100%);\n}\n\nhtml.dark-mode .gh-content table:not(.gist table) td:last-child {\n    background-image: linear-gradient(to left, var(--color-darkmode) 50%, color-mod(var(--color-darkmode) a(0%)) 100%);\n}\n\nhtml.dark-mode .gh-content table:not(.gist table) th {\n    color: rgba(255, 255, 255, 0.85);\n    background-color: color-mod(var(--color-darkmode) l(+8%));\n}\n\nhtml.dark-mode .gh-content table:not(.gist table) th,\nhtml.dark-mode .gh-content table:not(.gist table) td {\n    border: color-mod(var(--color-darkmode) l(+8%)) 1px solid;\n}\n\nhtml.dark-mode .gh-content :is(input, textarea) {\n    color: color-mod(var(--color-midgrey) l(-30%));\n}\n\nhtml.dark-mode .site-archive-header .no-image {\n    color: rgba(255, 255, 255, 0.9);\n    background: var(--color-darkmode);\n}\n\nhtml.dark-mode .kg-header-card.kg-style-dark {\n    background: color-mod(var(--color-darkgrey) l(-5%));\n}\n\nhtml.dark-mode .kg-header-card.kg-style-light {\n    background: color-mod(var(--color-darkgrey) l(+5%));\n}\n\nhtml.dark-mode .kg-header-card h2.kg-header-card-header,\nhtml.dark-mode .kg-header-card h3.kg-header-card-subheader {\n    color: #fff;\n}\n\nhtml.dark-mode .kg-bookmark-card a.kg-bookmark-container,\nhtml.dark-mode .kg-bookmark-card a.kg-bookmark-container:hover {\n    background: var(--color-darkmode) !important;\n    color: #fff !important;\n}\n\nhtml.dark-mode .footer-cta-title {\n    color: #fff;\n}\n\n@media (max-width: 767px) {\n    html.dark-mode .gh-head-open:not(.has-cover) #gh-head,\n    html.dark-mode .gh-head-open:not(.has-cover) #gh-head .gh-head-actions {\n        background: var(--color-darkmode);\n    }\n}\n\n@media (prefers-color-scheme: dark) {\n    html.auto-color body {\n        color: rgba(255, 255, 255, 0.75);\n        background: var(--color-darkmode);\n    }\n\n    html.auto-color img {\n        opacity: 0.9;\n    }\n\n    html.auto-color kbd {\n        background: color-mod(var(--color-darkmode) l(+5%));\n    }\n\n    html.auto-color figcaption a {\n        color: #fff;\n    }\n\n    html.auto-color body:not(.has-cover) .gh-head {\n        background: var(--color-darkmode);\n        color: #fff;\n    }\n\n    html.auto-color .gh-burger::before,\n    html.auto-color .gh-burger::after {\n        background-color: #fff;\n    }\n\n    html.auto-color .site-header-content {\n        color: #fff;\n    }\n\n    html.auto-color .site-header-cover {\n        opacity: 1;\n    }\n\n    html.auto-color .post-card-image {\n        background: var(--color-darkmode);\n    }\n\n    html.auto-color :is(.post-card-tags, .post-card-meta, .article-tag a, .byline-meta-content, .pagination .page-number) {\n        color: color-mod(var(--color-secondary-text) l(-22%));\n    }\n\n    html.auto-color .post-card-featured {\n        color: #fff;\n    }\n\n    html.auto-color .post-card-title {\n        color: #fff;\n    }\n\n    html.auto-color .post-card-excerpt {\n        color: var(--color-secondary-text);\n    }\n\n    html.auto-color .pagination a {\n        color: #fff;\n    }\n\n    html.auto-color .author-profile-location {\n        color: #fff;\n    }\n\n    html.auto-color .author-profile-social-link:hover {\n        color: #fff;\n    }\n\n    html.auto-color .article-title {\n        color: #fff;\n    }\n\n    html.auto-color .article-excerpt {\n        color: var(--color-secondary-text);\n    }\n\n    html.auto-color .post-full-image {\n        background-color: color-mod(var(--color-darkmode) l(+8%));\n    }\n\n    html.auto-color .author-avatar {\n        border-color: var(--color-darkmode);\n        background-color: color-mod(var(--color-darkmode) l(+8%));\n    }\n\n    html.auto-color .author-profile-image {\n        opacity: 1;\n    }\n\n    html.auto-color .author-profile-image path {\n        fill: var(--color-darkmode);\n    }\n\n    html.auto-color .article-byline-meta .author-name a {\n        color: #fff;\n    }\n\n    html.auto-color .no-image .author-social-link a {\n        color: rgba(255, 255, 255, 0.75);\n    }\n\n    html.auto-color .gh-content > [id] {\n        color: rgba(255, 255, 255, 0.9);\n    }\n\n    html.auto-color .gh-content pre {\n        background: color-mod(var(--color-darkgrey) l(-8%));\n    }\n\n    html.auto-color .gh-content :not(pre) > code {\n        background: color-mod(var(--color-darkgrey) l(+6%));\n        border-color: color-mod(var(--color-darkmode) l(+8%));\n        color: var(--color-wash);\n    }\n\n    :where(html.auto-color) .gh-content a {\n        color: #fff;\n    }\n\n    html.auto-color .gh-content code {\n        color: #fff;\n        background: #000;\n    }\n\n    html.auto-color hr {\n        border-top-color: color-mod(var(--color-darkmode) l(+8%));\n    }\n\n    html.auto-color .gh-content hr:after {\n        background: color-mod(var(--color-darkmode) l(+8%));\n        box-shadow: var(--color-darkmode) 0 0 0 5px;\n    }\n\n    html.auto-color figcaption {\n        color: rgba(255, 255, 255, 0.6);\n    }\n\n    html.auto-color .gh-content table:not(.gist table) td:first-child {\n        background-image: linear-gradient(to right, var(--color-darkmode) 50%, color-mod(var(--color-darkmode) a(0%)) 100%);\n    }\n\n    html.auto-color .gh-content table:not(.gist table) td:last-child {\n        background-image: linear-gradient(to left, var(--color-darkmode) 50%, color-mod(var(--color-darkmode) a(0%)) 100%);\n    }\n\n    html.auto-color .gh-content table:not(.gist table) th {\n        color: rgba(255, 255, 255, 0.85);\n        background-color: color-mod(var(--color-darkmode) l(+8%));\n    }\n\n    html.auto-color .gh-content table:not(.gist table) th,\n    html.auto-color .gh-content table:not(.gist table) td {\n        border: color-mod(var(--color-darkmode) l(+8%)) 1px solid;\n    }\n\n    html.auto-color .gh-content :is(input, textarea) {\n        color: color-mod(var(--color-midgrey) l(-30%));\n    }\n\n    html.auto-color .site-archive-header .no-image {\n        color: rgba(255, 255, 255, 0.9);\n        background: var(--color-darkmode);\n    }\n\n    html.auto-color .kg-header-card.kg-style-dark {\n        background: color-mod(var(--color-darkgrey) l(-5%));\n    }\n\n    html.auto-color .kg-header-card.kg-style-light {\n        background: color-mod(var(--color-darkgrey) l(+5%));\n    }\n\n    html.auto-color .kg-header-card h2.kg-header-card-header,\n    html.auto-color .kg-header-card h3.kg-header-card-subheader {\n        color: #fff;\n    }\n\n    html.auto-color .kg-bookmark-card a.kg-bookmark-container,\n    html.auto-color .kg-bookmark-card a.kg-bookmark-container:hover {\n        background: var(--color-darkmode) !important;\n        color: #fff !important;\n    }\n\n    html.auto-color .footer-cta-title {\n        color: #fff;\n    }\n\n    @media (max-width: 767px) {\n        html.auto-color .gh-head-open:not(.has-cover) #gh-head,\n        html.auto-color .gh-head-open:not(.has-cover) #gh-head .gh-head-actions {\n            background: var(--color-darkmode);\n        }\n    }\n}\n\n\n/* 12. Lightbox\n/* ---------------------------------------------------------- */\n\n.pswp {\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 3999999;\n    display: none;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    -ms-touch-action: none;\n    touch-action: none;\n    outline: none;\n    backface-visibility: hidden;\n    -webkit-text-size-adjust: 100%;\n}\n\n.pswp img {\n    max-width: none;\n}\n\n.pswp--animate_opacity {\n    opacity: 0.001;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    will-change: opacity;\n}\n\n.pswp--open {\n    display: block;\n}\n\n.pswp--zoom-allowed .pswp__img {\n    cursor: zoom-in;\n}\n\n.pswp--zoomed-in .pswp__img {\n    cursor: grab;\n}\n\n.pswp--dragging .pswp__img {\n    cursor: grabbing;\n}\n\n.pswp__bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, 0.85);\n    opacity: 0;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    transform: translateZ(0);\n    backface-visibility: hidden;\n    will-change: opacity;\n}\n\n.pswp__scroll-wrap {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n}\n\n.pswp__container,\n.pswp__zoom-wrap {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    touch-action: none;\n    backface-visibility: hidden;\n}\n\n.pswp__container,\n.pswp__img {\n    user-select: none;\n    -webkit-tap-highlight-color: transparent;\n    -webkit-touch-callout: none;\n}\n\n.pswp__zoom-wrap {\n    position: absolute;\n    width: 100%;\n    transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    transform-origin: left top;\n}\n\n.pswp--animated-in .pswp__bg,\n.pswp--animated-in .pswp__zoom-wrap {\n    transition: none;\n}\n\n.pswp__item {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    overflow: hidden;\n}\n\n.pswp__img {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: auto;\n    height: auto;\n}\n\n.pswp__img--placeholder {\n    backface-visibility: hidden;\n}\n\n.pswp__img--placeholder--blank {\n    background: #000;\n}\n\n.pswp--ie .pswp__img {\n    top: 0;\n    left: 0;\n    width: 100% !important;\n    height: auto !important;\n}\n\n.pswp__error-msg {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    width: 100%;\n    margin-top: -8px;\n    font-size: 14px;\n    line-height: 16px;\n    color: var(--color-secondary-text);\n    text-align: center;\n}\n\n.pswp__error-msg a {\n    color: var(--color-secondary-text);\n    text-decoration: underline;\n}\n\n.pswp__button {\n    position: relative;\n    display: block;\n    float: right;\n    width: 44px;\n    height: 44px;\n    padding: 0;\n    margin: 0;\n    overflow: visible;\n    appearance: none;\n    cursor: pointer;\n    background: none;\n    border: 0;\n    box-shadow: none;\n    transition: opacity 0.2s;\n}\n\n.pswp__button:focus,\n.pswp__button:hover {\n    opacity: 1;\n}\n\n.pswp__button:active {\n    outline: none;\n    opacity: 0.9;\n}\n\n.pswp__button::-moz-focus-inner {\n    padding: 0;\n    border: 0;\n}\n\n.pswp__ui--over-close .pswp__button--close {\n    opacity: 1;\n}\n\n.pswp__button,\n.pswp__button--arrow--left::before,\n.pswp__button--arrow--right::before {\n    width: 44px;\n    height: 44px;\n    background: url(\"../images/default-skin.png\") 0 0 no-repeat;\n    background-size: 264px 88px;\n}\n\n@media (-webkit-min-device-pixel-ratio: 1.1), (-webkit-min-device-pixel-ratio: 1.09375), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {\n    .pswp--svg .pswp__button,\n    .pswp--svg .pswp__button--arrow--left::before,\n    .pswp--svg .pswp__button--arrow--right::before {\n        background-image: url(\"../images/default-skin.svg\");\n    }\n\n    .pswp--svg .pswp__button--arrow--left,\n    .pswp--svg .pswp__button--arrow--right {\n        background: none;\n    }\n}\n\n.pswp__button--close {\n    background-position: 0 -44px;\n}\n\n.pswp__button--share {\n    background-position: -44px -44px;\n}\n\n.pswp__button--fs {\n    display: none;\n}\n\n.pswp--supports-fs .pswp__button--fs {\n    display: block;\n}\n\n.pswp--fs .pswp__button--fs {\n    background-position: -44px 0;\n}\n\n.pswp__button--zoom {\n    display: none;\n    background-position: -88px 0;\n}\n\n.pswp--zoom-allowed .pswp__button--zoom {\n    display: block;\n}\n\n.pswp--zoomed-in .pswp__button--zoom {\n    background-position: -132px 0;\n}\n\n.pswp--touch .pswp__button--arrow--left,\n.pswp--touch .pswp__button--arrow--right {\n    visibility: hidden;\n}\n\n.pswp__button--arrow--left,\n.pswp__button--arrow--right {\n    position: absolute;\n    top: 50%;\n    width: 70px;\n    height: 100px;\n    margin-top: -50px;\n    background: none;\n}\n\n.pswp__button--arrow--left {\n    left: 0;\n}\n\n.pswp__button--arrow--right {\n    right: 0;\n}\n\n.pswp__button--arrow--left::before,\n.pswp__button--arrow--right::before {\n    position: absolute;\n    top: 35px;\n    width: 32px;\n    height: 30px;\n    content: \"\";\n}\n\n.pswp__button--arrow--left::before {\n    left: 6px;\n    background-position: -138px -44px;\n}\n\n.pswp__button--arrow--right::before {\n    right: 6px;\n    background-position: -94px -44px;\n}\n\n.pswp__counter {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 44px;\n    padding: 0 15px;\n    font-size: 11px;\n    font-weight: 700;\n    line-height: 44px;\n    color: #fff;\n    user-select: none;\n}\n\n.pswp__caption {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    min-height: 44px;\n}\n\n.pswp__caption__center {\n    max-width: 420px;\n    padding: 25px 15px 30px;\n    margin: 0 auto;\n    font-size: 11px;\n    line-height: 1.6;\n    color: #fff;\n    text-align: center;\n}\n\n.pswp__caption__center .post-caption-title {\n    margin-bottom: 7px;\n    font-size: 15px;\n    font-weight: 500;\n    text-transform: uppercase;\n}\n\n.pswp__caption__center .post-caption-meta-item + .post-caption-meta-item::before {\n    padding: 0 4px;\n    content: \"\\02022\";\n}\n\n.pswp__caption--empty {\n    display: none;\n}\n\n.pswp__caption--fake {\n    visibility: hidden;\n}\n\n.pswp__preloader {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    width: 44px;\n    height: 44px;\n    margin-left: -22px;\n    opacity: 0;\n    transition: opacity 0.25s ease-out;\n    direction: ltr;\n    will-change: opacity;\n}\n\n.pswp__preloader__icn {\n    width: 20px;\n    height: 20px;\n    margin: 12px;\n}\n\n.pswp__preloader--active {\n    opacity: 1;\n}\n\n.pswp__preloader--active .pswp__preloader__icn {\n    background: url(\"../images/preloader.gif\") 0 0 no-repeat;\n}\n\n.pswp--css_animation .pswp__preloader--active {\n    opacity: 1;\n}\n\n.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {\n    animation: clockwise 500ms linear infinite;\n}\n\n.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {\n    animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;\n}\n\n.pswp--css_animation .pswp__preloader__icn {\n    position: absolute;\n    top: 15px;\n    left: 15px;\n    width: 14px;\n    height: 14px;\n    margin: 0;\n    background: none;\n    opacity: 0.75;\n}\n\n.pswp--css_animation .pswp__preloader__cut {\n    position: relative;\n    width: 7px;\n    height: 14px;\n    overflow: hidden;\n}\n\n.pswp--css_animation .pswp__preloader__donut {\n    position: absolute;\n    top: 0;\n    left: 0;\n    box-sizing: border-box;\n    width: 14px;\n    height: 14px;\n    margin: 0;\n    background: none;\n    border: 2px solid #fff;\n    border-bottom-color: transparent;\n    border-left-color: transparent;\n    border-radius: 50%;\n}\n\n@media screen and (max-width: 1024px) {\n    .pswp__preloader {\n        position: relative;\n        top: auto;\n        left: auto;\n        float: right;\n        margin: 0;\n    }\n}\n\n@keyframes clockwise {\n    0% {\n        transform: rotate(0deg);\n    }\n\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n@keyframes donut-rotate {\n    0% {\n        transform: rotate(0);\n    }\n\n    50% {\n        transform: rotate(-140deg);\n    }\n\n    100% {\n        transform: rotate(0);\n    }\n}\n\n.pswp__ui {\n    z-index: 1550;\n    visibility: visible;\n    opacity: 1;\n    -webkit-font-smoothing: auto;\n}\n\n.pswp__top-bar {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 44px;\n}\n\n.pswp__caption,\n.pswp__top-bar,\n.pswp--has_mouse .pswp__button--arrow--left,\n.pswp--has_mouse .pswp__button--arrow--right {\n    backface-visibility: hidden;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    will-change: opacity;\n}\n\n.pswp--has_mouse .pswp__button--arrow--left,\n.pswp--has_mouse .pswp__button--arrow--right {\n    visibility: visible;\n}\n\n.pswp__ui--idle .pswp__top-bar {\n    opacity: 0;\n}\n\n.pswp__ui--idle .pswp__button--arrow--left,\n.pswp__ui--idle .pswp__button--arrow--right {\n    opacity: 0;\n}\n\n.pswp__ui--hidden .pswp__top-bar,\n.pswp__ui--hidden .pswp__caption,\n.pswp__ui--hidden .pswp__button--arrow--left,\n.pswp__ui--hidden .pswp__button--arrow--right {\n    opacity: 0.001;\n}\n\n.pswp__ui--one-slide .pswp__button--arrow--left,\n.pswp__ui--one-slide .pswp__button--arrow--right,\n.pswp__ui--one-slide .pswp__counter {\n    display: none;\n}\n\n.pswp__element--disabled {\n    display: none !important;\n}\n\n.pswp--minimal--dark .pswp__top-bar {\n    background: none;\n}\n\n/*\n\nHey! You reached the end.\n\nHope you enjoyed this CSS file, if you have any suggestions\nfor improvements that might be useful for everyone who uses\nthis theme, you can find the open source repository for it\nhere: https://github.com/tryghost/casper\n\nOr, if you've just scrolled all the way to the bottom of the\nfile to add some of your own styles. Well, you've come to\nthe right place. Onward!\n\n */\n"]}