{"version": 3, "sources": ["global.css"], "names": [], "mappings": "AAGA,8YA+EI,QAAS,CACT,YAAa,CACb,cAAe,CAJf,QAAS,CACT,SAAU,CAIV,uBACJ,CACA,KACI,aACJ,CACA,aAEI,WACJ,CACA,oDAII,UAAW,CACX,YACJ,CAKA,IACI,aAAc,CAEd,WAAY,CADZ,cAEJ,CACA,KAII,yBAA0B,CAC1B,6BAA8B,CAJ9B,qBAAsB,CACtB,sBAIJ,CACA,iBAGI,kBACJ,CACA,EACI,4BACJ,CACA,iBAEI,SACJ,CACA,SAEI,eACJ,CACA,SAGI,iBACJ,CACA,GAEI,aAAc,CADd,cAEJ,CACA,MACI,aACJ,CACA,QAGI,aAAc,CACd,aAAc,CAFd,iBAAkB,CAGlB,uBACJ,CACA,IACI,SACJ,CACA,IACI,aACJ,CACA,IACI,QACJ,CACA,eACI,eACJ,CACA,KACI,wBACJ,CACA,kBAII,+BAAiC,CACjC,aACJ,CACA,IAII,kBAAmB,CACnB,sCAA2C,CAC3C,iBAAkB,CAClB,+CAAoD,CALpD,4BAA6B,CAC7B,gBAAiB,CAFjB,eAOJ,CACA,yBACI,IACI,gBACJ,CACJ,CACA,sCAMI,aAAc,CACd,YAAa,CAFb,QAGJ,CACA,OAEI,WAAY,CADZ,gBAEJ,CACA,cAEI,mBACJ,CACA,oEAOI,yBAA0B,CAF1B,cAGJ,CACA,sCAEI,cACJ,CACA,iDAGI,QAAS,CADT,SAEJ,CACA,MACI,kBACJ,CACA,YACI,YACJ,CACA,uCAEI,qBAAsB,CACtB,SACJ,CACA,4FAEI,WACJ,CACA,mBAGI,4BAA6B,CAF7B,sBAGJ,CACA,+FAEI,uBACJ,CACA,OAEI,QAAS,CADT,SAEJ,CACA,SACI,aACJ,CACA,MAEI,wBAAyB,CADzB,gBAEJ,CACA,MAEI,SACJ,CAMA,KAGI,yCAA6C,CAF7C,eAGJ,CACA,KAQI,iCAAkC,CAGlC,kCAAmC,CACnC,iCAAkC,CAClC,oCAAqC,CAJrC,eAAgB,CARhB,2BAA4B,CAC5B,gDAAkD,CAClD,gBAAiB,CAGjB,iBAAkB,CADlB,eAAgB,CAEhB,gBAAiB,CAHjB,iBAUJ,CAEA,iBAEI,kBAAmB,CADnB,gBAEJ,CAHA,YAEI,kBAAmB,CADnB,gBAEJ,CAEA,GAOI,QAAS,CACT,4BAA6B,CAN7B,aAAc,CAId,UAAW,CAFX,oBAAqB,CACrB,SAAU,CAJV,iBAAkB,CAElB,UAMJ,CAEA,kCAMI,qBACJ,CAEA,SAGI,QAAS,CAFT,QAAS,CACT,SAEJ,CAEA,SACI,eACJ,CAEA,qHAKI,gBACJ,CAEA,MAEI,kBAAmB,CACnB,mBACJ,CAEA,wBAII,aACJ,CAEA,MAEI,cACJ,CAEA,GAEI,iBAAkB,CADlB,iBAEJ,CAEA,MACI,eACJ,CAEA,GAII,aAAc,CAHd,UAAW,CAIX,eAAgB,CAHhB,iBAAkB,CAIlB,gBAAiB,CAHjB,WAIJ,CAEA,GACI,cAAiB,CACjB,eACJ,CAEA,WAGI,mBAAoB,CAFpB,cAAe,CACf,eAEJ,CAEA,iBACI,oBAAqB,CAErB,cAAgB,CADhB,wBAA2B,CAE3B,UACJ,CAEA,wBACI,qBACJ,CAEA,gBACI,eACJ,CACA,kBACI,eACJ,CAEA,EACI,aAAc,CACd,oBACJ,CAEA,kBAUI,iCAAkC,CAFlC,mDAAqD,CACrD,eAAgB,CAEhB,qBAAuB,CAJvB,gBAAiB,CADjB,YAMJ,CAEA,GAEI,gBAAiB,CACjB,eAAgB,CAChB,sBAAwB,CAHxB,eAIJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,gBAAiB,CACjB,eAAgB,CAFhB,mBAGJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,gBAAiB,CACjB,eAAgB,CAFhB,mBAGJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,cAAe,CADf,mBAEJ,CACA,yBACI,GACI,gBACJ,CACJ,CAEA,GAEI,cACJ,CAEA,MAJI,mBAOJ,CAHA,GAEI,gBACJ", "file": "global.css", "sourcesContent": ["/* Reset\n/* ---------------------------------------------------------- */\n\nhtml,\nbody,\ndiv,\nspan,\napplet,\nobject,\niframe,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\np,\nblockquote,\npre,\na,\nabbr,\nacronym,\naddress,\nbig,\ncite,\ncode,\ndel,\ndfn,\nem,\nimg,\nins,\nkbd,\nq,\ns,\nsamp,\nsmall,\nstrike,\nstrong,\nsub,\nsup,\ntt,\nvar,\ndl,\ndt,\ndd,\nol,\nul,\nli,\nfieldset,\nform,\nlabel,\nlegend,\ntable,\ncaption,\ntbody,\ntfoot,\nthead,\ntr,\nth,\ntd,\narticle,\naside,\ncanvas,\ndetails,\nembed,\nfigure,\nfigcaption,\nfooter,\nheader,\nhgroup,\nmenu,\nnav,\noutput,\nruby,\nsection,\nsummary,\ntime,\nmark,\naudio,\nvideo {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    font: inherit;\n    font-size: 100%;\n    vertical-align: baseline;\n}\nbody {\n    line-height: 1;\n}\nblockquote,\nq {\n    quotes: none;\n}\nblockquote:before,\nblockquote:after,\nq:before,\nq:after {\n    content: \"\";\n    content: none;\n}\ntable {\n    border-spacing: 0;\n    border-collapse: collapse;\n}\nimg {\n    display: block;\n    max-width: 100%;\n    height: auto;\n}\nhtml {\n    box-sizing: border-box;\n    font-family: sans-serif;\n\n    -ms-text-size-adjust: 100%;\n    -webkit-text-size-adjust: 100%;\n}\n*,\n*:before,\n*:after {\n    box-sizing: inherit;\n}\na {\n    background-color: transparent;\n}\na:active,\na:hover {\n    outline: 0;\n}\nb,\nstrong {\n    font-weight: bold;\n}\ni,\nem,\ndfn {\n    font-style: italic;\n}\nh1 {\n    margin: 0.67em 0;\n    font-size: 2em;\n}\nsmall {\n    font-size: 80%;\n}\nsub,\nsup {\n    position: relative;\n    font-size: 75%;\n    line-height: 0;\n    vertical-align: baseline;\n}\nsup {\n    top: -0.5em;\n}\nsub {\n    bottom: -0.25em;\n}\nimg {\n    border: 0;\n}\nsvg:not(:root) {\n    overflow: hidden;\n}\nmark {\n    background-color: #fdffb6;\n}\ncode,\nkbd,\npre,\nsamp {\n    font-family: monospace, monospace;\n    font-size: 1em;\n}\nkbd {\n    padding: 3px 5px;\n    font-family: var(--font-mono);\n    font-size: 1.5rem;\n    background: #f6f8fa;\n    border: 1px solid rgba(124, 139, 154, 0.25);\n    border-radius: 6px;\n    box-shadow: inset 0 -1px 0 rgba(124, 139, 154, 0.25);\n}\n@media (max-width: 600px) {\n    kbd {\n        font-size: 1.3rem;\n    }\n}\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n    margin: 0; /* 3 */\n    color: inherit; /* 1 */\n    font: inherit; /* 2 */\n}\nbutton {\n    overflow: visible;\n    border: none;\n}\nbutton,\nselect {\n    text-transform: none;\n}\nbutton,\nhtml input[type=\"button\"],\n/* 1 */\ninput[type=\"reset\"],\ninput[type=\"submit\"] {\n    cursor: pointer; /* 3 */\n\n    -webkit-appearance: button; /* 2 */\n}\nbutton[disabled],\nhtml input[disabled] {\n    cursor: default;\n}\nbutton::-moz-focus-inner,\ninput::-moz-focus-inner {\n    padding: 0;\n    border: 0;\n}\ninput {\n    line-height: normal;\n}\ninput:focus {\n    outline: none;\n}\ninput[type=\"checkbox\"],\ninput[type=\"radio\"] {\n    box-sizing: border-box; /* 1 */\n    padding: 0; /* 2 */\n}\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n    height: auto;\n}\ninput[type=\"search\"] {\n    box-sizing: content-box; /* 2 */\n\n    -webkit-appearance: textfield; /* 1 */\n}\ninput[type=\"search\"]::-webkit-search-cancel-button,\ninput[type=\"search\"]::-webkit-search-decoration {\n    -webkit-appearance: none;\n}\nlegend {\n    padding: 0; /* 2 */\n    border: 0; /* 1 */\n}\ntextarea {\n    overflow: auto;\n}\ntable {\n    border-spacing: 0;\n    border-collapse: collapse;\n}\ntd,\nth {\n    padding: 0;\n}\n\n/* ==========================================================================\n   Base styles: opinionated defaults\n   ========================================================================== */\n\nhtml {\n    font-size: 62.5%;\n\n    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\nbody {\n    color: var(--color-darkgrey);\n    font-family: var(--gh-font-body, var(--font-sans));\n    font-size: 1.6rem;\n    line-height: 1.6em;\n    font-weight: 400;\n    font-style: normal;\n    letter-spacing: 0;\n    text-rendering: optimizeLegibility;\n    background: #fff;\n\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    -moz-font-feature-settings: \"liga\" on;\n}\n\n::selection {\n    text-shadow: none;\n    background: #daf2fd;\n}\n\nhr {\n    position: relative;\n    display: block;\n    width: 100%;\n    margin: 2.5em 0 3.5em;\n    padding: 0;\n    height: 1px;\n    border: 0;\n    border-top: 1px solid #f0f0f0;\n}\n\naudio,\ncanvas,\niframe,\nimg,\nsvg,\nvideo {\n    vertical-align: middle;\n}\n\nfieldset {\n    margin: 0;\n    padding: 0;\n    border: 0;\n}\n\ntextarea {\n    resize: vertical;\n}\n\n::not(.gh-content) p,\n::not(.gh-content) ul,\n::not(.gh-content) ol,\n::not(.gh-content) dl,\n::not(.gh-content) blockquote {\n    margin: 0 0 1.5em 0;\n}\n\nol,\nul {\n    padding-left: 1.3em;\n    padding-right: 1.5em;\n}\n\nol ol,\nul ul,\nul ol,\nol ul {\n    margin: 0.5em 0;\n}\n\nul,\nol {\n    max-width: 100%;\n}\n\nli {\n    padding-left: 0.3em;\n    line-height: 1.6em;\n}\n\nli + li {\n    margin-top: 0.5em;\n}\n\ndt {\n    float: left;\n    margin: 0 20px 0 0;\n    width: 120px;\n    color: #daf2fd;\n    font-weight: 500;\n    text-align: right;\n}\n\ndd {\n    margin: 0 0 5px 0;\n    text-align: left;\n}\n\nblockquote {\n    margin: 1.5em 0;\n    padding: 0 1.6em 0 1.6em;\n    border-left: #daf2fd;\n}\n\nblockquote small {\n    display: inline-block;\n    margin: 0.8em 0 0.8em 1.5em;\n    font-size: 0.9em;\n    opacity: 0.8;\n}\n/* Quotation marks */\nblockquote small:before {\n    content: \"\\2014 \\00A0\";\n}\n\nblockquote cite {\n    font-weight: bold;\n}\nblockquote cite a {\n    font-weight: normal;\n}\n\na {\n    color: #15171A;\n    text-decoration: none;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n    margin-top: 0;\n    line-height: 1.15;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-weight: 600;\n    text-rendering: optimizeLegibility;\n    letter-spacing: -0.01em;\n}\n\nh1 {\n    margin: 0 0 0.5em 0;\n    font-size: 4.8rem;\n    font-weight: 700;\n    letter-spacing: -0.015em;\n}\n@media (max-width: 600px) {\n    h1 {\n        font-size: 2.8rem;\n    }\n}\n\nh2 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2.8rem;\n    font-weight: 700;\n}\n@media (max-width: 600px) {\n    h2 {\n        font-size: 2.3rem;\n    }\n}\n\nh3 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2.4rem;\n    font-weight: 600;\n}\n@media (max-width: 600px) {\n    h3 {\n        font-size: 1.7rem;\n    }\n}\n\nh4 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2rem;\n}\n@media (max-width: 600px) {\n    h4 {\n        font-size: 1.7rem;\n    }\n}\n\nh5 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 2rem;\n}\n\nh6 {\n    margin: 1.5em 0 0.5em 0;\n    font-size: 1.8rem;\n}\n"]}