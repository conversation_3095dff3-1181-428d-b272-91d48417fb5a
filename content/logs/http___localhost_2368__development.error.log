{"name":"Log","hostname":"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Pro-2.local","pid":98655,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"1a9f0cb7-665a-4ec4-8941-a45ef31df81a","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-vYptsnZ1XyHwUCI8374XAPNRJKE\""},"statusCode":403,"responseTime":"629ms"},"err":{"id":"d6bb9f70-6263-11f0-8fe0-956ae3b557a3","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:42:19.244Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":98655,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"b55f9451-837f-4242-8fe2-70512dcc821a","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-i820zuhXZ/PvbG/cDY5mAF7Qbzo\""},"statusCode":403,"responseTime":"3ms"},"err":{"id":"d6bdc250-6263-11f0-8fe0-956ae3b557a3","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:42:19.255Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","err":{"id":"d2fcd7d0-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":"ENOENT","name":"NotFoundError","statusCode":404,"level":"normal","message":"The currently active theme \"ease\" is missing.","context":"\"name: ease\"","help":"\"path: /Users/<USER>/Desktop/thelastblockbender/content/themes/\"","stack":"Error: ENOENT: no such file or directory, stat '/Users/<USER>/Desktop/thelastblockbender/content/themes/ease'","hideStack":true},"msg":"The currently active theme \"ease\" is missing.","time":"2025-07-16T16:56:31.950Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"1a3d1af0-d62b-4aa9-ad16-ef985a27efe2","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-2FL97/BzXMXXKE6gm0sZXIz9elA\""},"statusCode":403,"responseTime":"105ms"},"err":{"id":"d5d28d10-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:56:36.709Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"e0aa7d95-78dd-4708-a688-4e0841f6081b","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-mskQrrE3lKUZpN+hmzgLzH7IopU\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"d5de4ce0-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:56:36.787Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"19549ad0-3ef3-474a-a847-5e930ffe5bc0","userId":"1"},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","content-length":"66","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/plain, */*; q=0.01","content-type":"application/json;charset=UTF-8","origin":"http://localhost:2368","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"339","etag":"W/\"153-OoaAFUD9P/hot8oo8bXxTnDq+aA\"","set-cookie":"**REDACTED**"},"statusCode":500,"responseTime":"458ms"},"err":{"id":"ddbaa120-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Please check your site configuration and try again.","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Please check your site configuration and try again.\n    at Object.sendAuthCodeToUser (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/session-service.js:284:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Object.createSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:16:17)","hideStack":false},"msg":"Failed to send email. Please check your site configuration and try again.","time":"2025-07-16T16:56:49.982Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"aa4cfb7e-fca2-4129-a499-698288a1597e","userId":"1"},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","content-length":"66","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/plain, */*; q=0.01","content-type":"application/json;charset=UTF-8","origin":"http://localhost:2368","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"339","etag":"W/\"153-WwCl5vM06AhvbouMhFC3A+UeNpc\""},"statusCode":500,"responseTime":"358ms"},"err":{"id":"e0d99460-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Please check your site configuration and try again.","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Please check your site configuration and try again.\n    at Object.sendAuthCodeToUser (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/session-service.js:284:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Object.createSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:16:17)","hideStack":false},"msg":"Failed to send email. Please check your site configuration and try again.","time":"2025-07-16T16:56:55.212Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"87ac1989-b49f-49b3-99cf-872f1d070c8c","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-v+E2E0gwTdNTGR+ZZgCvlf50E80\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"0088b1b0-6266-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:48:28)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:57:48.365Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"0edcd19a-9fd2-4b3a-be5c-d709f93bcbca","userId":"1"},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","content-length":"66","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/plain, */*; q=0.01","content-type":"application/json;charset=UTF-8","origin":"http://localhost:2368","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"339","etag":"W/\"153-NQVVpdrGLorRVlE2ZruGzDZHq5g\""},"statusCode":500,"responseTime":"161ms"},"err":{"id":"055491a0-6266-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Please check your site configuration and try again.","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Please check your site configuration and try again.\n    at Object.sendAuthCodeToUser (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/session-service.js:284:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Object.createSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:16:17)","hideStack":false},"msg":"Failed to send email. Please check your site configuration and try again.","time":"2025-07-16T16:57:56.410Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","err":{"id":"36ca76f0-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":"ENOENT","name":"NotFoundError","statusCode":404,"level":"normal","message":"The currently active theme \"ease\" is missing.","context":"\"name: ease\"","help":"\"path: /Users/<USER>/Desktop/thelastblockbender/content/themes/\"","stack":"Error: ENOENT: no such file or directory, stat '/Users/<USER>/Desktop/thelastblockbender/content/themes/ease'","hideStack":true},"msg":"The currently active theme \"ease\" is missing.","time":"2025-07-16T16:59:19.391Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"fb5c0ff0-74dd-4e82-918b-110543c5e9b2","userId":"1"},"url":"/themes/active/","method":"GET","originalUrl":"/ghost/api/admin/themes/active/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"241","etag":"W/\"f1-8YnB5U/2zMf+5v0UdgR8TDqj7Vo\""},"statusCode":422,"responseTime":"30ms"},"err":{"id":"3861b8c0-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":null,"name":"ValidationError","statusCode":422,"level":"normal","message":"Theme \"ease\" is not loaded and cannot be checked.","stack":"ValidationError: Theme \"ease\" is not loaded and cannot be checked.\n    at Object.getThemeErrors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/themes/validate.js:114:15)\n    at async Object.query (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/api/endpoints/themes.js:30:33)\n    at async getResponse (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:259:34)\n    at async ImplWrapper (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:264:30)\n    at async Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:70:28)","hideStack":false,"errorDetails":"\"ease\""},"msg":"Theme \"ease\" is not loaded and cannot be checked.","time":"2025-07-16T16:59:22.072Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"ed2550b9-5869-40ac-976e-2b5fca8f3ad9","userId":null},"url":"/","method":"GET","originalUrl":"/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"4be-b29LKlm+ENG9E5RlPKQK2I44yec\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":500,"responseTime":"173ms"},"err":{"id":"3c787980-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":null,"name":"InternalServerError","statusCode":500,"level":"critical","message":"The currently active theme \"ease\" is missing.","stack":"InternalServerError: The currently active theme \"ease\" is missing.\n    at ensureActiveTheme (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/frontend/services/theme-engine/middleware/ensure-active-theme.js:19:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:280:10)\n    at loadMemberSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/members/middleware.js:97:9)","hideStack":false},"msg":"The currently active theme \"ease\" is missing.","time":"2025-07-16T16:59:29.090Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"e8edce5f-1d41-466f-8950-16276da65e3f","userId":"1"},"url":"/themes/active/","method":"GET","originalUrl":"/ghost/api/admin/themes/active/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"241","etag":"W/\"f1-n1HluIcd0mjK+Pf8NwdfigYRN4s\""},"statusCode":422,"responseTime":"18ms"},"err":{"id":"4b1b9350-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":null,"name":"ValidationError","statusCode":422,"level":"normal","message":"Theme \"ease\" is not loaded and cannot be checked.","stack":"ValidationError: Theme \"ease\" is not loaded and cannot be checked.\n    at Object.getThemeErrors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/themes/validate.js:114:15)\n    at async Object.query (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/api/endpoints/themes.js:30:33)\n    at async getResponse (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:259:34)\n    at async ImplWrapper (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:264:30)\n    at async Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:70:28)","hideStack":false,"errorDetails":"\"ease\""},"msg":"Theme \"ease\" is not loaded and cannot be checked.","time":"2025-07-16T16:59:53.479Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":80546,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Timeout awaiting 'request' for 1000ms","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-07-29T18:40:48.395Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":80546,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Worker for job \"update-check\" exited with code 1","stack":"Error: Worker for job \"update-check\" exited with code 1\n    at Worker.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/bree/lib/index.js:419:40)\n    at Worker.emit (node:events:518:28)\n    at [kOnExit] (node:internal/worker:315:10)\n    at Worker.<computed>.onexit (node:internal/worker:229:20)"},"msg":"Worker for job \"update-check\" exited with code 1","time":"2025-07-29T18:40:48.401Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":80546,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-07-30T20:12:50.259Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7ad650-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.680Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7b4b80-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.681Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7b99a0-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.682Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7b99a1-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.683Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":57905,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"10a038d4-3ed5-4af8-897a-f12fc4d2212b","userId":null},"url":"/search-index/posts","method":"GET","originalUrl":"/ghost/api/content/search-index/posts","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-rOeSNkSya1pQHjfmVXi4uDQDOAU\""},"statusCode":403,"responseTime":"11ms"},"err":{"id":"e7240570-6dd3-11f0-b278-4b7f16d54b0e","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:02:13.201Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":59159,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:04:17.321Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":59967,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:08:53.044Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":60343,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:10:52.435Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":61154,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"daccf243-a7c1-47b8-af7a-017259900e4d","userId":null},"url":"/search-index/pages","method":"GET","originalUrl":"/ghost/api/content/search-index/pages","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-pVQth0y4o5L/iB4B2sWLS596jao\""},"statusCode":403,"responseTime":"550ms"},"err":{"id":"b5db11a0-6dd5-11f0-932d-a53b20271096","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:15:09.517Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":61154,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"9996dc14-df8e-43b8-ae01-cd2a2b5ebccc","userId":null},"url":"/search-index/pages","method":"GET","originalUrl":"/ghost/api/content/search-index/pages","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-mzuCYibm0D5ReHU+HcmyxDeC4+Q\""},"statusCode":403,"responseTime":"4ms"},"err":{"id":"b92114e0-6dd5-11f0-932d-a53b20271096","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:15:14.991Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":61154,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:15:51.536Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"7bde38b7-7096-4efd-80d2-a58dd620eb29","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-UgZn8PCHKBDlfw2Ech4T/gPhrZM\""},"statusCode":403,"responseTime":"82ms"},"err":{"id":"d897c7a0-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:17.276Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"1765b791-fa4e-4e5c-893a-37a556f6bad9","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-7NaR1fFXGV56LZ6Ei07PdnlTzkw\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"d8f43f80-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:17.885Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"65083183-8a68-44e3-b00e-a15e18677c8e","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-/uXmyqY138JD5JhBrhLKHqC/uvY\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"d9956180-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:18.941Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"671ce396-8209-44d1-9a51-a23d318aaa4b","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-GnwM3ZeNp5fDv7XMrEQaZzW65XQ\""},"statusCode":403,"responseTime":"3ms"},"err":{"id":"da455090-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:20.091Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":63308,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"3cd5eda9-2e75-4ab7-a9e2-2df73e17d769","userId":null},"url":"/search-index/pages?key=22444f78447824223cefc48062","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=22444f78447824223cefc48062","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","pragma":"no-cache","cache-control":"no-cache","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-NXM8N3Tq9zXLjeHghu09TdarBVc\""},"statusCode":401,"responseTime":"93ms"},"err":{"id":"347dc7e0-6dd7-11f0-85e5-118fb28a23a6","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-07-31T06:25:51.455Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":63308,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"9162f311-01e9-4f65-ba58-be351e277ff2","userId":null},"url":"/search-index/pages?key=22444f78447824223cefc48062","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=22444f78447824223cefc48062","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","pragma":"no-cache","cache-control":"no-cache","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-mhFWd3U4ds5Vm+iEnXFz8YX+v1k\""},"statusCode":401,"responseTime":"3ms"},"err":{"id":"347e1600-6dd7-11f0-85e5-118fb28a23a6","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-07-31T06:25:51.456Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"46bfa50c-257d-4ab3-a53b-e045a1a5881d","userId":null},"url":"/","method":"GET","originalUrl":"/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"same-origin","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","referer":"http://localhost:2368/how-to-join/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**","if-none-match":"W/\"6fd6-pQjMKOHhyTtm74YsIq07cIILHhE\""},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"4d5-SBD77RVT/ecQrifIgVCKzCSCEJc\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":400,"responseTime":"70ms"},"err":{"id":"79ee6130-6e50-11f0-a29b-e579d20bf368","domain":"http://localhost:2368/","code":null,"name":"IncorrectUsageError","statusCode":400,"level":"critical","message":"[home.hbs] The partial server-players could not be found","stack":"Error: [home.hbs] The partial server-players could not be found\n    at prepareError (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/mw-error-handler/lib/mw-error-handler.js:87:19)\n    at Object.invokePartial (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/handlebars/dist/cjs/handlebars/runtime.js:332:11)\n    at Object.invokePartialWrapper [as invokePartial] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/handlebars/dist/cjs/handlebars/runtime.js:84:39)\n    at Object.eval [as main] (eval at createFunctionContext (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/handlebars/dist/cjs/handlebars/compiler/javascript-compiler.js:262:23), <anonymous>:11:28)\n    at main (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/handlebars/dist/cjs/handlebars/runtime.js:208:32)\n    at ret (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/handlebars/dist/cjs/handlebars/runtime.js:212:12)\n    at ret (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/handlebars/dist/cjs/handlebars/compiler/compiler.js:519:21)\n    at renderTemplate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-hbs/lib/hbs.js:499:13)\n    at render (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-hbs/lib/hbs.js:535:5)\n    at renderIt (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-hbs/lib/hbs.js:597:18)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-hbs/lib/hbs.js:607:11\n    at _returnLayouts (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-hbs/lib/hbs.js:131:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-hbs/lib/hbs.js:142:7\n    at FSReqCallback.readFileAfterClose [as oncomplete] (node:internal/fs/read/context:68:3)","hideStack":false},"msg":"[home.hbs] The partial server-players could not be found","time":"2025-07-31T20:53:57.157Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Timeout awaiting 'request' for 1000ms","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at runNextTicks (node:internal/process/task_queues:64:3)\n    at process.processTimers (node:internal/timers:516:9)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-08-02T09:22:25.133Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Worker for job \"update-check\" exited with code 1","stack":"Error: Worker for job \"update-check\" exited with code 1\n    at Worker.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/bree/lib/index.js:419:40)\n    at Worker.emit (node:events:518:28)\n    at [kOnExit] (node:internal/worker:315:10)\n    at Worker.<computed>.onexit (node:internal/worker:229:20)"},"msg":"Worker for job \"update-check\" exited with code 1","time":"2025-08-02T09:22:25.150Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"queryA ECONNREFUSED updates.ghost.org","stack":"RequestError: queryA ECONNREFUSED updates.ghost.org\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:107)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/promises:291:17)"},"msg":"queryA ECONNREFUSED updates.ghost.org","time":"2025-08-04T08:54:41.642Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Worker for job \"update-check\" exited with code 1","stack":"Error: Worker for job \"update-check\" exited with code 1\n    at Worker.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/bree/lib/index.js:419:40)\n    at Worker.emit (node:events:518:28)\n    at [kOnExit] (node:internal/worker:315:10)\n    at Worker.<computed>.onexit (node:internal/worker:229:20)"},"msg":"Worker for job \"update-check\" exited with code 1","time":"2025-08-04T08:54:41.644Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"queryA ECONNREFUSED updates.ghost.org","stack":"RequestError: queryA ECONNREFUSED updates.ghost.org\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:107)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/promises:291:17)"},"msg":"queryA ECONNREFUSED updates.ghost.org","time":"2025-08-05T08:57:04.355Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Worker for job \"update-check\" exited with code 1","stack":"Error: Worker for job \"update-check\" exited with code 1\n    at Worker.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/bree/lib/index.js:419:40)\n    at Worker.emit (node:events:518:28)\n    at [kOnExit] (node:internal/worker:315:10)\n    at Worker.<computed>.onexit (node:internal/worker:229:20)"},"msg":"Worker for job \"update-check\" exited with code 1","time":"2025-08-05T08:57:04.360Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-08-05T19:27:07.040Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":94136,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-08-05T19:27:07.044Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":62942,"level":50,"version":"6.0.0","msg":"Could not get webhook secret for ActivityPub FetchError: invalid json response body at http://localhost:2368/.ghost/activitypub/v1/site/ reason: Unexpected token '<', \"<!DOCTYPE \"... is not valid JSON","time":"2025-08-05T20:37:19.057Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":62942,"level":50,"version":"6.0.0","msg":"No webhook secret found - cannot initialise","time":"2025-08-05T20:37:19.057Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":31401,"level":50,"version":"6.0.0","msg":"Could not get webhook secret for ActivityPub FetchError: invalid json response body at http://localhost:2368/.ghost/activitypub/v1/site/ reason: Unexpected token '<', \"<!DOCTYPE \"... is not valid JSON","time":"2025-08-07T18:27:22.259Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":31401,"level":50,"version":"6.0.0","msg":"No webhook secret found - cannot initialise","time":"2025-08-07T18:27:22.259Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":38420,"level":50,"version":"6.0.0","msg":"Could not get webhook secret for ActivityPub FetchError: invalid json response body at http://localhost:2368/.ghost/activitypub/v1/site reason: Unexpected token '<', \"<!DOCTYPE \"... is not valid JSON","time":"2025-08-07T18:36:28.693Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":38420,"level":50,"version":"6.0.0","msg":"No webhook secret found - cannot initialise","time":"2025-08-07T18:36:28.693Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","msg":"Could not get webhook secret for ActivityPub FetchError: invalid json response body at http://localhost:2368/.ghost/activitypub/v1/site reason: Unexpected token '<', \"<!DOCTYPE \"... is not valid JSON","time":"2025-08-07T18:41:49.579Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","msg":"No webhook secret found - cannot initialise","time":"2025-08-07T18:41:49.579Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:639:26)\n    at ClientRequest.emit (node:events:536:35)\n    at emitErrorEvent (node:_http_client:104:11)\n    at TLSSocket.socketErrorListener (node:_http_client:518:5)\n    at TLSSocket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:596:11)\n    at process.processTimers (node:internal/timers:529:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-08-12T18:33:20.076Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:639:26)\n    at ClientRequest.emit (node:events:536:35)\n    at emitErrorEvent (node:_http_client:104:11)\n    at TLSSocket.socketErrorListener (node:_http_client:518:5)\n    at TLSSocket.emit (node:events:524:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at listOnTimeout (node:internal/timers:555:9)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:596:11)\n    at process.processTimers (node:internal/timers:529:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-08-12T18:33:20.084Z","v":0}
{"0":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef137c70-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f96061\",\"title\":\"How to Claim Land and Build Kingdoms in Minecraft Multiplayer\",\"slug\":\"how-to-claim-land-and-build-kingdoms-in-minecraft-multiplayer\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\\\n\\\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\\\n\\\\n*See also: <a href=\\\\\\\"/kingdom-management/\\\\\\\">Kingdom Management</a>, <a href=\\\\\\\"/region-flags/\\\\\\\">Region Flags*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can someone else build in my kingdom without permission?\\\",\\\"content\\\":\\\"<p>No. Only people you invite and give permissions to can build in your claimed land.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"61761187-b07a-462f-8a6b-54ce765fb7e8\",\"comment_id\":\"how-to-claim-land-and-build-kingdoms-in-minecraft-multiplayer\",\"html\":\"<!--kg-card-begin: html-->How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\n\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\n\\n*See also: <a href=\\\"/kingdom-management/\\\">Kingdom Management</a>, <a href=\\\"/region-flags/\\\">Region Flags*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\n\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\n\\n*See also: <a href=\\\"/kingdom-management/\\\">Kingdom Management</a>, <a href=\\\"/region-flags/\\\">Region Flags*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"1":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef1466d0-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f96062\",\"title\":\"How to Set a Home and Teleport in Minecraft Survival\",\"slug\":\"how-to-set-a-home-and-teleport-in-minecraft-survival\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"How to Set a Home and Teleport in Minecraft Survival\\\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\\\n\\\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\\\n\\\\n*See also: <a href=\\\\\\\"/setting-homes/\\\\\\\">Setting Homes</a>, <a href=\\\\\\\"/using-rtp/\\\\\\\">Using `/rtp`*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"What if I forget where my home is?\\\",\\\"content\\\":\\\"<p>If you've set your home using `/sethome`, you can always return using `/home`.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I have more than one home?\\\",\\\"content\\\":\\\"<p>Yes! You can use `/sethome <name>` to create multiple homes and return to them with `/home <name>`.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"7f77c36e-2897-4798-93bb-94d772579b3a\",\"comment_id\":\"how-to-set-a-home-and-teleport-in-minecraft-survival\",\"html\":\"<!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"2":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef1f6350-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f96067\",\"title\":\"Minecraft Farming Rules and Animal Limits Explained\",\"slug\":\"minecraft-farming-rules-and-animal-limits-explained\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Minecraft Farming Rules and Animal Limits Explained\\\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\\\n\\\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\\\n\\\\n*See also: <a href=\\\\\\\"/crop-farming-rules/\\\\\\\">Crop Farming Rules*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How many animals can I have?\\\",\\\"content\\\":\\\"<p>The limit varies, but aim for no more than 15-20 of each animal in one spot.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Will my crops be affected?\\\",\\\"content\\\":\\\"<p>Crops are fine to grow, just don't overbuild huge auto-farms in one location.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"7d7039ae-d188-4576-acac-6fe0c6d946c9\",\"comment_id\":\"minecraft-farming-rules-and-animal-limits-explained\",\"html\":\"<!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Avoid lag and keep your farms running smoothly with these farming and animal limits.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"3":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef244550-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f9606b\",\"title\":\"Avatar Bending Powers in Minecraft: How to Choose and Use Elements\",\"slug\":\"avatar-bending-powers-in-minecraft-how-to-choose-and-use-elements\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\\\n\\\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\\\n\\\\n*See also: <a href=\\\\\\\"/bending-gui/\\\\\\\">Bending GUI</a>, <a href=\\\\\\\"/element-descriptions/\\\\\\\">Element Descriptions*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I change my element later?\\\",\\\"content\\\":\\\"<p>Yes, but some changes may require a cooldown or an item—ask staff if unsure.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Are bending moves balanced?\\\",\\\"content\\\":\\\"<p>Yes, each element has strengths and weaknesses to keep PvP fair.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"8e4f29db-0a36-464e-bf6c-44bcf2ad405c\",\"comment_id\":\"avatar-bending-powers-in-minecraft-how-to-choose-and-use-elements\",\"html\":\"<!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"4":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef2508a0-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f9606c\",\"title\":\"Customizing with Minecraft Particle Effects and Trails\",\"slug\":\"customizing-with-minecraft-particle-effects-and-trails\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Customizing with Minecraft Particle Effects and Trails\\\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\\\n\\\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\\\n\\\\n*See also: <a href=\\\\\\\"/available-particle-effects/\\\\\\\">Available Particle Effects*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Do particles give me any powers?\\\",\\\"content\\\":\\\"<p>No, they are purely visual and just for fun.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How do I turn particles off?\\\",\\\"content\\\":\\\"<p>Use the `/pp` menu to disable or change your effects.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"7a6ad6cb-bac9-409d-842c-21c5a38f1fb6\",\"comment_id\":\"customizing-with-minecraft-particle-effects-and-trails\",\"html\":\"<!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Personalize your character with particles, trails, and cosmetic visual effects in-game.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"5":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef257dd0-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f9606d\",\"title\":\"Setting Nicknames and Pronouns on the Minecraft Server\",\"slug\":\"setting-nicknames-and-pronouns-on-the-minecraft-server\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Setting Nicknames and Pronouns on the Minecraft Server\\\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\\\n\\\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I use any name I want?\\\",\\\"content\\\":\\\"<p>You can pick any appropriate nickname that follows the server rules.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How do I set my pronouns?\\\",\\\"content\\\":\\\"<p>Use the pronoun setting command or ask staff for help.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"1d0992a9-81b1-4bda-a23e-acfcffbed01e\",\"comment_id\":\"setting-nicknames-and-pronouns-on-the-minecraft-server\",\"html\":\"<!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html--><!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html--><!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"6":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ef25f300-77ab-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8ace41176f9a18f9606e\",\"title\":\"Getting Married and Setting Partnerships in Minecraft Multiplayer\",\"slug\":\"getting-married-and-setting-partnerships-in-minecraft-multiplayer\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Getting Married and Setting Partnerships in Minecraft Multiplayer\\\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\\\n\\\\nPlayers can get \\\\\\\"married\\\\\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\\\n\\\\n*See also: <a href=\\\\\\\"/marriage-commands/\\\\\\\">Marriage Commands</a>, <a href=\\\\\\\"/marital-perks/\\\\\\\">Marital Perks*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Do I have to be married to teleport to someone?\\\",\\\"content\\\":\\\"<p>No, but married players get instant access to partner teleport features.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I get divorced or remarried?\\\",\\\"content\\\":\\\"<p>Yes, use `/divorce` if you want to end a partnership and start a new one.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8acd41176f9a18f9605f\"}],\"uuid\":\"26a2aec4-5590-4924-abe9-c0937fbcf398\",\"comment_id\":\"getting-married-and-setting-partnerships-in-minecraft-multiplayer\",\"html\":\"<!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Team up with a partner in-game to share homes, teleport, and unlock marriage features.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","msg":"Content import was unsuccessful","time":"2025-08-12T18:41:18.397Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","err":{"id":"04a4b9f0-77ac-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":null,"name":"UnhandledJobError","statusCode":500,"level":"critical","message":"Processed job threw an unhandled error","context":"\"function\"","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"Error: Sending failed\n    at /Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/job-manager/lib/JobManager.js:267:35\n    at createMailError (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/mail/GhostMailer.js:81:12)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:157:41)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:201:22)\n    at QueryReqWrap.callback (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:474:24)\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/callback_resolver:45:10)","hideStack":false},"msg":"Processed job threw an unhandled error","time":"2025-08-12T18:41:54.450Z","v":0}
{"0":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db5873a0-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f9608e\",\"title\":\"How to Claim Land and Build Kingdoms in Minecraft Multiplayer\",\"slug\":\"how-to-claim-land-and-build-kingdoms-in-minecraft-multiplayer\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\\\n\\\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\\\n\\\\n*See also: <a href=\\\\\\\"/kingdom-management/\\\\\\\">Kingdom Management</a>, <a href=\\\\\\\"/region-flags/\\\\\\\">Region Flags*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can someone else build in my kingdom without permission?\\\",\\\"content\\\":\\\"<p>No. Only people you invite and give permissions to can build in your claimed land.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"94ce6157-3377-4274-a12f-152e2b72f123\",\"comment_id\":\"how-to-claim-land-and-build-kingdoms-in-minecraft-multiplayer\",\"html\":\"<!--kg-card-begin: html-->How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\n\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\n\\n*See also: <a href=\\\"/kingdom-management/\\\">Kingdom Management</a>, <a href=\\\"/region-flags/\\\">Region Flags*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\n\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\n\\n*See also: <a href=\\\"/kingdom-management/\\\">Kingdom Management</a>, <a href=\\\"/region-flags/\\\">Region Flags*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"1":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db590fe0-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f9608f\",\"title\":\"How to Set a Home and Teleport in Minecraft Survival\",\"slug\":\"how-to-set-a-home-and-teleport-in-minecraft-survival\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"How to Set a Home and Teleport in Minecraft Survival\\\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\\\n\\\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\\\n\\\\n*See also: <a href=\\\\\\\"/setting-homes/\\\\\\\">Setting Homes</a>, <a href=\\\\\\\"/using-rtp/\\\\\\\">Using `/rtp`*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"What if I forget where my home is?\\\",\\\"content\\\":\\\"<p>If you've set your home using `/sethome`, you can always return using `/home`.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I have more than one home?\\\",\\\"content\\\":\\\"<p>Yes! You can use `/sethome <name>` to create multiple homes and return to them with `/home <name>`.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"09d2133a-8edf-4e70-98a7-1bb5e524228d\",\"comment_id\":\"how-to-set-a-home-and-teleport-in-minecraft-survival\",\"html\":\"<!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"2":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db5e6710-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f96094\",\"title\":\"Minecraft Farming Rules and Animal Limits Explained\",\"slug\":\"minecraft-farming-rules-and-animal-limits-explained\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Minecraft Farming Rules and Animal Limits Explained\\\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\\\n\\\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\\\n\\\\n*See also: <a href=\\\\\\\"/crop-farming-rules/\\\\\\\">Crop Farming Rules*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How many animals can I have?\\\",\\\"content\\\":\\\"<p>The limit varies, but aim for no more than 15-20 of each animal in one spot.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Will my crops be affected?\\\",\\\"content\\\":\\\"<p>Crops are fine to grow, just don't overbuild huge auto-farms in one location.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"1ae5ff9c-00dc-40ae-8fc3-569e1336961b\",\"comment_id\":\"minecraft-farming-rules-and-animal-limits-explained\",\"html\":\"<!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Avoid lag and keep your farms running smoothly with these farming and animal limits.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"3":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db639730-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f96098\",\"title\":\"Avatar Bending Powers in Minecraft: How to Choose and Use Elements\",\"slug\":\"avatar-bending-powers-in-minecraft-how-to-choose-and-use-elements\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\\\n\\\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\\\n\\\\n*See also: <a href=\\\\\\\"/bending-gui/\\\\\\\">Bending GUI</a>, <a href=\\\\\\\"/element-descriptions/\\\\\\\">Element Descriptions*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I change my element later?\\\",\\\"content\\\":\\\"<p>Yes, but some changes may require a cooldown or an item—ask staff if unsure.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Are bending moves balanced?\\\",\\\"content\\\":\\\"<p>Yes, each element has strengths and weaknesses to keep PvP fair.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"dd7ade83-c27d-4f43-9181-748fb532a0c4\",\"comment_id\":\"avatar-bending-powers-in-minecraft-how-to-choose-and-use-elements\",\"html\":\"<!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"4":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db645a80-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f96099\",\"title\":\"Customizing with Minecraft Particle Effects and Trails\",\"slug\":\"customizing-with-minecraft-particle-effects-and-trails\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Customizing with Minecraft Particle Effects and Trails\\\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\\\n\\\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\\\n\\\\n*See also: <a href=\\\\\\\"/available-particle-effects/\\\\\\\">Available Particle Effects*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Do particles give me any powers?\\\",\\\"content\\\":\\\"<p>No, they are purely visual and just for fun.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How do I turn particles off?\\\",\\\"content\\\":\\\"<p>Use the `/pp` menu to disable or change your effects.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"575862bb-69a8-4ab4-9ee9-725174d12c1a\",\"comment_id\":\"customizing-with-minecraft-particle-effects-and-trails\",\"html\":\"<!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Personalize your character with particles, trails, and cosmetic visual effects in-game.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"5":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db66a470-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f9609a\",\"title\":\"Setting Nicknames and Pronouns on the Minecraft Server\",\"slug\":\"setting-nicknames-and-pronouns-on-the-minecraft-server\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Setting Nicknames and Pronouns on the Minecraft Server\\\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\\\n\\\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I use any name I want?\\\",\\\"content\\\":\\\"<p>You can pick any appropriate nickname that follows the server rules.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How do I set my pronouns?\\\",\\\"content\\\":\\\"<p>Use the pronoun setting command or ask staff for help.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"1f86ed72-3eba-46e8-8fe7-53bd7a08f41c\",\"comment_id\":\"setting-nicknames-and-pronouns-on-the-minecraft-server\",\"html\":\"<!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html--><!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html--><!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"6":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"db6bd490-77ad-11f0-9eff-43b340db85c2","context":"{\"id\":\"689b8e0841176f9a18f9609b\",\"title\":\"Getting Married and Setting Partnerships in Minecraft Multiplayer\",\"slug\":\"getting-married-and-setting-partnerships-in-minecraft-multiplayer\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Getting Married and Setting Partnerships in Minecraft Multiplayer\\\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\\\n\\\\nPlayers can get \\\\\\\"married\\\\\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\\\n\\\\n*See also: <a href=\\\\\\\"/marriage-commands/\\\\\\\">Marriage Commands</a>, <a href=\\\\\\\"/marital-perks/\\\\\\\">Marital Perks*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Do I have to be married to teleport to someone?\\\",\\\"content\\\":\\\"<p>No, but married players get instant access to partner teleport features.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I get divorced or remarried?\\\",\\\"content\\\":\\\"<p>Yes, use `/divorce` if you want to end a partnership and start a new one.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689b8e0841176f9a18f9608c\"}],\"uuid\":\"9597a26d-1aed-4a75-a884-5b15c7ea00af\",\"comment_id\":\"getting-married-and-setting-partnerships-in-minecraft-multiplayer\",\"html\":\"<!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Team up with a partner in-game to share homes, teleport, and unlock marriage features.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","msg":"Content import was unsuccessful","time":"2025-08-12T18:55:04.311Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","err":{"id":"f2bbd910-77ad-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":null,"name":"UnhandledJobError","statusCode":500,"level":"critical","message":"Processed job threw an unhandled error","context":"\"function\"","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"Error: Sending failed\n    at /Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/job-manager/lib/JobManager.js:267:35\n    at createMailError (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/mail/GhostMailer.js:81:12)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:157:41)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:201:22)\n    at QueryReqWrap.callback (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:474:24)\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/callback_resolver:45:10)","hideStack":false},"msg":"Processed job threw an unhandled error","time":"2025-08-12T18:55:43.398Z","v":0}
{"0":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba362c20-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960dd\",\"title\":\"How to Claim Land and Build Kingdoms in Minecraft Multiplayer\",\"slug\":\"how-to-claim-land-and-build-kingdoms-in-minecraft-multiplayer\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\\\n\\\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\\\n\\\\n*See also: <a href=\\\\\\\"/kingdom-management/\\\\\\\">Kingdom Management</a>, <a href=\\\\\\\"/region-flags/\\\\\\\">Region Flags*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can someone else build in my kingdom without permission?\\\",\\\"content\\\":\\\"<p>No. Only people you invite and give permissions to can build in your claimed land.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"40153bd2-de7a-4e7b-ae65-3a96834cf6d4\",\"comment_id\":\"how-to-claim-land-and-build-kingdoms-in-minecraft-multiplayer\",\"html\":\"<!--kg-card-begin: html-->How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\n\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\n\\n*See also: <a href=\\\"/kingdom-management/\\\">Kingdom Management</a>, <a href=\\\"/region-flags/\\\">Region Flags*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Claim Land and Build Kingdoms in Minecraft Multiplayer\\n*Meta Description: Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.*\\n\\nIn our survival world, it’s important to protect your builds from being broken or taken by others. You can create your own area called a **towncenter** which marks the land as yours and keeps it safe. You can also invite friends to join your town, and together you can expand and manage the space. This system makes it easy to build villages and collaborate with other players.\\n\\n*See also: <a href=\\\"/kingdom-management/\\\">Kingdom Management</a>, <a href=\\\"/region-flags/\\\">Region Flags*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Protect your builds and create towns by claiming land and forming kingdoms on our Minecraft server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"1":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba36a150-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960de\",\"title\":\"How to Set a Home and Teleport in Minecraft Survival\",\"slug\":\"how-to-set-a-home-and-teleport-in-minecraft-survival\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"How to Set a Home and Teleport in Minecraft Survival\\\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\\\n\\\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\\\n\\\\n*See also: <a href=\\\\\\\"/setting-homes/\\\\\\\">Setting Homes</a>, <a href=\\\\\\\"/using-rtp/\\\\\\\">Using `/rtp`*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"What if I forget where my home is?\\\",\\\"content\\\":\\\"<p>If you've set your home using `/sethome`, you can always return using `/home`.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I have more than one home?\\\",\\\"content\\\":\\\"<p>Yes! You can use `/sethome <name>` to create multiple homes and return to them with `/home <name>`.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"eba5a568-336a-45da-86b6-f730e98368d5\",\"comment_id\":\"how-to-set-a-home-and-teleport-in-minecraft-survival\",\"html\":\"<!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html--><!--kg-card-begin: html-->How to Set a Home and Teleport in Minecraft Survival\\n*Meta Description: Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.*\\n\\nYou don’t always have to walk everywhere! You can set a home by standing somewhere and typing `/sethome`. Then, if you get lost or go exploring, just type `/home` and you’ll teleport back. Use `/spawn` to return to the server’s main hub, or try `/rtp` to be safely teleported to a random location in the survival world. It’s great for finding a fresh place to build.\\n\\n*See also: <a href=\\\"/setting-homes/\\\">Setting Homes</a>, <a href=\\\"/using-rtp/\\\">Using `/rtp`*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Learn how to use /sethome, /home, /spawn, and /rtp commands to teleport around the Minecraft server easily.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"2":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba3ce2e0-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960e3\",\"title\":\"Minecraft Farming Rules and Animal Limits Explained\",\"slug\":\"minecraft-farming-rules-and-animal-limits-explained\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Minecraft Farming Rules and Animal Limits Explained\\\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\\\n\\\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\\\n\\\\n*See also: <a href=\\\\\\\"/crop-farming-rules/\\\\\\\">Crop Farming Rules*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How many animals can I have?\\\",\\\"content\\\":\\\"<p>The limit varies, but aim for no more than 15-20 of each animal in one spot.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Will my crops be affected?\\\",\\\"content\\\":\\\"<p>Crops are fine to grow, just don't overbuild huge auto-farms in one location.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"80ebd98c-82ae-4bc2-a171-68823eb9de33\",\"comment_id\":\"minecraft-farming-rules-and-animal-limits-explained\",\"html\":\"<!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Minecraft Farming Rules and Animal Limits Explained\\n*Meta Description: Avoid lag and keep your farms running smoothly with these farming and animal limits.*\\n\\nTo keep the server running smoothly, there are limits on how many animals or mobs you can keep in one area. If you try to raise too many cows or chickens in a small space, it could cause lag or some animals might disappear. Spread out your farms and only keep what you need. This helps everyone enjoy a smoother game.\\n\\n*See also: <a href=\\\"/crop-farming-rules/\\\">Crop Farming Rules*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Avoid lag and keep your farms running smoothly with these farming and animal limits.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"3":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba4176c0-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960e7\",\"title\":\"Avatar Bending Powers in Minecraft: How to Choose and Use Elements\",\"slug\":\"avatar-bending-powers-in-minecraft-how-to-choose-and-use-elements\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\\\n\\\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\\\n\\\\n*See also: <a href=\\\\\\\"/bending-gui/\\\\\\\">Bending GUI</a>, <a href=\\\\\\\"/element-descriptions/\\\\\\\">Element Descriptions*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I change my element later?\\\",\\\"content\\\":\\\"<p>Yes, but some changes may require a cooldown or an item—ask staff if unsure.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Are bending moves balanced?\\\",\\\"content\\\":\\\"<p>Yes, each element has strengths and weaknesses to keep PvP fair.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"9fb02724-bbeb-4eda-b387-de180a725bc2\",\"comment_id\":\"avatar-bending-powers-in-minecraft-how-to-choose-and-use-elements\",\"html\":\"<!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Avatar Bending Powers in Minecraft: How to Choose and Use Elements\\n*Meta Description: Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.*\\n\\nIf you’ve watched *Avatar: The Last Airbender*, you’ll love our bending system! You can choose an element like Water, Fire, Earth, or Air and use bending powers to fight or move around. Use `/bending gui` to pick your element and select moves. It adds a magical twist to Minecraft combat and building.\\n\\n*See also: <a href=\\\"/bending-gui/\\\">Bending GUI</a>, <a href=\\\"/element-descriptions/\\\">Element Descriptions*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Use elemental bending like Water, Earth, Fire, and Air in our Avatar-themed Minecraft server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"4":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba41ebf0-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960e8\",\"title\":\"Customizing with Minecraft Particle Effects and Trails\",\"slug\":\"customizing-with-minecraft-particle-effects-and-trails\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Customizing with Minecraft Particle Effects and Trails\\\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\\\n\\\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\\\n\\\\n*See also: <a href=\\\\\\\"/available-particle-effects/\\\\\\\">Available Particle Effects*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Do particles give me any powers?\\\",\\\"content\\\":\\\"<p>No, they are purely visual and just for fun.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How do I turn particles off?\\\",\\\"content\\\":\\\"<p>Use the `/pp` menu to disable or change your effects.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"3ad75576-e64e-4e0c-aa21-20edae7f6d00\",\"comment_id\":\"customizing-with-minecraft-particle-effects-and-trails\",\"html\":\"<!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Customizing with Minecraft Particle Effects and Trails\\n*Meta Description: Personalize your character with particles, trails, and cosmetic visual effects in-game.*\\n\\nMake your character stand out with particle effects that follow you or decorate your builds. Open the cosmetics menu with `/pp` to choose from trails, swirls, wings, and more. These don’t change gameplay—they just look awesome.\\n\\n*See also: <a href=\\\"/available-particle-effects/\\\">Available Particle Effects*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Personalize your character with particles, trails, and cosmetic visual effects in-game.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"5":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba426120-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960e9\",\"title\":\"Setting Nicknames and Pronouns on the Minecraft Server\",\"slug\":\"setting-nicknames-and-pronouns-on-the-minecraft-server\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Setting Nicknames and Pronouns on the Minecraft Server\\\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\\\n\\\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I use any name I want?\\\",\\\"content\\\":\\\"<p>You can pick any appropriate nickname that follows the server rules.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"How do I set my pronouns?\\\",\\\"content\\\":\\\"<p>Use the pronoun setting command or ask staff for help.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"053bba6c-49ed-450a-ad15-3fc63f30ff6a\",\"comment_id\":\"setting-nicknames-and-pronouns-on-the-minecraft-server\",\"html\":\"<!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html--><!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html--><!--kg-card-begin: html-->Setting Nicknames and Pronouns on the Minecraft Server\\n*Meta Description: Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.*\\n\\nYou can change your display name with `/nick`, including colorful names with gradients. You can also set your pronouns to help others know how to refer to you in chat. This helps everyone feel welcome and respected.<!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Learn how to choose a nickname and display your pronouns on the Minecraft multiplayer server.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"6":{"statusCode":422,"errorType":"ValidationError","level":"normal","id":"ba42d650-784e-11f0-9eff-43b340db85c2","context":"{\"id\":\"689c9bed41176f9a18f960ea\",\"title\":\"Getting Married and Setting Partnerships in Minecraft Multiplayer\",\"slug\":\"getting-married-and-setting-partnerships-in-minecraft-multiplayer\",\"mobiledoc\":\"{\\\"version\\\":\\\"0.3.1\\\",\\\"atoms\\\":[],\\\"cards\\\":[[\\\"html\\\",{\\\"html\\\":\\\"Getting Married and Setting Partnerships in Minecraft Multiplayer\\\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\\\n\\\\nPlayers can get \\\\\\\"married\\\\\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\\\n\\\\n*See also: <a href=\\\\\\\"/marriage-commands/\\\\\\\">Marriage Commands</a>, <a href=\\\\\\\"/marital-perks/\\\\\\\">Marital Perks*</a>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Do I have to be married to teleport to someone?\\\",\\\"content\\\":\\\"<p>No, but married players get instant access to partner teleport features.</p>\\\"}],[\\\"toggle\\\",{\\\"heading\\\":\\\"Can I get divorced or remarried?\\\",\\\"content\\\":\\\"<p>Yes, use `/divorce` if you want to end a partnership and start a new one.</p>\\\"}]],\\\"markups\\\":[],\\\"sections\\\":[[10,0],[10,0],[10,0]],\\\"ghostVersion\\\":\\\"3.0\\\"}\",\"status\":\"draft\",\"created_at\":\"2025-08-12T18:38:24.000Z\",\"updated_at\":\"2025-08-12T18:38:24.000Z\",\"published_at\":null,\"tags\":[{\"id\":\"689c9bed41176f9a18f960db\"}],\"uuid\":\"f4081aa7-85b9-4c33-a7c8-0f4d5e08b623\",\"comment_id\":\"getting-married-and-setting-partnerships-in-minecraft-multiplayer\",\"html\":\"<!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html--><!--kg-card-begin: html-->Getting Married and Setting Partnerships in Minecraft Multiplayer\\n*Meta Description: Team up with a partner in-game to share homes, teleport, and unlock marriage features.*\\n\\nPlayers can get \\\"married\\\" on the server by using `/marry <player>`. This lets you teleport to your partner with `/marry tp`, share a home, and talk in private chat. It’s a fun way to stay connected with friends or loved ones.\\n\\n*See also: <a href=\\\"/marriage-commands/\\\">Marriage Commands</a>, <a href=\\\"/marital-perks/\\\">Marital Perks*</a><!--kg-card-end: html-->\",\"posts_meta\":{\"id\":null,\"post_id\":null,\"og_image\":null,\"og_title\":null,\"og_description\":null,\"twitter_image\":null,\"twitter_title\":null,\"twitter_description\":null,\"meta_title\":null,\"meta_description\":\"Team up with a partner in-game to share homes, teleport, and unlock marriage features.\",\"email_subject\":null,\"frontmatter\":null,\"feature_image_alt\":null,\"feature_image_caption\":null,\"email_only\":false}}","code":null,"property":null,"redirect":null,"hideStack":false,"message":"Value in [posts.comment_id] exceeds maximum length of 50 characters.","name":"ValidationError"},"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","msg":"Content import was unsuccessful","time":"2025-08-13T14:06:37.631Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","err":{"id":"d9e34da0-784e-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":null,"name":"UnhandledJobError","statusCode":500,"level":"critical","message":"Processed job threw an unhandled error","context":"\"function\"","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"Error: Sending failed\n    at /Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/job-manager/lib/JobManager.js:267:35\n    at createMailError (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/mail/GhostMailer.js:81:12)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:157:41)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:201:22)\n    at QueryReqWrap.callback (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:474:24)\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/callback_resolver:45:10)","hideStack":false},"msg":"Processed job threw an unhandled error","time":"2025-08-13T14:07:30.687Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","err":{"id":"ab2036d0-784f-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":null,"name":"UnhandledJobError","statusCode":500,"level":"critical","message":"Processed job threw an unhandled error","context":"\"function\"","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"Error: Sending failed\n    at /Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/@tryghost/job-manager/lib/JobManager.js:267:35\n    at createMailError (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/mail/GhostMailer.js:81:12)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:157:41)\n    at DirectMailer.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:201:22)\n    at QueryReqWrap.callback (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/node_modules/nodemailer-direct-transport/lib/direct-transport.js:474:24)\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/callback_resolver:45:10)","hideStack":false},"msg":"Processed job threw an unhandled error","time":"2025-08-13T14:13:21.729Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","req":{"meta":{"requestId":"a3006b9c-e00c-4f57-8002-01d01d84a995","userId":null},"url":"/pages/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext&limit=all","method":"GET","originalUrl":"/ghost/api/content/pages/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext&limit=all","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**","fields":"title,url,excerpt,plaintext","limit":100}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v6.0","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-5tjAfCTT86mkYhYQ9UU4mbWCHoI\""},"statusCode":401,"responseTime":"9ms"},"err":{"id":"4a1771a0-798f-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-08-15T04:21:17.886Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","req":{"meta":{"requestId":"3f638a98-fe25-4e6d-b67f-78f23f7a9e66","userId":null},"url":"/pages/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","method":"GET","originalUrl":"/ghost/api/content/pages/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**","fields":"title,url,excerpt,plaintext,html","limit":100}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v6.0","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-LXwnlQemmSwktInYA9g/WNAuM5w\""},"statusCode":401,"responseTime":"53ms"},"err":{"id":"0673d570-79ed-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-08-15T15:32:17.107Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","req":{"meta":{"requestId":"c9671933-f1e2-4568-87ce-a193f8e694fd","userId":null},"url":"/posts/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","method":"GET","originalUrl":"/ghost/api/content/posts/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**","fields":"title,url,excerpt,plaintext,html","limit":100}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v6.0","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-EztZmN8mlf76Y25Y2rtupi3dGDw\""},"statusCode":401,"responseTime":"23ms"},"err":{"id":"0675f850-79ed-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-08-15T15:32:17.110Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","req":{"meta":{"requestId":"e1f5ceab-c5d3-4b99-94fb-01164033ff34","userId":null},"url":"/pages/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","method":"GET","originalUrl":"/ghost/api/content/pages/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**","fields":"title,url,excerpt,plaintext,html","limit":100}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v6.0","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-nEnrfSep3z7Th7KGAwJ8seMvQkU\""},"statusCode":401,"responseTime":"15ms"},"err":{"id":"073f14b0-79ed-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-08-15T15:32:18.428Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":39448,"level":50,"version":"6.0.0","req":{"meta":{"requestId":"8605beaa-9f3f-413a-a66f-7b781af64e91","userId":null},"url":"/posts/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","method":"GET","originalUrl":"/ghost/api/content/posts/?key=9a41b1e7087364643a05c513cf&fields=title,url,excerpt,plaintext,html&limit=all","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**","fields":"title,url,excerpt,plaintext,html","limit":100}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v6.0","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-6xq6SJ+MrL6UoVtxwg8RNVcU2+w\""},"statusCode":401,"responseTime":"8ms"},"err":{"id":"073f62d0-79ed-11f0-9eff-43b340db85c2","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/6.0.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-08-15T15:32:18.431Z","v":0}
