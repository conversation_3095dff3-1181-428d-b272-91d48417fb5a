{{!< default}}

{{#post}}

<main class="site-main">
    {{#match @page.show_title_and_feature_image}}
        <header class="gh-article-header gh-canvas">
            <h1 class="gh-article-title">{{title}}</h1>
            {{> "post-image"}}
        </header>
    {{/match}}

    <div class="wiki-page-layout gh-canvas">
        <!-- Left Column: Page Content -->
        <div class="wiki-page-content">
            <article class="{{post_class}}">
                <div class="post-content gh-content">
                    {{content}}
                </div>
            </article>
        </div>

        <!-- Right Column: Related Posts -->
        <aside class="wiki-page-sidebar">
            <div class="related-posts-section">
                <h3 class="related-posts-title">Related Posts</h3>

                {{#get "posts" filter="tag:[{{#foreach tags}}{{slug}}{{#unless @last}},{{/unless}}{{/foreach}}]+id:-{{id}}" limit="10"}}
                    {{#if posts}}
                        <div class="related-posts-list">
                            {{#foreach posts}}
                                <article class="related-post-card">
                                    <a href="{{url}}" class="related-post-link">
                                        {{#if feature_image}}
                                            <div class="related-post-image">
                                                <img src="{{img_url feature_image size="s"}}" alt="{{title}}" loading="lazy">
                                            </div>
                                        {{/if}}
                                        <div class="related-post-content">
                                            <h4 class="related-post-title">{{title}}</h4>
                                            {{#if excerpt}}
                                                <p class="related-post-excerpt">{{excerpt words="15"}}</p>
                                            {{/if}}
                                            <div class="related-post-meta">
                                                <time class="related-post-date">{{date format="MMM DD, YYYY"}}</time>
                                                {{#if tags}}
                                                    <div class="related-post-tags">
                                                        {{#foreach tags limit="2"}}
                                                            <span class="related-post-tag">{{name}}</span>
                                                        {{/foreach}}
                                                    </div>
                                                {{/if}}
                                            </div>
                                        </div>
                                    </a>
                                </article>
                            {{/foreach}}
                        </div>
                    {{else}}
                        <p class="no-related-posts">No related posts found.</p>
                    {{/if}}
                {{/get}}
            </div>
        </aside>
    </div>

</main>

<style>
.wiki-page-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-top: 2rem;
}

.wiki-page-content {
    min-width: 0; /* Prevents overflow issues */
}

.wiki-page-sidebar {
    position: sticky;
    top: 2rem;
    height: fit-content;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.related-posts-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.related-posts-title {
    margin: 0 0 1.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #fff;
}

.related-posts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-post-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.related-post-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.15);
}

.related-post-link {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
}

.related-post-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
}

.related-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-post-content {
    flex: 1;
    min-width: 0;
}

.related-post-title {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #fff;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-post-excerpt {
    margin: 0 0 0.75rem 0;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-post-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.related-post-date {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.5);
}

.related-post-tags {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.related-post-tag {
    font-size: 0.7rem;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-related-posts {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    text-align: center;
    margin: 2rem 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .wiki-page-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .wiki-page-sidebar {
        position: static;
        max-height: none;
    }
}

@media (max-width: 768px) {
    .related-post-link {
        flex-direction: column;
        gap: 0.75rem;
    }

    .related-post-image {
        width: 100%;
        height: 120px;
    }
}
</style>

{{/post}}