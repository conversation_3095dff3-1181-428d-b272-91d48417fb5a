{{!< default}}

{{#post}}

<main class="site-main">
    <article class="{{post_class}}">

        {{#match @page.show_title_and_feature_image}}
            <header class="gh-article-header gh-canvas">
                <h1 class="gh-article-title">{{title}}</h1>
                {{> "post-image"}}
            </header>
        {{/match}}

        <div class="post-content left gh-content gh-canvas">
            {{content}}
        </div>

    </article>

    <div class="post-cards-grid gh-canvas">
        {{#get "pages" filter="tag:{{slug}}" limit="all"}}
            {{#foreach pages}}
                <article class="post-card glass-button">
                    <a href="{{url}}" class="post-card-link">
                        {{#if feature_image}}
                            <div class="post-card-image">
                                <img src="{{img_url feature_image size="m"}}" alt="{{title}}" loading="lazy">
                            </div>
                        {{/if}}
                        <div class="post-card-content">
                            <h3 class="post-card-title">{{title}}</h3>
                            <button class="post-card-button">Read more</button>

                        </div>
                    </a>
                </article>
            {{/foreach}}
        {{/get}}
        {{#get "posts" filter="tag:{{slug}}" limit="all"}}
            {{#foreach posts}}
                <article class="post-card glass-button">
                    <a href="{{url}}" class="post-card-link">
                        {{#if feature_image}}
                            <div class="post-card-image">
                                <img src="{{img_url feature_image size="m"}}" alt="{{title}}" loading="lazy">
                            </div>
                        {{/if}}
                        <div class="post-card-content">
                            <h3 class="post-card-title">{{title}}</h3>
                            <button class="post-card-button">Read more</button>

                        </div>
                    </a>
                </article>
            {{/foreach}}
        {{/get}}
    </div>

</main>


{{/post}}