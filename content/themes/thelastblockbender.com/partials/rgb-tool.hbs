<!-- RGB Nickname Tool - Ported from Qwik Components -->
<div class="rgb-tool" id="rgb-tool">
    <!-- Main Container -->
    <div class="rgb-container">
        
        <!-- Text Input Section -->
        <div class="rgb-section">
            <div class="rgb-input-wrapper">
                <div class="rgb-preview-container" id="rgb-preview-container">
                    <div class="rgb-preview-text" id="rgb-preview-text">Gerald</div>
                    <textarea 
                        id="rgb-text-input" 
                        class="rgb-text-overlay" 
                        placeholder="Enter your nickname..."
                        spellcheck="false">Gerald</textarea>
                </div>
                
                <!-- Preview Style Selector -->
                <div class="rgb-preview-controls" style="display: none;">
                    <label for="rgb-preview-style">Preview Style:</label>
                    <select id="rgb-preview-style">
                        <option value="default">Default</option>
                        <option value="chat">Minecraft Chat</option>
                        <option value="tab-header">Tab Header</option>
                        <option value="tab-footer">Tab Footer</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Output Section -->
        <div class="rgb-section">
            <h3>Output</h3>
            <div class="rgb-output-container">
                <textarea id="rgb-output" readonly placeholder="Generated RGB text will appear here..."></textarea>
                <button type="button" id="rgb-copy" class="rgb-copy-btn">Copy</button>
            </div>
        </div>

        <!-- Color Configuration -->
        <div class="rgb-section">
            <h3>Colors</h3>
            
            <!-- Color Amount -->
            <div class="rgb-control-group">
                <label for="rgb-color-amount">Number of colors</label>
                <div class="rgb-number-input">
                    <button type="button" id="rgb-color-decrease">-</button>
                    <input type="number" id="rgb-color-amount" value="2" min="2" max="10">
                    <button type="button" id="rgb-color-increase">+</button>
                </div>
            </div>

            <!-- Color List -->
            <div class="rgb-color-list" id="rgb-color-list">
                <!-- Colors will be dynamically generated -->
            </div>

            <!-- Color Map -->
            <div class="rgb-color-map" id="rgb-color-map">
                <!-- Gradient bar will be generated -->
            </div>

            <!-- Color Controls -->
            <div class="rgb-color-controls">
                <button type="button" id="rgb-randomize">🎲 Randomize</button>
                <button type="button" id="rgb-disperse" style="display: none;">⋯ Disperse</button>
            </div>
        </div>

        <!-- Format Options -->
        <div class="rgb-section">
            <h3>Options</h3>
            
            <div class="rgb-format-grid" style="display: none;">
                <!-- Format Selection -->
                <div class="rgb-control-group" style="display: none;">
                    <label for="rgb-format">Color Format:</label>
                    <select id="rgb-format">
                        <option value="hex">Hex (&#RRGGBB)</option>
                        <option value="bukkit">Bukkit (&x&R&R&G&G&B&B)</option>
                        <option value="minimessage">MiniMessage</option>
                        <option value="custom">Custom Format</option>
                    </select>
                </div>

                <!-- Prefix/Suffix (hardcoded) -->
                <div class="rgb-control-group" style="display: none;">
                    <label>Command Format:</label>
                    <div class="rgb-prefix-display">/nick $t</div>
                </div>

                <!-- Custom Format (hidden by default) -->
                <div class="rgb-control-group rgb-custom-format" id="rgb-custom-format" style="display: none;">
                    <label for="rgb-custom-format-input">Custom Format:</label>
                    <input type="text" id="rgb-custom-format-input" placeholder="&#$1$2$3$4$5$6$f$c">
                    <small>Use $1-$6 for hex digits, $f for formatting, $c for character</small>
                </div>
            </div>

            <!-- Formatting Options -->
            <div class="rgb-formatting">
                <div class="rgb-checkbox-group">
                    <label><input type="checkbox" id="rgb-bold"> Bold</label>
                    <label><input type="checkbox" id="rgb-italic"> Italic</label>
                    <label><input type="checkbox" id="rgb-underline"> Underline</label>
                    <label><input type="checkbox" id="rgb-strikethrough"> Strikethrough</label>
                    <label><input type="checkbox" id="rgb-obfuscate"> Obfuscated</label>
                </div>
            </div>

            <!-- Advanced Options -->
            <div class="rgb-advanced" style="display: none;">
                <h4>Advanced Options</h4>
                <div class="rgb-checkbox-group">
                    <label><input type="checkbox" id="rgb-disperse-always"> Always Disperse Colors</label>
                    <label><input type="checkbox" id="rgb-trim-spaces"> Trim Colors from Spaces</label>
                    <label><input type="checkbox" id="rgb-lowercase"> Lowercase Hex Codes</label>
                </div>
            </div>
        </div>

        

        <!-- Presets Section -->
        <div class="rgb-section" style="display: none;">
            <h3>Presets</h3>
            <div class="rgb-preset-grid">
                <button class="rgb-preset" data-preset="fire">🔥 Fire</button>
                <button class="rgb-preset" data-preset="ocean">🌊 Ocean</button>
                <button class="rgb-preset" data-preset="forest">🌲 Forest</button>
                <button class="rgb-preset" data-preset="sunset">🌅 Sunset</button>
                <button class="rgb-preset" data-preset="neon">⚡ Neon</button>
                <button class="rgb-preset" data-preset="rainbow">🌈 Rainbow</button>
            </div>
        </div>
    </div>
</div>

<style>
/* RGB Tool Styles - Ported from original components */
.rgb-tool {
    margin: 2rem auto;
    padding: 2rem;
}

.rgb-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.rgb-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.rgb-section h3 {
    margin: 0 0 20px 0;
    color: #fff;
    font-size: 26px;
    font-weight: 600;
}

.rgb-section h4 {
    margin: 1rem 0 0.5rem 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 500;
}

/* Input Section */
.rgb-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.rgb-preview-container {
    position: relative;
    min-height: 80px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.rgb-preview-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1rem;
    font-size: 2rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    white-space: pre-wrap;
    word-break: break-all;
}

.rgb-text-overlay {
    text-align: center;
    position: absolute;
    top: 13px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1rem;
    background: transparent;
    border: none;
    color: transparent;
    font-size: 2rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    resize: none;
    outline: none;
    caret-color: white;
    white-space: pre-wrap;
    word-break: break-all;
}

.rgb-preview-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rgb-preview-controls label {
    color: #fff;
    font-weight: 500;
}

.rgb-preview-controls select {
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

/* Control Groups */
.rgb-control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rgb-control-group label {
    color: #fff;
    font-weight: 500;
    font-size: 16px;
}

.rgb-control-group input,
.rgb-control-group select {
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1rem;
}

.rgb-control-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.rgb-control-group small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.rgb-prefix-display {
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
    font-size: 1rem;
}

/* Number Input */
.rgb-number-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rgb-number-input button {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rgb-number-input button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.rgb-number-input input {
    flex: 1;
    text-align: center;
    margin: 0;
}

/* Color List */
.rgb-color-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 1rem 0;
}

.rgb-color-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.rgb-color-picker {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
}

.rgb-color-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-family: 'Courier New', monospace;
}

/* Color Map */
.rgb-color-map {
    height: 20px;
    border-radius: 10px;
    margin: 1rem 0;
    position: relative;
    cursor: crosshair;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Format Grid */
.rgb-format-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Checkbox Groups */
.rgb-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
}

.rgb-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    font-size: 18px;
    margin-right: 20px;
    cursor: pointer;
}

.rgb-checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
}

/* Output */
.rgb-output-container {
    display: flex;
    gap: 0.5rem;
}

.rgb-output-container textarea {
    flex: 1;
    padding: 1rem;
    height: 50px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-family: 'Courier New', monospace;
    font-size: 16px;
    resize: none;
}

.rgb-copy-btn {
    padding: 1rem;
    background: rgba(0, 150, 255, 0.8);
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.rgb-copy-btn:hover {
    background: rgba(0, 150, 255, 1);
    transform: translateY(-2px);
}

/* Buttons */
.rgb-color-controls,
.rgb-preset-controls {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.rgb-color-controls button,
.rgb-preset-controls button {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 18px;
}

.rgb-color-controls button:hover,
.rgb-preset-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Preset Grid */
.rgb-preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.rgb-preset {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    text-align: center;
}

.rgb-preset:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}



/* Responsive */
@media (max-width: 768px) {
    .rgb-tool {
        margin: 1rem;
        padding: 1rem;
    }
    
    .rgb-format-grid {
        grid-template-columns: 1fr;
    }
    
    .rgb-checkbox-group {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .rgb-output-container {
        flex-direction: column;
    }
    
    .rgb-preset-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .rgb-color-controls,
    .rgb-preset-controls {
        justify-content: center;
    }
}
</style>

<script>
// RGB Tool JavaScript - Ported from Qwik Components
document.addEventListener('DOMContentLoaded', function() {

    // State management (similar to rgbStore context)
    const rgbState = {
        text: 'Gerald',
        colors: [
            { hex: '#ff0000', pos: 0 },
            { hex: '#0000ff', pos: 100 }
        ],
        format: {
            color: 'hex',
            char: '&',
            bold: '',
            italic: '',
            underline: '',
            strikethrough: '',
            obfuscate: ''
        },
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false,
        obfuscate: false,
        prefixsuffix: '/nick $t', // Hardcoded - not user changeable
        customFormat: false,
        disperse: false,
        trimspaces: true,
        lowercase: false,
        colorlength: 1,
        previewStyle: 'default'
    };

    // Predefined formats (from original defaults)
    const formats = {
        hex: { color: '&#$1$2$3$4$5$6', char: '&' },
        bukkit: { color: '&x&$1&$2&$3&$4&$5&$6', char: '&' },
        minimessage: {
            color: '<#$1$2$3$4$5$6>',
            bold: '<bold>$t</bold>',
            italic: '<italic>$t</italic>',
            underline: '<underlined>$t</underlined>',
            strikethrough: '<strikethrough>$t</strikethrough>',
            obfuscate: '<obfuscated>$t</obfuscated>'
        }
    };

    // Preset configurations
    const presets = {
        fire: { colors: [{ hex: '#ff4500', pos: 0 }, { hex: '#ff0000', pos: 50 }, { hex: '#ffff00', pos: 100 }] },
        ocean: { colors: [{ hex: '#0066cc', pos: 0 }, { hex: '#00ccff', pos: 100 }] },
        forest: { colors: [{ hex: '#228b22', pos: 0 }, { hex: '#90ee90', pos: 100 }] },
        sunset: { colors: [{ hex: '#ff6347', pos: 0 }, { hex: '#ffd700', pos: 50 }, { hex: '#ff4500', pos: 100 }] },
        neon: { colors: [{ hex: '#ff00ff', pos: 0 }, { hex: '#00ffff', pos: 100 }] },
        rainbow: { colors: [
            { hex: '#ff0000', pos: 0 },
            { hex: '#ff8000', pos: 16.67 },
            { hex: '#ffff00', pos: 33.33 },
            { hex: '#80ff00', pos: 50 },
            { hex: '#00ff80', pos: 66.67 },
            { hex: '#0080ff', pos: 83.33 },
            { hex: '#8000ff', pos: 100 }
        ]}
    };

    // DOM elements
    const elements = {
        textInput: document.getElementById('rgb-text-input'),
        previewText: document.getElementById('rgb-preview-text'),
        previewStyle: document.getElementById('rgb-preview-style'),
        colorAmount: document.getElementById('rgb-color-amount'),
        colorList: document.getElementById('rgb-color-list'),
        colorMap: document.getElementById('rgb-color-map'),
        formatSelect: document.getElementById('rgb-format'),
        customFormat: document.getElementById('rgb-custom-format'),
        customFormatInput: document.getElementById('rgb-custom-format-input'),
        output: document.getElementById('rgb-output'),
        copyBtn: document.getElementById('rgb-copy'),

        // Checkboxes
        bold: document.getElementById('rgb-bold'),
        italic: document.getElementById('rgb-italic'),
        underline: document.getElementById('rgb-underline'),
        strikethrough: document.getElementById('rgb-strikethrough'),
        obfuscate: document.getElementById('rgb-obfuscate'),
        disperseAlways: document.getElementById('rgb-disperse-always'),
        trimSpaces: document.getElementById('rgb-trim-spaces'),
        lowercase: document.getElementById('rgb-lowercase'),

        // Buttons
        colorDecrease: document.getElementById('rgb-color-decrease'),
        colorIncrease: document.getElementById('rgb-color-increase'),
        randomize: document.getElementById('rgb-randomize'),
        disperse: document.getElementById('rgb-disperse')
    };

    // Utility functions (ported from original utils)
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    function rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    function interpolateColor(color1, color2, factor) {
        const rgb1 = hexToRgb(color1);
        const rgb2 = hexToRgb(color2);

        const r = Math.round(rgb1.r + factor * (rgb2.r - rgb1.r));
        const g = Math.round(rgb1.g + factor * (rgb2.g - rgb1.g));
        const b = Math.round(rgb1.b + factor * (rgb2.b - rgb1.b));

        return rgbToHex(r, g, b);
    }

    function getRandomColor() {
        return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
    }

    function sortColors(colors) {
        return [...colors].sort((a, b) => a.pos - b.pos);
    }

    function disperseColors(colors) {
        const sorted = sortColors(colors);
        return sorted.map((color, i) => ({
            ...color,
            pos: (100 / (sorted.length - 1)) * i
        }));
    }

    // Generate gradient colors for text
    function generateGradient(text, colors) {
        if (!text || colors.length === 0) return [];

        const sortedColors = sortColors(colors);
        const result = [];

        for (let i = 0; i < text.length; i++) {
            const position = text.length === 1 ? 0 : (i / (text.length - 1)) * 100;

            // Find the correct color segment for this position
            let color1, color2, factor;

            if (position <= sortedColors[0].pos) {
                // Before first color
                color1 = sortedColors[0];
                color2 = sortedColors[0];
                factor = 0;
            } else if (position >= sortedColors[sortedColors.length - 1].pos) {
                // After last color
                color1 = sortedColors[sortedColors.length - 1];
                color2 = sortedColors[sortedColors.length - 1];
                factor = 0;
            } else {
                // Find the segment this position falls into
                for (let j = 0; j < sortedColors.length - 1; j++) {
                    if (position >= sortedColors[j].pos && position <= sortedColors[j + 1].pos) {
                        color1 = sortedColors[j];
                        color2 = sortedColors[j + 1];

                        // Calculate interpolation factor within this segment
                        const segmentRange = color2.pos - color1.pos;
                        factor = segmentRange === 0 ? 0 : (position - color1.pos) / segmentRange;
                        break;
                    }
                }
            }

            const interpolatedColor = interpolateColor(color1.hex, color2.hex, factor);

            result.push({
                char: text[i],
                color: interpolatedColor,
                position: position
            });
        }

        return result;
    }

    // Generate output text (ported from generateOutput function)
    function generateOutput() {
        const text = rgbState.text;
        if (!text) return '';

        const gradient = generateGradient(text, rgbState.colors);
        let output = '';

        const format = rgbState.customFormat ?
            { color: elements.customFormatInput.value } :
            formats[rgbState.format.color] || formats.hex;

        gradient.forEach(item => {
            if (rgbState.trimspaces && item.char === ' ') {
                output += item.char;
                return;
            }

            const hex = rgbState.lowercase ?
                item.color.toLowerCase().replace('#', '') :
                item.color.toUpperCase().replace('#', '');

            let colorCode = format.color
                .replace('$1', hex[0])
                .replace('$2', hex[1])
                .replace('$3', hex[2])
                .replace('$4', hex[3])
                .replace('$5', hex[4])
                .replace('$6', hex[5]);

            // Add formatting codes
            let formatCodes = '';
            if (rgbState.format.color === 'minimessage') {
                // MiniMessage format
                if (rgbState.bold) formatCodes += '<bold>';
                if (rgbState.italic) formatCodes += '<italic>';
                if (rgbState.underline) formatCodes += '<underlined>';
                if (rgbState.strikethrough) formatCodes += '<strikethrough>';
                if (rgbState.obfuscate) formatCodes += '<obfuscated>';
            } else {
                // Legacy format
                const char = format.char || '&';
                if (rgbState.bold) formatCodes += char + 'l';
                if (rgbState.italic) formatCodes += char + 'o';
                if (rgbState.underline) formatCodes += char + 'n';
                if (rgbState.strikethrough) formatCodes += char + 'm';
                if (rgbState.obfuscate) formatCodes += char + 'k';
            }

            output += colorCode + formatCodes + item.char;
        });

        // Close MiniMessage tags
        if (rgbState.format.color === 'minimessage') {
            if (rgbState.obfuscate) output += '</obfuscated>';
            if (rgbState.strikethrough) output += '</strikethrough>';
            if (rgbState.underline) output += '</underlined>';
            if (rgbState.italic) output += '</italic>';
            if (rgbState.bold) output += '</bold>';
        }

        // Apply prefix/suffix
        const finalOutput = rgbState.prefixsuffix.replace('$t', output);
        return finalOutput;
    }

    // Update preview display
    function updatePreview() {
        const gradient = generateGradient(rgbState.text, rgbState.colors);
        let previewHTML = '';

        gradient.forEach(item => {
            let style = `color: ${item.color};`;
            if (rgbState.bold) style += ' font-weight: bold;';
            if (rgbState.italic) style += ' font-style: italic;';
            if (rgbState.underline) style += ' text-decoration: underline;';
            if (rgbState.strikethrough) style += ' text-decoration: line-through;';

            previewHTML += `<span style="${style}">${item.char}</span>`;
        });

        elements.previewText.innerHTML = previewHTML;

        // Update output
        elements.output.value = generateOutput();
    }

    // Update color list display
    function updateColorList() {
        const container = elements.colorList;
        container.innerHTML = '';

        rgbState.colors.forEach((color, index) => {
            const item = document.createElement('div');
            item.className = 'rgb-color-item';

            item.innerHTML = `
                <input type="color" class="rgb-color-picker" value="${color.hex}" data-index="${index}">
                <input type="text" class="rgb-color-input" value="${color.hex}" data-index="${index}">
                <button type="button" class="rgb-color-remove" data-index="${index}">🗑️</button>
            `;

            container.appendChild(item);
        });

        // Add event listeners for color inputs
        container.querySelectorAll('.rgb-color-picker').forEach(picker => {
            picker.addEventListener('change', function() {
                const index = parseInt(this.dataset.index);
                rgbState.colors[index].hex = this.value;
                updateColorMap();
                updatePreview();
            });
        });

        container.querySelectorAll('.rgb-color-input').forEach(input => {
            input.addEventListener('input', function() {
                const index = parseInt(this.dataset.index);
                if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
                    rgbState.colors[index].hex = this.value;
                    updateColorMap();
                    updatePreview();
                }
            });
        });

        container.querySelectorAll('.rgb-color-remove').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                if (rgbState.colors.length > 2) {
                    rgbState.colors.splice(index, 1);
                    updateColorList();
                    updateColorMap();
                    updatePreview();
                }
            });
        });
    }

    // Update color map (gradient bar)
    function updateColorMap() {
        const sorted = sortColors(rgbState.colors);
        const gradient = sorted.map(color => `${color.hex} ${color.pos}%`).join(', ');
        elements.colorMap.style.background = `linear-gradient(to right, ${gradient})`;
    }

    // Initialize the tool
    function init() {
        // Set initial values
        elements.textInput.value = rgbState.text;
        elements.colorAmount.value = rgbState.colors.length;

        // Update displays
        updateColorList();
        updateColorMap();
        updatePreview();

        // Event listeners
        elements.textInput.addEventListener('input', function() {
            rgbState.text = this.value;
            updatePreview();
        });

        elements.formatSelect.addEventListener('change', function() {
            rgbState.format.color = this.value;
            rgbState.customFormat = this.value === 'custom';
            elements.customFormat.style.display = rgbState.customFormat ? 'block' : 'none';
            updatePreview();
        });

        elements.customFormatInput.addEventListener('input', function() {
            updatePreview();
        });

        // Formatting checkboxes
        ['bold', 'italic', 'underline', 'strikethrough', 'obfuscate'].forEach(format => {
            elements[format].addEventListener('change', function() {
                rgbState[format] = this.checked;
                updatePreview();
            });
        });

        // Advanced options
        elements.trimSpaces.addEventListener('change', function() {
            rgbState.trimspaces = this.checked;
            updatePreview();
        });

        elements.lowercase.addEventListener('change', function() {
            rgbState.lowercase = this.checked;
            updatePreview();
        });

        elements.disperseAlways.addEventListener('change', function() {
            rgbState.disperse = this.checked;
            if (this.checked) {
                rgbState.colors = disperseColors(rgbState.colors);
                updateColorList();
                updateColorMap();
                updatePreview();
            }
        });

        // Color amount controls
        elements.colorDecrease.addEventListener('click', function() {
            if (rgbState.colors.length > 2) {
                rgbState.colors.pop();
                elements.colorAmount.value = rgbState.colors.length;
                updateColorList();
                updateColorMap();
                updatePreview();
            }
        });

        elements.colorIncrease.addEventListener('click', function() {
            if (rgbState.colors.length < 10) {
                const newLength = rgbState.colors.length + 1;
                rgbState.colors.push({
                    hex: getRandomColor(),
                    pos: ((rgbState.colors.length) / (newLength - 1)) * 100
                });
                // Redistribute all colors evenly
                rgbState.colors = rgbState.colors.map((color, index) => ({
                    ...color,
                    pos: (index / (newLength - 1)) * 100
                }));
                elements.colorAmount.value = rgbState.colors.length;
                updateColorList();
                updateColorMap();
                updatePreview();
            }
        });

        elements.colorAmount.addEventListener('change', function() {
            const newAmount = parseInt(this.value);
            if (newAmount >= 2 && newAmount <= 10) {
                // Add colors if needed
                while (rgbState.colors.length < newAmount) {
                    rgbState.colors.push({
                        hex: getRandomColor(),
                        pos: 0 // Will be redistributed below
                    });
                }
                // Remove colors if needed
                while (rgbState.colors.length > newAmount) {
                    rgbState.colors.pop();
                }
                // Redistribute all colors evenly
                rgbState.colors = rgbState.colors.map((color, index) => ({
                    ...color,
                    pos: (index / (newAmount - 1)) * 100
                }));
                updateColorList();
                updateColorMap();
                updatePreview();
            }
        });

        // Randomize button
        elements.randomize.addEventListener('click', function() {
            rgbState.colors = rgbState.colors.map(color => ({
                ...color,
                hex: getRandomColor()
            }));
            updateColorList();
            updateColorMap();
            updatePreview();
        });

        // Disperse button
        elements.disperse.addEventListener('click', function() {
            rgbState.colors = disperseColors(rgbState.colors);
            updateColorList();
            updateColorMap();
            updatePreview();
        });

        // Copy button
        elements.copyBtn.addEventListener('click', function() {
            const outputText = elements.output.value;

            // Check if nickname is too long
            if (outputText.length > 160) {
                alert('⚠️ Warning: This nickname is ' + outputText.length + ' characters long!\n\n' +
                      'Minecraft nicknames longer than 160 characters might not work in-game. ' +
                      'Consider reducing the number of colors if you experience issues.');
            }

            elements.output.select();
            elements.output.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                this.textContent = 'Copied!';
                this.style.background = 'rgba(0, 200, 0, 0.8)';

                setTimeout(() => {
                    this.textContent = '📋 Copy';
                    this.style.background = 'rgba(0, 150, 255, 0.8)';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy text: ', err);
            }
        });

        // Preset buttons
        document.querySelectorAll('.rgb-preset').forEach(btn => {
            btn.addEventListener('click', function() {
                const presetName = this.dataset.preset;
                if (presets[presetName]) {
                    rgbState.colors = [...presets[presetName].colors];
                    elements.colorAmount.value = rgbState.colors.length;
                    updateColorList();
                    updateColorMap();
                    updatePreview();
                }
            });
        });
    }

    // Initialize the tool
    init();
});
</script>
