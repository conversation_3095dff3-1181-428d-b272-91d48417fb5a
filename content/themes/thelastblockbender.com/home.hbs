{{!< default}}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">

<style> 
.grid-container {
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(16, 1fr);
  gap: 22px 22px;
}

.grid-item {
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

/* Hover background overlay */
.grid-item .hover-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
  pointer-events: none;
}

.grid-item .hover-bg.fade-in {
  opacity: 1;
}


.item-1 {
  grid-column: 5 / 13;
  grid-row: 7 / 15;
}

.item-3 {
  grid-column: 13 / 17;
  grid-row: 1 / 7;
  align-items: end !important;
}

.item-6 {
  grid-column: 1 / 5;
  grid-row: 7 / 15;
}

.item-20 {
  grid-column: 1 / 5;
  grid-row: 15 / 20;
}

.item-7 {
  grid-column: 1 / 8;
  grid-row: 20 / 26;
}

.item-8 {
  grid-column: 5 / 7;
  grid-row: 15 / 20;
}

.item-9 {
  grid-column: 7 / 9;
  grid-row: 15 / 20;
}

.item-10 {
  grid-column: 9 / 11;
  grid-row: 15 / 20;
}

.item-11 {
  grid-column: 11 / 13;
  grid-row: 15 / 20;
}

.item-14 {
  grid-column: 8 / 13;
  grid-row: 20 / 26;
}

.item-15 {
  grid-column: 13 / 17;
  grid-row: 12 / 26;
}

.item-16 {
  grid-column: 13 / 17;
  grid-row: 7 / 12;
}

.item-17 {
  grid-column: 6 / 13;
  grid-row: 1 / 7;
}

.item-18 {
  grid-column: 1 / 6;
  grid-row: 1 / 4;
}

.item-19 {
  grid-column: 1 / 6;
  grid-row: 4 / 7;
}


/* custom overrides */


.grid-container {
    max-width: 1200px;
    margin: 0 auto;
}

body {
  background: url('grad.jpeg');
  background-size: cover;
}

.grid-item.social-button {
    padding: 16px 32px;
}

@media(max-width:1024px) {
   .grid-item.social-button {
    padding: 0px;
} 
}

.social-button img {
    margin: 0px 0px;
    opacity: 0.6;
}

.social-button:hover img {
    opacity: 1;
}

.java, .bedrock {
    display: block;
    text-align: center;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
}

.java h2, .bedrock h2 {
    font-weight: 900;
    font-size: 26px;
    margin-bottom: 15px;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
}
.java h4, .bedrock h4 {
    font-size: 14px;
    font-weight: 200;
  font-family: "Montserrat", sans-serif;
  font-weight: 200;
}

.discord {
    display: block;
    text-align: center;
}

.discord h2 {
    font-weight: 200;
    font-size: 26px;
    margin-top: 5px;
    margin-bottom: 15px;
    font-family: "Montserrat", sans-serif;
}
.discord h4 {
    font-size: 14px;
    font-weight: 600;
    font-family: "Montserrat", sans-serif;
}

.discord #onlineCount, .discord #totalCount {
 display: inline-block;
}

.server {
    display: block;
}

.server h1 {
    font-weight: 900;
    font-size: 26px;
    margin-bottom: 15px;
    display: block;
    width: 100%;
    text-align: center;
}

.server h5 {
    display: block;
    width: 100%;
    text-align: center;
}


/* Tooltip styles */
.copy-tooltip {
    position: absolute;
    bottom: 25%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 400;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.copy-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

.copy-tooltip.show {
    opacity: 1;
    visibility: visible;
}

.grid-item.disabled {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);

}

.grid-item.disabled h2, .grid-item.disabled h4 {
        color: rgba(255,255,255,0.4) !important;
}




.grid-item.item-1 img {
position: absolute;
 top: -40%;
width: 150%;
    max-width: none;
}



.grid-item {
  font-family: "Montserrat", sans-serif;
font-weight: 200;
    font-size: 26px;
    line-height: 26px;
    text-align: left;
    align-items: start;
    justify-content: left;
    padding: 20px 20px;
}

.grid-item h3 {
    text-align: left;
  font-family: "Montserrat", sans-serif;
font-weight: 300;
}

</style>



<main id="main">
    <div class="grid-container">
    <div class="grid-item item-1 server" data-link="/" data-hover-image="meow.png">
       <div class="hover-bg"></div>
       <h1>The Last BlockBender</h1> <h5>{{> server-players}}</h5>
    </div>
    <div class="grid-item glass-button item-3" data-link="/how-to-bend" data-hover-image="http://localhost:2368/content/images/2025/08/korra.png">
        <div class="hover-bg"></div>
        <h3>How<br> to<br> Bend</h3>
    </div>
    <div class="grid-item glass-button item-6" data-link="/wiki" data-hover-image="http://localhost:2368/content/images/2025/08/tenzin.png">
        <div class="hover-bg"></div>
        <h3>Wiki</h3>
    </div>
    <div class="grid-item glass-button item-7 discord" data-link="https://discord.thelastblockbender.com" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h2>Discord</h2>
        <h4>discord.blockbender.com | {{> discord-members}}</h4>
    </div>
    <div class="grid-item social-button item-8" data-link="https://www.instagram.com/thelastblockbendermc/" data-hover-image="meow.png">
       <div class="hover-bg"></div>
       <img src="{{asset 'images/instagram.png'}}" alt-"Instagram">
    </div>
    <div class="grid-item social-button item-9" data-link="https://www.youtube.com/@lastblockbender" data-hover-image="meow.png">
       <div class="hover-bg"></div>
       <img src="{{asset 'images/youtube.png'}}" alt-"Youtube">
    </div>
    <div class="grid-item social-button item-10" data-link="https://www.tiktok.com/@lastblockbender" data-hover-image="meow.png">
       <div class="hover-bg"></div>
       <img src="{{asset 'images/tiktok.png'}}" alt-"Tiktok">
    </div>
    <div class="grid-item social-button item-11" data-link="https://ca.pinterest.com/thelastblockbender/" data-hover-image="meow.png">
       <div class="hover-bg"></div>
       <img src="{{asset 'images/pinterest.png'}}" alt-"Pinterest">
    </div>
    <div class="grid-item glass-button item-14" data-link="/vote" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h3>Voting</h3>
    </div>
    <div class="grid-item glass-button item-15" data-link="https://store.thelastblockbender.com" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h3>Donation<br>Store</h3>
    </div>
    <div class="grid-item glass-button item-16" data-link="/faq" data-hover-image="http://localhost:2368/content/images/2025/08/bumi.png"">
        <div class="hover-bg"></div>
        <h3>Frequently<br>Asked<br> Questions</h3>
    </div>
    <div class="grid-item glass-button item-17" data-link="/how-to-join" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h3>How to Join</h3>
    </div>
    <div class="grid-item glass-button item-20" data-link="/updates" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h3>Updates</h3>
    </div>
    <div class="grid-item glass-button java item-18" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h2>play.blockbender.com</h2>
        <h4>Java Edition</h4>
        <div class="copy-tooltip">Copied to clipboard</div>
    </div>
    <div class="grid-item glass-button bedrock item-19" data-hover-image="meow.png">
        <div class="hover-bg"></div>
        <h2>Port 19132</h2>
        <h4>Bedrock Edition</h4>
        <div class="copy-tooltip">Copied to clipboard</div>
    </div>
    </div>
</main>

<script>
document.body.classList.add('darkMode');

// Copy Java server text on click
document.querySelector('.java').addEventListener('click', function() {
    const h2Text = this.querySelector('h2').textContent;
    const tooltip = this.querySelector('.copy-tooltip');

    navigator.clipboard.writeText(h2Text).then(() => {
        // Show tooltip and add disabled class
        tooltip.classList.add('show');
        this.classList.add('disabled');

        // Hide tooltip and remove disabled class after 2 seconds
        setTimeout(() => {
            tooltip.classList.remove('show');
            this.classList.remove('disabled');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy text: ', err);
    });
});

// Copy Bedrock server text on click
document.querySelector('.bedrock').addEventListener('click', function() {
    const tooltip = this.querySelector('.copy-tooltip');

    navigator.clipboard.writeText("19132").then(() => {
        // Show tooltip and add disabled class
        tooltip.classList.add('show');
        this.classList.add('disabled');

        // Hide tooltip and remove disabled class after 2 seconds
        setTimeout(() => {
            tooltip.classList.remove('show');
            this.classList.remove('disabled');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy text: ', err);
    });
});

// Handle grid item link clicks
document.querySelectorAll('.grid-item[data-link]').forEach(item => {
    item.addEventListener('click', function() {
        const link = this.getAttribute('data-link');
        if (link) {
            // Check if it's an external link (starts with http)
            if (link.startsWith('http')) {
                window.open(link, '_blank');
            } else {
                window.location.href = link;
            }
        }
    });
});

// Handle hover background images with fade effect
document.querySelectorAll('.grid-item[data-hover-image]').forEach(item => {
    const hoverBg = item.querySelector('.hover-bg');
    const hoverImage = item.getAttribute('data-hover-image');

    if (hoverBg && hoverImage) {
        // Set the background image
        hoverBg.style.backgroundImage = `url('${hoverImage}')`;

        // Add hover event listeners
        item.addEventListener('mouseenter', function() {
            hoverBg.classList.add('fade-in');
        });

        item.addEventListener('mouseleave', function() {
            hoverBg.classList.remove('fade-in');
        });
    }
});
</script>
