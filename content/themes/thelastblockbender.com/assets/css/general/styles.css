/* dark mode */
body.darkMode {
background-color:hsla(265,70%,3%,1);
/* background-image:
radial-gradient(at 98% 26%, hsla(272,50%,10%,1) 0px, transparent 50%),
radial-gradient(at 4% 22%, hsla(272,50%,10%,1) 0px, transparent 50%);
*/
background-image: url(/assets/images/gradient.jpg);
background-size: 120% 120%;
background-repeat: no-repeat;
background-position: 50% 50%;
color: var(--white-color);
background-attachment: fixed;
}

.darkMode .gh-head {
background: transparent;
color: var(--white-color);
}

#gh-head {
    z-index: 20;
}

.nav ul.isDropDown {
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

.darkMode a {
color: var(--white-color);	
}

.darkMode h1, .darkMode h2, .darkMode h3, .darkMode h4, .darkMode h5, .darkMode h6 {
color: var(--white-color);
}


.darkMode .kg-callout-card-grey {
    background: #77797e;
}

.darkMode .kg-callout-card-white {
	color: var(--black-color);
}

.darkMode .kg-callout-card-blue {
    background: #8492c3;
    color: var(--black-color);
}

.darkMode .kg-callout-card-green {
    background: #84c3b0;
    color: var(--black-color);
}

.darkMode .kg-callout-card-yellow {
    background: #c3bc84;
    color: var(--black-color);
}

.darkMode .kg-callout-card-red {
    background: #c39184;
    color: var(--black-color);
}

.darkMode .kg-callout-card-pink {
    background: #c38484;
    color: var(--black-color);
}

.darkMode .kg-callout-card-purple {
    background: #9d84c3;
    color: var(--black-color);
}

body.darkMode .gh-foot a:hover {
    color: var(--color-white);
    opacity: 0.6;
}

body.darkMode .gh-foot-menu .nav li+li:before {
  display: none;
}


/* wide template */

body.wideTemplate .gh-canvas {
    --content-width: min(70vw, 1260px);
}
@media (max-width: 1024px) {
	body.wideTemplate .gh-canvas {
    --content-width: 100vw;
}	
}


/* Global Style changes */
* {
	transition: all 0.2s ease;
}

.gh-foot-inner {
  display: block;
  text-align: center;
}

.gh-copyright {
  margin-top: 30px;
}

.kg-toggle-card {
    border-radius: 12px !important;
}

iframe {
    border-radius: 16px;
}

h2 a:after {
content: "🔗";
border: 0px;
margin-left: 10px;
font-size: 18px;
}



/* menu styles */
.gh-head .nav 
{
  gap: 0px !important;
}

.nav > li {
  transition: all 0.3s ease;
    border-radius: 60px;
    padding: 12px 24px;
    border: 1px solid rgba(0,0,0,0);
}

.nav > li:hover {
   
    border: 1px solid #38314d;
  background: rgba(255, 255, 255, 0.03);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.nav ul.isDropDown {
    left: 0px;
    top: 120%;
    padding: 12px 24px;
    border-radius: 16px;
    border: 1px solid rgba(0,0,0,0);
  background: rgba(255, 255, 255, 0.03);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
}

.nav ul.isDropDown > li {
 margin-bottom: 20px !important;   
}

.nav ul.isDropDown > li:hover :before {
    content: "→";
    margin-right: 10px;
}
    
    
.nav ul.isDropDown li:last-of-type  {
    margin-bottom: 5px !important;
}

@media(max-width:768px) {
.gh-burger:after, .gh-burger:before 
{
    background-color: #fff;
}

.is-head-open #gh-head {
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
}

.nav {
   margin-left: 0px !important; 
}
.gh-head-actions {
  background: transparent !important;
}
}




/* glass elements */

.glass-button {
  position: relative;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px !important;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none !important;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden !important;
  display: inline-block;
  min-height: 10px;
}

.glass-button::before {
  opacity: 0;
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.glass-button:hover::before {
  opacity: 1;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.07);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}







.kg-button-card a {
  position: relative;
  padding: 32px 32px !important;
  background: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px !important;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none !important;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden !important;
  display: inline-block;
  min-height: 10px;
}
.kg-button-card a:after {
    content: "⟶";
    margin-left: 30px;
    font-size: 32px;
    opacity: .6;
    margin-top: -10px;
  transition: all 0.3s ease !important;
}

.kg-button-card a::before {
  opacity: 0;
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease !important;
}

.kg-button-card a:hover::before {
  opacity: 1;
}

.kg-button-card a:hover {
  background: rgba(255, 255, 255, 0.07);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.kg-button-card a:hover::before {
  left: 100%;
}

.kg-button-card a:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}



/* Additional demo buttons */
.button-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  padding: 500px 0;
}

.glass-button.secondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.glass-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
}




/* Owl Slider Styles */

  .owl-carousel {
    margin-top: 0px !important;
  }
  .owl-item h4 {
      font-size: 2vw;
      text-align: center;
      margin-bottom: 30px;
  }
    .owl-item .item { 
  position: relative;
  padding: 16px 32px 26px 32px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px !important;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none !important;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden !important;
  display: inline-block;
  min-height: 10px;
  margin: 0px 20px !important;
}
.owl-item .item:before {
  opacity: 0;
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.owl-item .item:hover {
  background: rgba(255, 255, 255, 0.01);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


.owl-next span, .owl-prev span {
width: 100%;
display: block;
}

.owl-next {
margin-left: 10px;
    font-size: 65px !important;

}

.owl-prev {
  margin-right: 10px;
    font-size: 65px !important;
}

.owl-nav {
    position: absolute;
    top: 100%;
    right: 0px;
}

/*(1) owl-stage box*/
.owl-carousel .owl-stage { 
 display: flex;
}

/*(2) the direct div of each element in the owl carousel*/
.article-items { 
height: 100%;
}
.owl-item {
  margin-right: 12px !important;
}

.owl-item .item {
    display: flex;
    height: 100%;
}

.owl-item .item div {
  align-self: center;
}

.owl-stage-outer {
    mask-image: linear-gradient(90deg,rgba(0, 0, 0, 0) 0%, rgba(255, 255, 255, 1) 20%, rgba(255, 255, 255, 1) 80%, rgba(0, 0, 0, 0) 100%);;
}

@media (max-width:768px) {
  .owl-item h4 {
    font-size: 26px;
  } 
}


/* Three Box */

.three-box {
  margin: 50px 0px;
}

  .darkMode .three-box span {
  color: #000 !important; 
  }
  .three-box {
    grid-column: wide-start/wide-end;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
    align-items: start;
  }
  @media screen and (max-width:767px) {
    .three-box {
       grid-template-columns: 1fr; }
  }
  
  .three-box .kg-product-card-container {
    height: 100%;
  }

  


.three-box .kg-product-card-container  {
  position: relative;
  padding: 32px 32px;
  background: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px !important;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none !important;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden !important;
  display: inline-block;
  min-height: 10px;
}

.three-box .kg-product-card-container img {
    border-radius: 12px;
}

.darkMode .three-box span {
    color: #fff !important;
}

.three-box .kg-product-card-title {
margin: 32px 0px !important;
    font-size: 28px !important;
}
