{{!< default}}

{{#post}}

<main class="site-main wiki-page">
    {{#match @page.show_title_and_feature_image}}
        <header class="gh-article-header gh-canvas">
            <h1 class="gh-article-title">{{title}}</h1>
            {{> "post-image"}}
        </header>
    {{/match}}

    <div class="wiki-page-layout gh-canvas">
        <!-- Left Column: Page Content -->
        <div class="wiki-page-content">
            <article class="{{post_class}}">
                <div class="post-content gh-content">
                    {{content}}
                </div>
            </article>
        </div>

        <!-- Right Column: Related Posts -->
        <aside class="wiki-page-sidebar">
            <div class="related-posts-section">
                <h3 class="related-posts-title">Related Posts</h3>

                {{#get "posts" filter="tag:{{primary_tag.slug}}+id:-{{id}}" limit="10"}}
                    {{#if posts}}
                        <div class="related-posts-list">
                            {{#foreach posts}}
                                <article class="related-post-card">
                                    <a href="{{url}}" class="related-post-link">
                                        {{#if feature_image}}
                                            <div class="related-post-image">
                                                <img src="{{img_url feature_image size="s"}}" alt="{{title}}" loading="lazy">
                                            </div>
                                        {{/if}}
                                        <div class="related-post-content glass-button">
                                            <h4 class="related-post-title">{{title}}</h4>
                                            {{#if excerpt}}
                                                <p class="related-post-excerpt">{{excerpt words="15"}}</p>
                                            {{/if}}
                                           
                                        </div>
                                    </a>
                                </article>
                            {{/foreach}}
                        </div>
                    {{else}}
                        <p class="no-related-posts">No related posts found.</p>
                    {{/if}}
                {{/get}}
            </div>
        </aside>
    </div>

</main>

<style>
    .wiki-page-layout {
    display: block !important;
    max-width: 1280px;
    padding: 40px;
    margin: 0 auto;
}

.wiki-page .wiki-page-content {
    width: 65%;
    float: left;
    
}

.wiki-page .wiki-page-sidebar {
    width: 35%;
    padding-left: 60px;
    float: left;
    padding-top: 40px;
}
.wiki-page .wiki-page-sidebar h3 {
font-size: 20px;
    margin-bottom: 40px;
}

.wiki-page .related-post-title {
    font-size: 20px;
    margin-bottom: 10px;
}

.related-post-excerpt {
    font-size: 14px !important;
    line-height: 18px;
}

.related-post-content {
    margin-bottom: 20px;
}

@media(max-width: 1024px){
.wiki-page .wiki-page-sidebar {
    width: 100%;
    float: none;
    padding-left: 0px;
    margin-top: 60px;
}

.wiki-page .wiki-page-content {
    width: 100%;
    float: none;
} 
}
</style>

{{/post}}